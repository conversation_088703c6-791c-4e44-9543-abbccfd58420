#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR DES TRANSITIONS ENTRE INDEX - BACCARAT SYSTEM
========================================================

Module d'analyse des transitions entre les différents INDEX (INDEX5, INDEX3, INDEX7)
pour chaque position de main (1 à 59) dans le système baccarat.

Auteur: Système d'analyse baccarat INDEX
Date: 2025-07-05
"""

import json
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Any
import sys
from datetime import datetime

class AnalyseurTransitionsIndex:
    """Analyseur des transitions entre INDEX pour chaque position de main"""
    
    def __init__(self):
        """Initialisation de l'analyseur"""
        self.donnees = None
        
        # Mappings INDEX
        self.index6_mapping = {
            '0_A': 'M', '0_B': 'N', '0_C': 'O',
            '1_A': 'S', '1_B': 'T', '1_C': 'U'
        }
        
        # INDEX5 : toutes les combinaisons INDEX1_INDEX2_INDEX3
        self.index5_values = [
            '0_A_BANKER', '0_B_BANKER', '0_C_BANKER', '1_A_BANKER', '1_B_BANKER', '1_C_BANKER',
            '0_A_PLAYER', '0_B_PLAYER', '0_C_PLAYER', '1_A_PLAYER', '1_B_PLAYER', '1_C_PLAYER',
            '0_A_TIE', '0_B_TIE', '0_C_TIE', '1_A_TIE', '1_B_TIE', '1_C_TIE'
        ]
        
        # INDEX7 : toutes les combinaisons INDEX6_INDEX3
        self.index7_values = [
            'M_BANKER', 'N_BANKER', 'O_BANKER', 'S_BANKER', 'T_BANKER', 'U_BANKER',
            'M_PLAYER', 'N_PLAYER', 'O_PLAYER', 'S_PLAYER', 'T_PLAYER', 'U_PLAYER',
            'M_TIE', 'N_TIE', 'O_TIE', 'S_TIE', 'T_TIE', 'U_TIE'
        ]
        
        # Résultats des analyses
        self.transitions_index5 = {}  # main_n -> {index5_n -> {index5_n+1 -> count}}
        self.transitions_index1_index2 = {}  # main_n -> {index1_index2_n -> {index5_n+1, index3_n+1, index7_n+1 -> count}}
        
    def charger_donnees(self, donnees: Dict) -> bool:
        """Charge les données depuis un dictionnaire"""
        try:
            self.donnees = donnees
            print(f"✅ Données chargées pour l'analyse des transitions INDEX")
            return True
        except Exception as e:
            print(f"❌ Erreur lors du chargement des données : {e}")
            return False
    
    def construire_index5(self, main: Dict) -> str:
        """Construit la valeur INDEX5 à partir d'une main"""
        if not all(key in main and main[key] is not None and main[key] != "" 
                  for key in ['index1', 'index2', 'index3']):
            return None
        return f"{main['index1']}_{main['index2']}_{main['index3']}"
    
    def construire_index6(self, main: Dict) -> str:
        """Construit la valeur INDEX6 à partir d'une main"""
        if not all(key in main and main[key] is not None and main[key] != "" 
                  for key in ['index1', 'index2']):
            return None
        index1_index2 = f"{main['index1']}_{main['index2']}"
        return self.index6_mapping.get(index1_index2)
    
    def construire_index7(self, main: Dict) -> str:
        """Construit la valeur INDEX7 à partir d'une main"""
        index6 = self.construire_index6(main)
        if index6 is None or not main.get('index3'):
            return None
        return f"{index6}_{main['index3']}"
    
    def construire_index1_index2(self, main: Dict) -> str:
        """Construit la valeur INDEX1_INDEX2 à partir d'une main"""
        if not all(key in main and main[key] is not None and main[key] != "" 
                  for key in ['index1', 'index2']):
            return None
        return f"{main['index1']}_{main['index2']}"
    
    def analyser_transitions_par_position(self) -> Dict[str, Any]:
        """Analyse les transitions pour chaque position de main (1 à 59)"""
        print("\n🔍 ANALYSE DES TRANSITIONS INDEX PAR POSITION")
        print("=" * 80)

        if not self.donnees or 'parties_condensees' not in self.donnees:
            print("❌ Aucune donnée disponible")
            return {}

        # Initialiser les structures de données
        self.transitions_index5 = {}
        self.transitions_index1_index2 = {}

        for position in range(1, 60):  # Positions 1 à 59
            self.transitions_index5[position] = defaultdict(lambda: defaultdict(int))
            self.transitions_index1_index2[position] = defaultdict(lambda: {
                'index5': defaultdict(int),
                'index3': defaultdict(int),
                'index7': defaultdict(int)
            })

        total_parties = len(self.donnees['parties_condensees'])
        parties_traitees = 0

        print(f"📊 Analyse de {total_parties:,} parties...")

        # NOUVELLE LOGIQUE : Analyser par position parallèle
        # Pour chaque position N (1 à 59), analyser toutes les mains à cette position
        # et voir ce qui arrive à la position N+1

        transitions_detectees = 0
        parties_avec_mains = 0

        for partie in self.donnees['parties_condensees']:
            if 'mains_condensees' not in partie:
                continue

            parties_avec_mains += 1

            mains = partie['mains_condensees']

            # Créer un dictionnaire des mains par position (sans filtrer les dummy)
            mains_par_position = {}
            for main in mains:
                position = main.get('main_number')
                if position is not None:
                    mains_par_position[position] = main

            # Analyser les transitions position N -> position N+1
            for position in range(1, 60):  # Positions 1 à 59
                if position not in mains_par_position or (position + 1) not in mains_par_position:
                    continue

                main_n = mains_par_position[position]
                main_n_plus_1 = mains_par_position[position + 1]

                # Vérifier que les mains ne sont pas dummy (main_number null ou index vides)
                if (main_n.get('main_number') is None or
                    main_n.get('index1') == "" or main_n.get('index2') == "" or main_n.get('index3') == "" or
                    main_n_plus_1.get('main_number') is None or
                    main_n_plus_1.get('index1') == "" or main_n_plus_1.get('index2') == "" or main_n_plus_1.get('index3') == ""):
                    continue

                # Utiliser les INDEX déjà calculés dans le dataset
                index5_n = main_n.get('index5')
                index1_index2_n = f"{main_n.get('index1')}_{main_n.get('index2')}"

                # INDEX pour la main N+1
                index5_n_plus_1 = main_n_plus_1.get('index5')
                index3_n_plus_1 = main_n_plus_1.get('index3')
                index7_n_plus_1 = main_n_plus_1.get('index7')

                if not all([index5_n, index1_index2_n, index5_n_plus_1, index3_n_plus_1, index7_n_plus_1]):
                    continue

                # 1. Transitions INDEX5 position N -> INDEX5 position N+1
                self.transitions_index5[position][index5_n][index5_n_plus_1] += 1

                # 2. Transitions INDEX1_INDEX2 position N -> INDEX5/INDEX3/INDEX7 position N+1
                self.transitions_index1_index2[position][index1_index2_n]['index5'][index5_n_plus_1] += 1
                self.transitions_index1_index2[position][index1_index2_n]['index3'][index3_n_plus_1] += 1
                self.transitions_index1_index2[position][index1_index2_n]['index7'][index7_n_plus_1] += 1

                transitions_detectees += 1

            parties_traitees += 1
            if parties_traitees % 10000 == 0:
                print(f"   📈 Progression : {parties_traitees:,}/{total_parties:,} parties ({(parties_traitees/total_parties)*100:.1f}%)")

        print(f"✅ Analyse terminée : {parties_traitees:,} parties traitées")
        print(f"📊 Parties avec mains : {parties_avec_mains:,}")
        print(f"📊 Transitions détectées : {transitions_detectees:,}")

        # Debug : Afficher quelques exemples de données
        if parties_avec_mains > 0 and transitions_detectees == 0:
            print("🔍 DEBUG - Examen d'une partie pour diagnostic...")
            for i, partie in enumerate(self.donnees['parties_condensees'][:1]):
                if 'mains_condensees' in partie:
                    print(f"   Partie {i+1} : {len(partie['mains_condensees'])} mains")
                    for j, main in enumerate(partie['mains_condensees'][:5]):
                        print(f"      Main {j+1}: position={main.get('main_number')}, index1={main.get('index1')}, index2={main.get('index2')}, index3={main.get('index3')}, index5={main.get('index5')}")
                    break

        return {
            'transitions_index5': dict(self.transitions_index5),
            'transitions_index1_index2': dict(self.transitions_index1_index2),
            'total_parties': parties_traitees
        }
    
    def generer_rapport_transitions(self, nom_fichier: str, resultats: Dict[str, Any]) -> str:
        """Génère un rapport détaillé des transitions INDEX"""
        print(f"📄 Génération du rapport des transitions INDEX...")
        
        rapport_content = []
        rapport_content.append("=" * 120)
        rapport_content.append("🔍 ANALYSE DES TRANSITIONS INDEX PAR POSITION DE MAIN")
        rapport_content.append("=" * 120)
        rapport_content.append(f"📅 Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        rapport_content.append(f"📊 Total parties analysées : {resultats.get('total_parties', 0):,}")
        rapport_content.append("=" * 120)
        rapport_content.append("")
        
        transitions_index5 = resultats.get('transitions_index5', {})
        transitions_index1_index2 = resultats.get('transitions_index1_index2', {})
        
        # Section 1: Transitions INDEX5 -> INDEX5
        rapport_content.append("1. TRANSITIONS INDEX5 -> INDEX5 PAR POSITION")
        rapport_content.append("-" * 80)
        rapport_content.append("")
        
        for position in range(1, 60):
            if position not in transitions_index5:
                continue
                
            position_data = transitions_index5[position]
            if not position_data:
                continue
            
            rapport_content.append(f"📍 POSITION {position} :")
            rapport_content.append("")
            
            # Calculer les totaux pour les pourcentages
            for index5_source in sorted(position_data.keys()):
                transitions = position_data[index5_source]
                total_transitions = sum(transitions.values())
                
                if total_transitions == 0:
                    continue
                
                rapport_content.append(f"   {index5_source} -> (Total: {total_transitions:,})")
                
                # Trier par fréquence décroissante
                for index5_dest, count in sorted(transitions.items(), key=lambda x: x[1], reverse=True):
                    pourcentage = (count / total_transitions) * 100
                    rapport_content.append(f"      {index5_dest}: {count:,} ({pourcentage:.4f}%)")
                
                rapport_content.append("")
            
            rapport_content.append("-" * 40)
            rapport_content.append("")
        
        # Section 2: Transitions INDEX1_INDEX2 -> INDEX5/INDEX3/INDEX7
        rapport_content.append("2. TRANSITIONS INDEX1_INDEX2 -> INDEX5/INDEX3/INDEX7 PAR POSITION")
        rapport_content.append("-" * 80)
        rapport_content.append("")

        for position in range(1, 60):
            if position not in transitions_index1_index2:
                continue

            position_data = transitions_index1_index2[position]
            if not position_data:
                continue

            rapport_content.append(f"📍 POSITION {position} :")
            rapport_content.append("")

            # Pour chaque combinaison INDEX1_INDEX2
            for index1_index2 in ['0_A', '0_B', '0_C', '1_A', '1_B', '1_C']:
                if index1_index2 not in position_data:
                    continue

                data = position_data[index1_index2]

                # INDEX5 transitions
                index5_transitions = data.get('index5', {})
                total_index5 = sum(index5_transitions.values())

                if total_index5 > 0:
                    rapport_content.append(f"   {index1_index2} -> INDEX5 (Total: {total_index5:,})")
                    for index5, count in sorted(index5_transitions.items(), key=lambda x: x[1], reverse=True)[:5]:  # Top 5
                        pourcentage = (count / total_index5) * 100
                        rapport_content.append(f"      {index5}: {count:,} ({pourcentage:.4f}%)")
                    rapport_content.append("")

                # INDEX3 transitions
                index3_transitions = data.get('index3', {})
                total_index3 = sum(index3_transitions.values())

                if total_index3 > 0:
                    rapport_content.append(f"   {index1_index2} -> INDEX3 (Total: {total_index3:,})")
                    for index3, count in sorted(index3_transitions.items(), key=lambda x: x[1], reverse=True):
                        pourcentage = (count / total_index3) * 100
                        rapport_content.append(f"      {index3}: {count:,} ({pourcentage:.4f}%)")
                    rapport_content.append("")

                # INDEX7 transitions
                index7_transitions = data.get('index7', {})
                total_index7 = sum(index7_transitions.values())

                if total_index7 > 0:
                    rapport_content.append(f"   {index1_index2} -> INDEX7 (Total: {total_index7:,})")
                    for index7, count in sorted(index7_transitions.items(), key=lambda x: x[1], reverse=True)[:5]:  # Top 5
                        pourcentage = (count / total_index7) * 100
                        rapport_content.append(f"      {index7}: {count:,} ({pourcentage:.4f}%)")
                    rapport_content.append("")

            rapport_content.append("-" * 40)
            rapport_content.append("")

        # Section 3: Résumé statistique
        rapport_content.append("3. RÉSUMÉ STATISTIQUE GLOBAL")
        rapport_content.append("-" * 80)
        rapport_content.append("")

        # Calculer les statistiques globales
        total_transitions_index5 = 0
        total_transitions_index1_index2 = 0

        for position_data in transitions_index5.values():
            for source_data in position_data.values():
                total_transitions_index5 += sum(source_data.values())

        for position_data in transitions_index1_index2.values():
            for source_data in position_data.values():
                total_transitions_index1_index2 += sum(source_data.get('index5', {}).values())

        rapport_content.append(f"📊 Total transitions INDEX5 -> INDEX5 : {total_transitions_index5:,}")
        rapport_content.append(f"📊 Total transitions INDEX1_INDEX2 -> * : {total_transitions_index1_index2:,}")
        rapport_content.append(f"📊 Positions analysées : 1 à 59 (59 positions)")
        rapport_content.append(f"📊 Combinaisons INDEX5 possibles : {len(self.index5_values)}")
        rapport_content.append(f"📊 Combinaisons INDEX7 possibles : {len(self.index7_values)}")
        rapport_content.append("")

        rapport_content.append("=" * 120)
        rapport_content.append("FIN DU RAPPORT DES TRANSITIONS INDEX")
        rapport_content.append("=" * 120)

        return "\n".join(rapport_content)

def integrer_analyse_transitions(donnees: Dict, nom_rapport_principal: str) -> str:
    """Fonction d'intégration pour les autres analyseurs"""
    print("\n🔗 INTÉGRATION DE L'ANALYSE DES TRANSITIONS INDEX")
    print("=" * 80)
    
    # Créer l'analyseur
    analyseur = AnalyseurTransitionsIndex()
    
    # Charger les données
    if not analyseur.charger_donnees(donnees):
        return ""
    
    # Effectuer l'analyse
    resultats = analyseur.analyser_transitions_par_position()
    
    # Générer le contenu du rapport
    nom_rapport_transitions = nom_rapport_principal.replace('.txt', '_transitions_index.txt')
    contenu_rapport = analyseur.generer_rapport_transitions(nom_rapport_transitions, resultats)
    
    # Sauvegarder le rapport séparé
    try:
        with open(nom_rapport_transitions, 'w', encoding='utf-8') as f:
            f.write(contenu_rapport)
        print(f"✅ Rapport des transitions INDEX généré : {nom_rapport_transitions}")
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde : {e}")
    
    # Retourner un résumé pour intégration dans le rapport principal
    resume = f"""

{"-" * 80}
🔍 ANALYSE DES TRANSITIONS INDEX - RÉSUMÉ
{"-" * 80}
📊 Total parties analysées : {resultats.get('total_parties', 0):,}
📄 Rapport détaillé généré : {nom_rapport_transitions}

Cette analyse examine les transitions entre les différents INDEX (INDEX5, INDEX3, INDEX7)
pour chaque position de main (1 à 59) dans le système baccarat.

📈 MÉTRIQUES CALCULÉES :
• Transitions INDEX5 -> INDEX5 par position
• Transitions INDEX1_INDEX2 -> INDEX5/INDEX3/INDEX7 par position
• Proportions et pourcentages pour chaque transition
• Analyse complète des 18 valeurs d'INDEX5
• Analyse complète des 18 valeurs d'INDEX7

💡 UTILISATION :
Consultez le rapport détaillé '{nom_rapport_transitions}' pour l'analyse complète
des patterns de transition par position de main.
{"-" * 80}
"""
    
    return resume

if __name__ == "__main__":
    print("🔍 ANALYSEUR DES TRANSITIONS INDEX - MODE STANDALONE")
    print("=" * 80)
    print("Ce module est conçu pour être intégré aux autres analyseurs.")
    print("Utilisez analyseur_statistique_avance.py ou analyseur_biais_systematique.py")
    print("pour une analyse complète incluant les transitions INDEX.")
