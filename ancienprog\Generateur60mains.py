#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GÉNÉRATEUR DE PARTIES BACCARAT LUPASCO RÉALISTE
===============================================

Génère des parties de baccarat réalistes avec :
- Sabot de 416 cartes (8 decks de 52 cartes)
- Règles strictes du baccarat selon regles_baccarat_essentielles.txt
- Simulation complète des tirages de cartes
- Calcul automatique des INDEX Lupasco
- Export CSV pour analyse

Basé sur les règles officielles du baccarat, sans statistiques prédéfinies.
"""

import secrets  # CSPRNG cryptographiquement sécurisé
import os       # Entropie système
import hashlib  # Combinaison d'entropie
import time     # Timing haute précision
import json     # Pour export JSON
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

# Imports pour l'analyse scientifique révolutionnaire
import numpy as np
import scipy.stats as stats
import scipy.signal as signal
from scipy.fft import fft, fftfreq
from scipy.special import gamma, beta, erf, erfc
from scipy.optimize import minimize
from scipy.linalg import toeplitz
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mutual_info_score
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# ============================================================================
# CONFIGURATION DU SABOT BACCARAT
# ============================================================================

# Sabot de 8 decks standard
DECKS_COUNT = 8
CARDS_PER_DECK = 52
TOTAL_CARDS = DECKS_COUNT * CARDS_PER_DECK  # 416 cartes

# Cut card à 80% du sabot
CUT_CARD_POSITION = int(TOTAL_CARDS * 0.8)  # 332ème carte

# Valeurs des cartes pour le calcul des scores
CARD_VALUES = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 0, 'J': 0, 'Q': 0, 'K': 0
}

# Cartes pour le brûlage (valeur → nombre de cartes supplémentaires)
BURN_CARDS_COUNT = {
    'A': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    '10': 10, 'J': 10, 'Q': 10, 'K': 10
}

# Mapping nombre de cartes → catégories INDEX2
CARDS_MAPPING = {
    4: 'A',  # pair_4 = A
    5: 'C',  # impair_5 = C
    6: 'B'   # pair_6 = B
}

# NOUVEAU : Mapping INDEX1+INDEX2 → INDEX6
INDEX6_MAPPING = {
    '0_A': 'M',
    '0_B': 'N', 
    '0_C': 'O',
    '1_A': 'S',
    '1_B': 'T',
    '1_C': 'U'
}

# ============================================================================
# STRUCTURES DE DONNÉES
# ============================================================================

@dataclass
class Carte:
    """Représente une carte du sabot"""
    rang: str      # 'A', '2', '3', ..., '10', 'J', 'Q', 'K'
    couleur: str   # '♠', '♥', '♦', '♣'
    valeur: int    # Valeur pour le calcul du score (0-9)

@dataclass
class MainBaccarat:
    """Structure d'une main de baccarat avec tous les INDEX Lupasco"""

    # Identification
    main_number: int
    manche_pb_number: Optional[int]  # None pour brûlage, même numéro pour TIE

    # Cartes distribuées
    cartes_player: List[Carte]
    cartes_banker: List[Carte]
    total_cartes_distribuees: int

    # Scores calculés
    score_player: int
    score_banker: int

    # INDEX Lupasco
    index1: int                      # 0 (SYNC) ou 1 (DESYNC)
    cards_count: int                 # 4, 5, ou 6 (ou nombre pour brûlage)
    index2: str                      # 'A', 'C', 'B' (ou '' pour brûlage)
    index3: str                      # 'PLAYER', 'BANKER', 'TIE' (ou '' pour brûlage)
    index5: str                      # INDEX1_INDEX2_INDEX3 (ex: '0_A_PLAYER')
    index6: str                      # INDEX6 (M, N, O, S, T, U ou '' pour brûlage)
    index7: str                      # NOUVEAU : INDEX7 (INDEX6_INDEX3, ex: 'M_BANKER')

    # Métadonnées
    timestamp: str = ""

@dataclass
class PartieBaccarat:
    """Structure d'une partie complète de baccarat"""

    # Identification
    partie_number: int

    # Sabot
    sabot_initial: List[Carte]
    cartes_restantes: int

    # Brûlage initial
    cartes_brulees: List[Carte]
    burn_cards_count: int
    burn_parity: str                 # 'PAIR' ou 'IMPAIR'
    initial_sync_state: int          # 0 (SYNC) ou 1 (DESYNC)

    # Mains de la partie
    mains: List[MainBaccarat]

    # Paramètres avec valeurs par défaut
    cut_card_atteinte: bool = False
    total_mains: int = 0
    total_manches_pb: int = 0
    total_ties: int = 0
    current_sync_state: int = 0  # SYNC
    premiere_carte_brulee: Optional[Carte] = None

# ============================================================================
# GÉNÉRATEUR DE HASARD CRYPTOGRAPHIQUEMENT SÉCURISÉ
# ============================================================================

class SecureRandomGenerator:
    """
    Générateur de hasard cryptographiquement sécurisé

    Combine plusieurs sources d'entropie pour un hasard optimal :
    - CSPRNG système (secrets)
    - Entropie hardware (os.urandom)
    - Timing haute précision
    - Adresses mémoire
    """

    def __init__(self, seed: Optional[int] = None):
        """Initialise le générateur sécurisé"""
        # Collecter entropie de multiples sources
        entropy_sources = [
            secrets.token_bytes(32),      # CSPRNG système
            os.urandom(32),               # Entropie hardware
            str(time.time_ns()).encode(), # Timing haute précision
            str(id(self)).encode(),       # Adresse mémoire
        ]

        # Si seed fourni, l'ajouter aux sources
        if seed is not None:
            entropy_sources.append(str(seed).encode())

        # Combiner toutes les sources avec SHA-256
        combined = b''.join(entropy_sources)
        self.entropy_pool = hashlib.sha256(combined).digest()

        print(f"Generateur securise initialise avec {len(entropy_sources)} sources d'entropie")

    def secure_uniform_int(self, max_val: int) -> int:
        """
        Génère un entier uniforme sans biais de modulo (rejection sampling)

        Utilise la méthode de rejection sampling pour éliminer complètement
        le biais de modulo et garantir une distribution parfaitement uniforme.

        Args:
            max_val: Valeur maximale (exclusive) - doit être > 0

        Returns:
            int: Entier uniforme dans [0, max_val-1]

        Raises:
            ValueError: Si max_val <= 0
        """
        if max_val <= 0:
            raise ValueError("max_val doit être strictement positif")

        # Cas trivial
        if max_val == 1:
            return 0

        # Calculer le nombre de bytes nécessaires
        range_size = max_val
        num_bytes = (range_size.bit_length() + 7) // 8

        # Rejection sampling pour éliminer le biais
        while True:
            random_bytes = secrets.token_bytes(num_bytes)
            random_int = int.from_bytes(random_bytes, 'big')

            # Calculer la limite valide pour éviter le biais
            max_valid = (2**(num_bytes * 8) // range_size) * range_size

            if random_int < max_valid:
                return random_int % range_size

    def secure_shuffle(self, deck: List) -> List:
        """
        Mélange cryptographiquement sécurisé (Fisher-Yates) SANS BIAIS

        Utilise la méthode de rejection sampling pour garantir une distribution
        parfaitement uniforme des cartes, éliminant tout biais statistique.

        Args:
            deck: Liste à mélanger

        Returns:
            List: Liste mélangée de façon cryptographiquement sécurisée et sans biais
        """
        deck_copy = deck.copy()

        for i in range(len(deck_copy) - 1, 0, -1):
            # Générer index aléatoire SANS BIAIS avec rejection sampling
            j = self.secure_uniform_int(i + 1)

            # Échanger les éléments
            deck_copy[i], deck_copy[j] = deck_copy[j], deck_copy[i]

        return deck_copy

    def refresh_entropy(self):
        """Rafraîchit le pool d'entropie"""
        new_entropy = [
            secrets.token_bytes(16),
            str(time.time_ns()).encode(),
            os.urandom(16)
        ]
        combined = self.entropy_pool + b''.join(new_entropy)
        self.entropy_pool = hashlib.sha256(combined).digest()

# ============================================================================
# GÉNÉRATEUR DE PARTIES RÉALISTE
# ============================================================================

class GenerateurPartiesBaccarat:
    """
    Générateur de parties de baccarat réalistes avec hasard cryptographiquement sécurisé

    Simule un vrai sabot de 416 cartes avec les règles strictes du baccarat.
    Utilise un générateur de hasard sécurisé pour un mélange optimal.
    Aucune statistique prédéfinie - tout est calculé selon les tirages réels.
    """

    def __init__(self, seed: Optional[int] = None):
        """
        Initialise le générateur

        Args:
            seed: Graine pour la reproductibilité (optionnel)
        """
        # Initialiser le générateur sécurisé
        self.secure_rng = SecureRandomGenerator(seed)

        self.parties_generees = []

    def _creer_sabot_complet(self) -> List[Carte]:
        """
        Crée un sabot complet de 416 cartes (8 decks mélangés cryptographiquement)

        Returns:
            List[Carte]: Sabot mélangé de façon cryptographiquement sécurisée
        """
        sabot = []
        couleurs = ['♠', '♥', '♦', '♣']
        rangs = ['A', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K']

        # Créer 8 decks complets (416 cartes exactement)
        for deck in range(DECKS_COUNT):
            for couleur in couleurs:
                for rang in rangs:
                    carte = Carte(
                        rang=rang,
                        couleur=couleur,
                        valeur=CARD_VALUES[rang]
                    )
                    sabot.append(carte)

        print(f"Sabot cree : {len(sabot)} cartes")

        # Rafraîchir l'entropie avant mélange
        self.secure_rng.refresh_entropy()

        # Mélanger le sabot avec algorithme cryptographiquement sécurisé
        sabot_melange = self.secure_rng.secure_shuffle(sabot)

        print(f"Melange cryptographiquement securise effectue")

        return sabot_melange
    
    def _effectuer_brulage(self, sabot: List[Carte]) -> Tuple[List[Carte], int, str, str]:
        """
        Effectue le brûlage selon les règles officielles du baccarat

        Args:
            sabot: Sabot complet de cartes

        Returns:
            Tuple[cartes_brulees, cards_count, parity, sync_state]
        """
        if len(sabot) == 0:
            raise ValueError("Sabot vide pour le brûlage")

        # Tirer la première carte pour déterminer le nombre de cartes à brûler
        premiere_carte = sabot.pop(0)
        cartes_brulees = [premiere_carte]

        print(f"🔥 Première carte brûlée : {premiere_carte.rang}{premiere_carte.couleur}")

        # Nombre de cartes supplémentaires à brûler selon les règles
        cartes_supplementaires = BURN_CARDS_COUNT[premiere_carte.rang]

        # Calculer le total attendu
        total_attendu = 1 + cartes_supplementaires  # 1 carte tirée + cartes supplémentaires

        # Brûler les cartes supplémentaires
        for i in range(cartes_supplementaires):
            if len(sabot) > 0:
                carte_brulee = sabot.pop(0)
                cartes_brulees.append(carte_brulee)

        # Total de cartes brûlées (vérification)
        total_brulees = len(cartes_brulees)

        # Parité du total brûlé
        burn_parity = 'PAIR' if total_brulees % 2 == 0 else 'IMPAIR'

        # État SYNC initial selon la parité (RÈGLE CORRECTE)
        # Si total brûlé PAIR → première manche commence en SYNC (0)
        # Si total brûlé IMPAIR → première manche commence en DESYNC (1)
        initial_sync = 0 if burn_parity == 'PAIR' else 1

        print(f"🔥 Parité brûlage : {burn_parity} → Première manche : {initial_sync}")

        return cartes_brulees, total_brulees, burn_parity, initial_sync
    
    def _calculer_score(self, cartes: List[Carte]) -> int:
        """
        Calcule le score d'une main selon les règles du baccarat

        Args:
            cartes: Liste des cartes de la main

        Returns:
            int: Score de la main (0-9)
        """
        total = sum(carte.valeur for carte in cartes)
        return total % 10

    def _doit_tirer_player(self, score_player: int) -> bool:
        """
        Détermine si le joueur doit tirer une 3ème carte

        Args:
            score_player: Score actuel du joueur

        Returns:
            bool: True si le joueur doit tirer
        """
        if score_player in [8, 9]:  # Naturel
            return False
        elif score_player in [0, 1, 2, 3, 4, 5]:  # Tire
            return True
        else:  # 6, 7 - Reste
            return False

    def _doit_tirer_banker(self, score_banker: int,
                          troisieme_carte_player: Optional[Carte]) -> bool:
        """
        Détermine si le banquier doit tirer une 3ème carte

        Args:
            score_banker: Score actuel du banquier
            troisieme_carte_player: 3ème carte du joueur (None si pas tirée)

        Returns:
            bool: True si le banquier doit tirer
        """
        if score_banker in [8, 9]:  # Naturel
            return False

        if troisieme_carte_player is None:  # Joueur n'a pas tiré
            if score_banker in [0, 1, 2, 3, 4, 5]:
                return True
            else:  # 6, 7
                return False
        else:  # Joueur a tiré une 3ème carte
            valeur_3eme = troisieme_carte_player.valeur

            if score_banker == 3:
                return valeur_3eme != 8
            elif score_banker == 4:
                return valeur_3eme in [2, 3, 4, 5, 6, 7]
            elif score_banker == 5:
                return valeur_3eme in [4, 5, 6, 7]
            elif score_banker == 6:
                return valeur_3eme in [6, 7]
            elif score_banker == 7:
                return False
            else:  # 0, 1, 2
                return True
    
    def _jouer_manche(self, sabot: List[Carte]) -> Tuple[List[Carte], List[Carte], int, str]:
        """
        Joue une manche complète de baccarat selon les règles officielles

        Args:
            sabot: Sabot de cartes (modifié en place)

        Returns:
            Tuple[cartes_player, cartes_banker, total_cartes, resultat]
        """
        if len(sabot) < 6:  # Sécurité - minimum 6 cartes pour une manche complète
            raise ValueError("Pas assez de cartes dans le sabot")

        # Distribution initiale : 2 cartes chacun
        cartes_player = [sabot.pop(0), sabot.pop(0)]
        cartes_banker = [sabot.pop(0), sabot.pop(0)]

        # Calcul des scores initiaux
        score_player = self._calculer_score(cartes_player)
        score_banker = self._calculer_score(cartes_banker)

        # Vérifier les naturels (8 ou 9)
        if score_player in [8, 9] or score_banker in [8, 9]:
            # Naturel - pas de 3ème carte
            pass
        else:
            # Règles de tirage
            troisieme_carte_player = None

            # Le joueur tire-t-il ?
            if self._doit_tirer_player(score_player):
                troisieme_carte_player = sabot.pop(0)
                cartes_player.append(troisieme_carte_player)
                score_player = self._calculer_score(cartes_player)

            # Le banquier tire-t-il ?
            if self._doit_tirer_banker(score_banker, troisieme_carte_player):
                cartes_banker.append(sabot.pop(0))
                score_banker = self._calculer_score(cartes_banker)

        # Déterminer le résultat
        if score_player > score_banker:
            resultat = 'PLAYER'
        elif score_banker > score_player:
            resultat = 'BANKER'
        else:
            resultat = 'TIE'

        # Total de cartes distribuées
        total_cartes = len(cartes_player) + len(cartes_banker)

        return cartes_player, cartes_banker, total_cartes, resultat

    def _calculer_nouvel_etat_sync(self, etat_actuel: int, cards_parity: str) -> int:
        """
        Calcule le nouvel état SYNC/DESYNC selon les règles Lupasco

        Args:
            etat_actuel: État SYNC/DESYNC actuel (0=SYNC, 1=DESYNC)
            cards_parity: Parité des cartes ('PAIR' ou 'IMPAIR')

        Returns:
            int: Nouvel état SYNC/DESYNC (0=SYNC, 1=DESYNC)
        """
        if cards_parity == 'PAIR':
            # PAIR conserve l'état
            return etat_actuel
        else:
            # IMPAIR change l'état
            return 1 if etat_actuel == 0 else 0
    
    def generer_partie(self, partie_number: int, max_mains: int = 60) -> PartieBaccarat:
        """
        Génère une partie complète de baccarat réaliste avec hasard cryptographiquement sécurisé

        Args:
            partie_number: Numéro de la partie
            max_mains: Nombre maximum de mains totales (défaut: 60)

        Returns:
            PartieBaccarat: Partie générée avec mélange sécurisé
        """
        # Génération silencieuse pour optimisation mémoire

        # Créer et mélanger le sabot de façon sécurisée
        sabot = self._creer_sabot_complet()

        # Effectuer le brûlage
        cartes_brulees, burn_cards_count, burn_parity, initial_sync = self._effectuer_brulage(sabot)

        # Créer la partie
        partie = PartieBaccarat(
            partie_number=partie_number,
            sabot_initial=sabot.copy(),  # Copie pour référence
            cartes_restantes=len(sabot),
            cartes_brulees=cartes_brulees,
            burn_cards_count=burn_cards_count,
            burn_parity=burn_parity,
            initial_sync_state=initial_sync,
            mains=[],
            current_sync_state=initial_sync
        )

        # Stocker les informations de brûlage dans la partie (pas comme une main)
        partie.premiere_carte_brulee = cartes_brulees[0] if cartes_brulees else None

        # NOUVEAU : Alignement main_number = index avec main dummy vide
        # Ajouter une main dummy complètement vide pour l'alignement
        main_dummy = MainBaccarat(
            main_number=None,
            manche_pb_number=None,
            cartes_player=[],
            cartes_banker=[],
            total_cartes_distribuees=0,
            score_player=0,
            score_banker=0,
            index1=None,
            cards_count=0,
            index2='',
            index3='',
            index5='',
            index6='',  # INDEX6 vide pour main dummy
            index7=''   # NOUVEAU : INDEX7 vide pour main dummy
        )

        # Ajouter la main dummy vide
        partie.mains.append(main_dummy)
        print(f"🎯 Main dummy vide ajoutée pour alignement main_number = index")

        # Jouer les mains jusqu'à EXACTEMENT max_mains (priorité absolue)
        main_counter = 1  # Commencer à 1 (première vraie main)
        manche_pb_counter = 1  # Commencer à manche 1 (avant distribution)

        while main_counter <= max_mains:  # CONDITION PRINCIPALE : 60 mains totales

            # Vérifier s'il reste assez de cartes pour une manche
            if len(sabot) < 6:
                print(f"ARRET FORCE : plus assez de cartes (mains: {main_counter-1}/{max_mains})")
                break

            # Vérifier si cut card atteinte (332 cartes distribuées) - INFORMATIF SEULEMENT
            cartes_distribuees = TOTAL_CARDS - len(sabot)
            if cartes_distribuees >= CUT_CARD_POSITION and not partie.cut_card_atteinte:
                print(f"Cut card atteinte : {cartes_distribuees} cartes distribuees")
                print(f"Continuation pour atteindre {max_mains} mains (mains restantes: {max_mains - main_counter + 1})")
                partie.cut_card_atteinte = True

            try:
                # Jouer une manche
                cartes_player, cartes_banker, total_cartes, result = self._jouer_manche(sabot)

                # Calculer les scores finaux
                score_player = self._calculer_score(cartes_player)
                score_banker = self._calculer_score(cartes_banker)

                # Déterminer la catégorie INDEX2
                cards_category = CARDS_MAPPING.get(total_cartes, f"cartes_{total_cartes}")
                cards_parity = 'PAIR' if total_cartes % 2 == 0 else 'IMPAIR'

                # État SYNC au début de cette main
                current_sync = partie.current_sync_state

                # NOUVEAU : Calculer INDEX6
                index1_index2 = f"{current_sync}_{cards_category}"
                index6 = INDEX6_MAPPING.get(index1_index2, '')

                # NOUVEAU : Calculer INDEX7 (INDEX6_INDEX3)
                index7 = f"{index6}_{result}" if index6 else ''

                # Calculer le numéro de manche P/B selon la nouvelle logique
                if result in ['PLAYER', 'BANKER']:
                    # PLAYER ou BANKER : utiliser le compteur actuel puis l'incrémenter
                    manche_pb_number = manche_pb_counter
                    manche_pb_counter += 1  # Incrémenter pour la prochaine manche P/B
                else:  # TIE
                    # TIE : utiliser le compteur actuel SANS l'incrémenter
                    manche_pb_number = manche_pb_counter

                # NOUVEAU : Créer la main avec alignement main_number = index
                # main_counter correspond maintenant directement à l'index dans le tableau
                main = MainBaccarat(
                    main_number=main_counter,  # main_counter = index de la main dans le tableau
                    manche_pb_number=manche_pb_number,
                    cartes_player=cartes_player,
                    cartes_banker=cartes_banker,
                    total_cartes_distribuees=total_cartes,
                    score_player=score_player,
                    score_banker=score_banker,
                    index1=current_sync,
                    cards_count=total_cartes,
                    index2=cards_category,
                    index3=result,
                    index5=f"{current_sync}_{cards_category}_{result}",
                    index6=index6,  # INDEX6
                    index7=index7   # NOUVEAU : INDEX7
                )

                partie.mains.append(main)

                # Mettre à jour l'état pour la prochaine main
                partie.current_sync_state = self._calculer_nouvel_etat_sync(current_sync, cards_parity)

                # Mettre à jour les statistiques (exclure la main dummy)
                partie.total_mains += 1  # Compter seulement les vraies mains
                if result in ['PLAYER', 'BANKER']:
                    partie.total_manches_pb += 1
                else:
                    partie.total_ties += 1

                main_counter += 1
                partie.cartes_restantes = len(sabot)

                # Vérifier si on a atteint exactement max_mains
                if main_counter > max_mains:
                    print(f"Objectif atteint : {max_mains} mains completees")
                    break

            except ValueError as e:
                # Plus assez de cartes
                print(f"Fin de partie forcee : {e}")
                print(f"Mains realisees : {main_counter-1}/{max_mains}")
                break

        # Afficher le résumé de la partie
        mains_realisees = main_counter - 1  # -1 car le compteur est toujours en avance
        if mains_realisees == max_mains:
            print(f"Partie {partie_number} terminee : {max_mains} mains (objectif atteint)")
        else:
            print(f"Partie {partie_number} terminee : {mains_realisees}/{max_mains} mains")

        return partie
    
    def generer_multiple_parties(self, nb_parties: int, max_mains: int = 60) -> List[PartieBaccarat]:
        """
        Génère plusieurs parties (version séquentielle - conservée pour compatibilité)

        Args:
            nb_parties: Nombre de parties à générer
            max_mains: Nombre maximum de mains totales par partie

        Returns:
            List[PartieBaccarat]: Liste des parties générées
        """
        parties = []

        for i in range(1, nb_parties + 1):
            partie = self.generer_partie(i, max_mains)
            parties.append(partie)
            self.parties_generees.append(partie)

            # Partie générée (affichage supprimé)

        return parties

    def generer_multiple_parties_parallel(self, nb_parties: int, max_mains: int = 60, max_workers: Optional[int] = None) -> List[PartieBaccarat]:
        """
        Génère plusieurs parties EN PARALLÈLE pour accélération maximale

        PRÉSERVE EXACTEMENT la même logique de génération que generer_partie()
        Utilise ProcessPoolExecutor pour contourner le GIL Python

        Args:
            nb_parties: Nombre de parties à générer
            max_mains: Nombre maximum de mains totales par partie
            max_workers: Nombre de processus (défaut: nombre de cœurs CPU)

        Returns:
            List[PartieBaccarat]: Liste des parties générées (ordre préservé)
        """
        if max_workers is None:
            max_workers = min(mp.cpu_count(), nb_parties)

        print(f"🚀 GÉNÉRATION PARALLÈLE : {nb_parties} parties sur {max_workers} processus")

        # Diviser le travail en chunks pour optimiser la performance
        chunk_size = max(1, nb_parties // (max_workers * 4))  # 4 chunks par worker

        parties = [None] * nb_parties  # Pré-allouer pour préserver l'ordre

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Soumettre les tâches avec leurs indices pour préserver l'ordre
            future_to_index = {}

            for i in range(nb_parties):
                future = executor.submit(_generer_partie_worker, i + 1, max_mains)
                future_to_index[future] = i

            # Collecter les résultats au fur et à mesure
            completed = 0
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    partie = future.result()
                    parties[index] = partie
                    completed += 1

                    # Affichage du progrès
                    if completed % max(1, nb_parties // 20) == 0:  # Afficher tous les 5%
                        progress = (completed / nb_parties) * 100
                        print(f"⚡ Progrès: {completed}/{nb_parties} parties ({progress:.1f}%)")

                except Exception as e:
                    print(f"❌ Erreur génération partie {index + 1}: {e}")
                    # Générer en fallback séquentiel pour cette partie
                    parties[index] = self.generer_partie(index + 1, max_mains)

        # OPTIMISATION MÉMOIRE : Ne pas stocker dans self.parties_generees
        # pour éviter la consommation excessive de RAM

        return [p for p in parties if p]  # Filtrer les None éventuels

    def generer_parties_par_batch_optimise(self, nb_parties_total: int, batch_size: int = 10000,
                                         max_mains: int = 60, max_workers: Optional[int] = None) -> str:
        """
        GÉNÉRATION OPTIMISÉE POUR TRÈS GRANDES QUANTITÉS (1M+ parties)

        Génère les parties par batches avec export immédiat et libération mémoire
        pour éviter la consommation excessive de RAM

        Args:
            nb_parties_total: Nombre total de parties à générer
            batch_size: Taille de chaque batch (défaut: 10000)
            max_mains: Nombre de mains par partie (défaut: 60)
            max_workers: Nombre de processus parallèles

        Returns:
            str: Nom du fichier JSON condensé final
        """
        if max_workers is None:
            max_workers = min(mp.cpu_count(), batch_size)

        print(f"🎯 GÉNÉRATION OPTIMISÉE GRANDE ÉCHELLE")
        print(f"📊 Total parties : {nb_parties_total:,}")
        print(f"📦 Taille batch : {batch_size:,}")
        print(f"⚡ Processus : {max_workers}")
        print("=" * 50)

        # Créer le nom de fichier final
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename_final = f"dataset_baccarat_lupasco_{timestamp}_condensed.json"

        # Calculer le nombre de batches
        nb_batches = (nb_parties_total + batch_size - 1) // batch_size

        # Initialiser le fichier JSON avec structure de base
        with open(filename_final, 'w', encoding='utf-8') as f:
            f.write('{"parties_condensees": [')

        parties_generees_total = 0

        for batch_num in range(nb_batches):
            # Calculer les indices pour ce batch
            start_idx = batch_num * batch_size + 1
            end_idx = min((batch_num + 1) * batch_size, nb_parties_total)
            nb_parties_batch = end_idx - start_idx + 1

            print(f"📦 Batch {batch_num + 1}/{nb_batches}: parties {start_idx}-{end_idx}")

            # Générer ce batch en parallèle (SANS stockage dans self.parties_generees)
            parties_batch = self._generer_batch_parallel_optimise(
                start_idx, nb_parties_batch, max_mains, max_workers
            )

            # Export immédiat de ce batch
            self._append_batch_to_json(parties_batch, filename_final, batch_num > 0)

            parties_generees_total += len(parties_batch)

            # LIBÉRATION MÉMOIRE CRITIQUE
            del parties_batch
            import gc
            gc.collect()

            print(f"✅ Batch {batch_num + 1} terminé - Total: {parties_generees_total:,}/{nb_parties_total:,}")

        # Finaliser le fichier JSON
        with open(filename_final, 'a', encoding='utf-8') as f:
            f.write(']}')

        print(f"🎉 GÉNÉRATION TERMINÉE : {parties_generees_total:,} parties")
        print(f"📁 Fichier : {filename_final}")

        return filename_final

    def _generer_batch_parallel_optimise(self, start_idx: int, nb_parties: int,
                                       max_mains: int, max_workers: int) -> List[PartieBaccarat]:
        """Génère un batch de parties en parallèle SANS affichage verbeux"""
        parties = [None] * nb_parties

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            future_to_index = {}

            for i in range(nb_parties):
                partie_number = start_idx + i
                future = executor.submit(_generer_partie_worker_silencieux, partie_number, max_mains)
                future_to_index[future] = i

            # Collecter sans affichage verbeux
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    partie = future.result()
                    parties[index] = partie
                except Exception as e:
                    print(f"❌ Erreur partie {start_idx + index}: {e}")
                    # Fallback silencieux
                    generateur_fallback = GenerateurPartiesBaccarat()
                    parties[index] = generateur_fallback._generer_partie_silencieuse(start_idx + index, max_mains)

        return [p for p in parties if p]

    def _generer_partie_silencieuse(self, partie_number: int, max_mains: int = 60) -> PartieBaccarat:
        """Version silencieuse de generer_partie pour optimisation mémoire"""
        # Créer et mélanger le sabot de façon sécurisée
        sabot = self._creer_sabot_complet()

        # Effectuer le brûlage
        cartes_brulees, burn_cards_count, burn_parity, initial_sync = self._effectuer_brulage(sabot)

        # Créer la partie
        partie = PartieBaccarat(
            partie_number=partie_number,
            sabot_initial=sabot.copy(),  # Copie pour référence
            cartes_restantes=len(sabot),
            cartes_brulees=cartes_brulees,
            burn_cards_count=burn_cards_count,
            burn_parity=burn_parity,
            initial_sync_state=initial_sync,
            mains=[],
            current_sync_state=initial_sync
        )

        # Ajouter la main dummy pour alignement (main_number = index)
        main_dummy = MainBaccarat(
            main_number=0,  # Index 0 = main dummy
            manche_pb_number=0,
            cartes_player=[],
            cartes_banker=[],
            total_cartes_distribuees=0,
            score_player=0,
            score_banker=0,
            index1=initial_sync,
            cards_count=0,
            index2="dummy",
            index3="DUMMY",
            index5=f"{initial_sync}_dummy_DUMMY",
            index6="",  # INDEX6 vide pour main dummy
            index7=""   # NOUVEAU : INDEX7 vide pour main dummy
        )
        partie.mains.append(main_dummy)

        # Jouer les mains jusqu'à EXACTEMENT max_mains (priorité absolue)
        main_counter = 1  # Commencer à 1 (première vraie main)
        manche_pb_counter = 1  # Commencer à manche 1 (avant distribution)

        while main_counter <= max_mains:  # CONDITION PRINCIPALE : 60 mains totales

            # Vérifier s'il reste assez de cartes pour une manche
            if len(sabot) < 6:
                break

            # Vérifier si cut card atteinte (332 cartes distribuées) - INFORMATIF SEULEMENT
            cartes_distribuees = TOTAL_CARDS - len(sabot)
            if cartes_distribuees >= CUT_CARD_POSITION and not partie.cut_card_atteinte:
                partie.cut_card_atteinte = True

            try:
                # Jouer une manche
                cartes_player, cartes_banker, total_cartes, result = self._jouer_manche(sabot)

                # Calculer les scores finaux
                score_player = self._calculer_score(cartes_player)
                score_banker = self._calculer_score(cartes_banker)

                # Déterminer la catégorie INDEX2
                cards_category = CARDS_MAPPING.get(total_cartes, f"cartes_{total_cartes}")
                cards_parity = 'PAIR' if total_cartes % 2 == 0 else 'IMPAIR'

                # État SYNC au début de cette main
                current_sync = partie.current_sync_state

                # NOUVEAU : Calculer INDEX6
                index1_index2 = f"{current_sync}_{cards_category}"
                index6 = INDEX6_MAPPING.get(index1_index2, '')

                # NOUVEAU : Calculer INDEX7 (INDEX6_INDEX3)
                index7 = f"{index6}_{result}" if index6 else ''

                # Calculer le numéro de manche P/B selon la nouvelle logique
                if result in ['PLAYER', 'BANKER']:
                    # PLAYER ou BANKER : utiliser le compteur actuel puis l'incrémenter
                    manche_pb_number = manche_pb_counter
                    manche_pb_counter += 1  # Incrémenter pour la prochaine manche P/B
                else:  # TIE
                    # TIE : utiliser le compteur actuel SANS l'incrémenter
                    manche_pb_number = manche_pb_counter

                # Créer la main avec alignement main_number = index
                main = MainBaccarat(
                    main_number=main_counter,  # main_counter = index de la main dans le tableau
                    manche_pb_number=manche_pb_number,
                    cartes_player=cartes_player,
                    cartes_banker=cartes_banker,
                    total_cartes_distribuees=total_cartes,
                    score_player=score_player,
                    score_banker=score_banker,
                    index1=current_sync,
                    cards_count=total_cartes,
                    index2=cards_category,
                    index3=result,
                    index5=f"{current_sync}_{cards_category}_{result}",
                    index6=index6,  # INDEX6
                    index7=index7   # NOUVEAU : INDEX7
                )

                partie.mains.append(main)

                # Mettre à jour l'état pour la prochaine main
                partie.current_sync_state = self._calculer_nouvel_etat_sync(current_sync, cards_parity)

                # Mettre à jour les statistiques (exclure la main dummy)
                partie.total_mains += 1  # Compter seulement les vraies mains
                if result in ['PLAYER', 'BANKER']:
                    partie.total_manches_pb += 1
                else:
                    partie.total_ties += 1

                main_counter += 1
                partie.cartes_restantes = len(sabot)

                # Vérifier si on a atteint exactement max_mains
                if main_counter > max_mains:
                    break

            except ValueError as e:
                # Plus assez de cartes
                break

        return partie

    def _append_batch_to_json(self, parties: List[PartieBaccarat], filename: str, add_comma: bool):
        """Ajoute un batch de parties au fichier JSON de façon optimisée"""
        exportateur = ExportateurCondense()

        # Convertir les parties au format condensé
        parties_condensees = []
        for partie in parties:
            partie_condensee = {
                "partie_number": partie.partie_number,
                "statistiques": {
                    "total_mains": partie.total_mains,
                    "total_manches_pb": partie.total_manches_pb,
                    "total_ties": partie.total_ties
                },
                "index1_brulage": partie.initial_sync_state,
                "mains_condensees": [
                    {
                        "main_number": main.main_number,
                        "manche_pb_number": main.manche_pb_number,
                        "index1": main.index1,
                        "index2": main.index2,
                        "index3": main.index3,
                        "index5": main.index5,
                        "index6": main.index6,  # INDEX6
                        "index7": main.index7   # NOUVEAU : INDEX7
                    } for main in partie.mains
                ]
            }
            parties_condensees.append(partie_condensee)

        # Écrire dans le fichier
        with open(filename, 'a', encoding='utf-8') as f:
            if add_comma:
                f.write(',')

            for i, partie_data in enumerate(parties_condensees):
                if i > 0:
                    f.write(',')
                json.dump(partie_data, f, ensure_ascii=False)

    def exporter_txt(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties en format texte (.txt) selon le format demandé

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier TXT
        """
        with open(filename, 'w', encoding='utf-8') as txtfile:
            # En-tête du fichier
            txtfile.write("GÉNÉRATEUR PARTIES BACCARAT LUPASCO\n")
            txtfile.write("=" * 50 + "\n")
            txtfile.write(f"Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txtfile.write(f"Nombre de parties : {len(parties)}\n")
            txtfile.write("Hasard cryptographiquement sécurisé\n")
            txtfile.write("=" * 50 + "\n\n")

            for partie in parties:
                # Informations de brûlage en en-tête de chaque partie
                premiere_carte = partie.premiere_carte_brulee
                carte_str = f"{premiere_carte.rang}{premiere_carte.couleur}" if premiere_carte else "N/A"

                txtfile.write(f"PARTIE {partie.partie_number}\n")
                txtfile.write(f"BURN: {partie.burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {partie.initial_sync_state}\n")
                txtfile.write("-" * 90 + "\n")
                txtfile.write("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5      | INDEX6 | INDEX7\n")
                txtfile.write("-" * 75 + "\n")

                for main in partie.mains:
                    # NOUVEAU : Exclure la main dummy (main_number = None) de l'export TXT
                    if main.main_number is None:
                        continue  # Ignorer la main dummy dans l'export TXT

                    txtfile.write(f"{main.main_number:4d} | {main.manche_pb_number:6d} | {main.index1:6s} | {main.index2:8s} | {main.index3:6s} | {main.index5:11s} | {main.index6:6s} | {main.index7}\n")

                txtfile.write("\n")  # Ligne vide entre les parties


def _generer_partie_worker(partie_number: int, max_mains: int = 60) -> PartieBaccarat:
    """
    Fonction worker pour génération parallèle

    IMPORTANT: Cette fonction doit être au niveau module (pas dans la classe)
    pour être picklable par ProcessPoolExecutor

    Crée une nouvelle instance du générateur pour chaque processus
    afin de préserver l'indépendance cryptographique

    Args:
        partie_number: Numéro de la partie
        max_mains: Nombre maximum de mains

    Returns:
        PartieBaccarat: Partie générée avec hasard indépendant
    """
    # Créer une nouvelle instance pour ce processus
    # Chaque processus aura son propre générateur avec entropie indépendante
    generateur = GenerateurPartiesBaccarat()

    # Générer la partie avec la même logique exacte
    return generateur.generer_partie(partie_number, max_mains)


def _generer_partie_worker_silencieux(partie_number: int, max_mains: int = 60) -> PartieBaccarat:
    """
    Version SILENCIEUSE du worker pour génération optimisée grande échelle

    Identique à _generer_partie_worker mais utilise la version silencieuse
    pour éviter les millions de prints qui ralentissent la génération
    """
    generateur = GenerateurPartiesBaccarat()
    return generateur._generer_partie_silencieuse(partie_number, max_mains)

    def exporter_json(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties en format JSON pour analyse facile

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier JSON
        """
        def carte_to_dict(carte: Carte) -> Dict:
            """Convertit une carte en dictionnaire"""
            return {
                "rang": carte.rang,
                "couleur": carte.couleur,
                "valeur": carte.valeur
            }

        def main_to_dict(main: MainBaccarat) -> Dict:
            """Convertit une main en dictionnaire"""
            return {
                "main_number": main.main_number,
                "manche_pb_number": main.manche_pb_number,
                "cartes_player": [carte_to_dict(c) for c in main.cartes_player],
                "cartes_banker": [carte_to_dict(c) for c in main.cartes_banker],
                "total_cartes_distribuees": main.total_cartes_distribuees,
                "score_player": main.score_player,
                "score_banker": main.score_banker,
                "index1": main.index1,
                "cards_count": main.cards_count,
                "index2": main.index2,
                "index3": main.index3,
                "index5": main.index5,
                "index6": main.index6,  # INDEX6
                "index7": main.index7,  # NOUVEAU : INDEX7
                "timestamp": main.timestamp
            }

        def partie_to_dict(partie: PartieBaccarat) -> Dict:
            """Convertit une partie en dictionnaire"""
            return {
                "partie_number": partie.partie_number,
                "burn_info": {
                    "cartes_brulees": [carte_to_dict(c) for c in partie.cartes_brulees],
                    "burn_cards_count": partie.burn_cards_count,
                    "burn_parity": partie.burn_parity,
                    "initial_sync_state": partie.initial_sync_state,
                    "premiere_carte_brulee": carte_to_dict(partie.premiere_carte_brulee) if partie.premiere_carte_brulee else None
                },
                "statistiques": {
                    "total_mains": partie.total_mains,
                    "total_manches_pb": partie.total_manches_pb,
                    "total_ties": partie.total_ties,
                    "cut_card_atteinte": partie.cut_card_atteinte,
                    "cartes_restantes": partie.cartes_restantes
                },
                "mains": [main_to_dict(main) for main in partie.mains]
            }

        # Structure JSON principale
        data = {
            "metadata": {
                "generateur": "GÉNÉRATEUR PARTIES BACCARAT LUPASCO",
                "version": "2.0",
                "date_generation": datetime.now().isoformat(),
                "nombre_parties": len(parties),
                "hasard_cryptographique": True,
                "description": "Parties de baccarat générées avec hasard cryptographiquement sécurisé selon les règles Lupasco"
            },
            "configuration": {
                "decks_count": DECKS_COUNT,
                "total_cards": TOTAL_CARDS,
                "cut_card_position": CUT_CARD_POSITION,
                "cards_mapping": CARDS_MAPPING
            },
            "parties": [partie_to_dict(partie) for partie in parties]
        }

        # Écrire le fichier JSON avec indentation pour lisibilité
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data, jsonfile, indent=2, ensure_ascii=False)

        print(f"Export JSON terminé : {filename}")

class ExportateurCondense:
    """
    Exportateur qui génère directement le format condensé
    identique à celui produit par reorganisateur_json_baccarat.py
    """

    def __init__(self):
        """Initialise l'exportateur condensé"""
        pass

    def exporter_json_condense(self, parties: List[PartieBaccarat], filename: str):
        """
        Exporte les parties directement au format condensé
        identique à reorganisateur_json_baccarat.py

        Args:
            parties: Liste des parties à exporter
            filename: Nom du fichier JSON
        """
        def main_to_dict_condense(main: MainBaccarat) -> Dict:
            """Convertit une main au format condensé"""
            # Pour la ligne d'alignement (main_number: null), tous les index sont des chaînes vides
            if main.main_number is None:
                return {
                    "main_number": None,
                    "manche_pb_number": None,
                    "index1": "",
                    "index2": "",
                    "index3": "",
                    "index5": "",
                    "index6": "",  # INDEX6 vide pour main dummy
                    "index7": ""   # NOUVEAU : INDEX7 vide pour main dummy
                }
            else:
                return {
                    "main_number": main.main_number,
                    "manche_pb_number": main.manche_pb_number,
                    "index1": main.index1,
                    "index2": main.index2,
                    "index3": main.index3,
                    "index5": main.index5,
                    "index6": main.index6,  # INDEX6
                    "index7": main.index7   # NOUVEAU : INDEX7
                }

        def partie_to_dict_condense(partie: PartieBaccarat) -> Dict:
            """Convertit une partie au format condensé"""
            return {
                "partie_number": partie.partie_number,
                "statistiques": {
                    "total_mains": partie.total_mains,
                    "total_manches_pb": partie.total_manches_pb,
                    "total_ties": partie.total_ties
                },
                "index1_brulage": partie.initial_sync_state,
                "mains_condensees": [main_to_dict_condense(main) for main in partie.mains]
            }

        # Structure JSON condensée (sans metadata)
        data_condensee = {
            "parties_condensees": [partie_to_dict_condense(partie) for partie in parties]
        }

        # Écrire le fichier JSON avec indentation pour lisibilité
        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(data_condensee, jsonfile, indent=2, ensure_ascii=False)

        print(f"Export JSON condensé terminé : {filename}")

# ============================================================================
# EXEMPLE D'UTILISATION
# ============================================================================

def generer_dataset_complet(nb_parties: int = 1000, use_parallel: bool = True, batch_size: int = 100000):
    """
    Génère un dataset complet pour l'analyse Lupasco avec hasard cryptographiquement sécurisé

    OPTIMISÉ POUR TRÈS GRANDES QUANTITÉS (1M+ parties) :
    - Génération par batches avec export immédiat
    - Libération mémoire après chaque batch
    - Affichage minimal pour performance maximale

    Args:
        nb_parties: Nombre de parties à générer (défaut: 1000000)
        use_parallel: Utiliser la génération parallèle (défaut: True)
        batch_size: Taille des batches pour optimisation mémoire (défaut: 10000)
    """
    # Créer le générateur avec hasard sécurisé
    generateur = GenerateurPartiesBaccarat()

    # CHOIX DE LA MÉTHODE SELON LA QUANTITÉ
    if nb_parties >= 50000:
        # GÉNÉRATION OPTIMISÉE POUR GRANDES QUANTITÉS
        print(f"🚀 Mode optimisé grande échelle activé (≥50K parties)")
        return generateur.generer_parties_par_batch_optimise(
            nb_parties_total=nb_parties,
            batch_size=batch_size,
            max_mains=60
        )
    else:
        # GÉNÉRATION CLASSIQUE POUR PETITES QUANTITÉS
        print(f"🎯 GÉNÉRATION DATASET BACCARAT LUPASCO")
        print(f"📊 Parties à générer : {nb_parties}")
        print(f"🎲 Mains par partie : 60")
        print(f"🔐 Hasard cryptographique : ACTIVÉ")
        print(f"⚡ Mode parallèle : {'ACTIVÉ' if use_parallel else 'DÉSACTIVÉ'}")
        print("=" * 50)

        # Générer les parties (60 mains chacune) - VERSION CLASSIQUE
        if use_parallel and nb_parties > 1:
            parties = generateur.generer_multiple_parties_parallel(nb_parties=nb_parties, max_mains=60)
        else:
            parties = generateur.generer_multiple_parties(nb_parties=nb_parties, max_mains=60)

        # Créer le nom de fichier avec timestamp (format condensé uniquement)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename_json_condense = f"dataset_baccarat_lupasco_{timestamp}_condensed.json"

        # Exporter uniquement en JSON condensé
        exportateur_condense = ExportateurCondense()
        exportateur_condense.exporter_json_condense(parties, filename_json_condense)

        return filename_json_condense


def test_generateur():
    """Test simple du générateur pour vérifier la logique et les exports"""
    print("TEST DU GENERATEUR BACCARAT LUPASCO")
    print("=" * 50)

    # Créer le générateur
    generateur = GenerateurPartiesBaccarat(seed=42)  # Seed pour reproductibilité

    # Générer 1 partie complète (60 mains)
    print("Génération d'une partie complète (60 mains)...")
    partie = generateur.generer_partie(100, max_mains=60)

    # Afficher les résultats détaillés
    print(f"\nRESULTATS DE LA PARTIE TEST :")
    print(f"Brûlage : {partie.burn_cards_count} cartes, parité {partie.burn_parity}")
    print(f"État initial : {partie.initial_sync_state}")
    print(f"Total mains : {len(partie.mains)}")
    print(f"Manches P/B : {partie.total_manches_pb}")
    print(f"TIE : {partie.total_ties}")

    # Afficher les informations de brûlage
    premiere_carte = partie.premiere_carte_brulee
    carte_str = f"{premiere_carte.rang}{premiere_carte.couleur}" if premiere_carte else "N/A"
    print(f"BURN: {partie.burn_cards_count} cartes | Première carte: {carte_str} | INDEX1 initial: {partie.initial_sync_state}")

    print(f"\nDETAIL DES PREMIERES MAINS :")
    print("Main | Manche | INDEX1 | INDEX2   | INDEX3 | INDEX5      | INDEX6 | INDEX7")
    print("-" * 80)

    for i, main in enumerate(partie.mains[:15]):  # Afficher les 15 premières mains
        manche_str = str(main.manche_pb_number) if main.manche_pb_number is not None else "-"
        print(f"{main.main_number:4d} | {manche_str:6s} | {main.index1:6d} | {main.index2:8s} | {main.index3:6s} | {main.index5:11s} | {main.index6:6s} | {main.index7}")

    # Test des exports TXT et JSON
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename_txt = f"test_partie_lupasco_{timestamp}.txt"
    filename_json = f"test_partie_lupasco_{timestamp}.json"

    print(f"\nTEST DES EXPORTS :")
    # generateur.exporter_txt([partie], filename_txt)  # DÉSACTIVÉ
    generateur.exporter_json([partie], filename_json)

    print(f"✅ Fichiers de test générés :")
    # print(f"📝 TXT : {filename_txt}")  # DÉSACTIVÉ
    print(f"📋 JSON : {filename_json}")

    return partie

if __name__ == "__main__":
    # Générer les parties
    filename_json_condense = generer_dataset_complet()
    print(f"Fichier généré : {filename_json_condense}")
