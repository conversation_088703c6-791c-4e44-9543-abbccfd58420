\author{
<PERSON>
}

\title{
Upper and Lower Bounds for Stochastic Processes
}

\section*{Decomposition Theorems}

June 8, 2021

\author{
Springer-Verlag \\ Berlin Heidelberg NewYork \\ London Paris Tokyo \\ Hong Kong Barcelona \\ Budapest
}

Dedicated to the memory of <PERSON>.
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-002.jpg?height=1009&width=1509&top_left_y=967&top_left_x=308)

Fig. -1.1. <PERSON> (1934-2020), by <PERSON>.

\section*{Contents}
0. Introduction ..... 1
1. What is this Book About? ..... 5
1.1 Philosophy ..... 5
1.2 What is Chaining? ..... 5
1.3 The Kolmogorov Conditions ..... 6
1.4 Chaining in a Metric Space: Dudley's Bound ..... 9
1.5 Overall Plan of the Book ..... 12
1.6 Does this Book Contain any Ideas? ..... 12
1.7 Overview by Chapters ..... 13
1.7.1 Gaussian Processes and the Generic Chaining ..... 13
1.7.2 Trees and Other Measures of Size ..... 15
1.7.3 Matching Theorems ..... 15
1.7.4 Warming up With \(p\)-stable Processes ..... 16
1.7.5 Bernoulli Processes ..... 16
1.7.6 Random Fourier Series and Trigonometric Sums ..... 17
1.7.7 Partition Scheme for Families of Distances ..... 17
1.7.8 Peaky parts of Functions ..... 18
1.7.9 Proof of the Bernoulli Conjecture ..... 18
1.7.10 Random Series of Functions ..... 18
1.7.11 Infinitely Divisible Processes ..... 18
1.7.12 Unfulfilled Dreams ..... 19
1.7.13 Empirical Processes ..... 19
1.7.14 Gaussian Chaos ..... 19
1.7.15 Convergence of Orthogonal Series; Majorizing Measures ..... 19
1.7.16 Shor's Matching Theorem ..... 20
1.7.17 The Ultimate Matching Theorem in Dimension Three . ..... 20
1.7.18 Applications to Banach Space Theory ..... 20
2. Gaussian Processes and the Generic Chaining ..... 23
2.1 Overview ..... 23
2.2 Measuring the Size of the Supremum ..... 23
2.3 The Union Bound and Other Basic Facts ..... 24
2.4 The Generic Chaining ..... 29
2.5 Entropy Numbers ..... 33
2.6 Rolling Up our Sleeves: Chaining in the Simplex ..... 38
2.7 Admissible Sequences of Partitions ..... 41
2.8 Functionals ..... 45
2.9 Partitioning Schemes ..... 48
2.10 Gaussian Processes: The Majorizing Measure Theorem ..... 57
2.11 Gaussian Processes as Subsets of a Hilbert Space ..... 61
2.12 Dreams ..... 68
2.13 A First Look at Ellipsoids ..... 70
2.14 Rolling Up our Sleeves: Chaining on Ellipsoids ..... 75
2.15 Continuity of Gaussian Processes ..... 77
2.16 Notes and Comments ..... 81
3. Trees and Other Measures of Size ..... 83
3.1 Trees ..... 83
3.1.1 Separated Trees ..... 84
3.1.2 Organized trees ..... 85
3.1.3 Majorizing Measures ..... 88
3.2 Rolling Up our Sleeves: Trees in Ellipsoids ..... 91
3.3 Fernique's Functional ..... 94
3.3.1 Fernique's Functional ..... 94
3.3.2 Fernique's Convexity Argument ..... 95
3.3.3 From Majorizing Measures to Sequences of Partitions . ..... 97
3.4 Witnessing Measures ..... 99
3.5 An inequality of Fernique ..... 100
4. Matching Theorems ..... 105
4.1 The Ellipsoid Theorem ..... 105
4.2 Partitioning Scheme, II ..... 110
4.3 Matchings ..... 113
4.4 Discrepancy Bounds ..... 116
4.5 The Ajtai-Komlós-Tusnády Matching Theorem ..... 117
4.5.1 The Long and Instructive Way ..... 119
4.5.2 The Short and Magic Way ..... 128
4.6 Lower Bound for the Ajtai-Komlós-Tusnády Theorem ..... 131
4.7 The Leighton-Shor Grid Matching Theorem ..... 138
4.8 Lower Bound for the Leighton-Shor Theorem ..... 145
4.9 For the Expert Only ..... 148
4.10 Notes and Comments ..... 151
Part II. Some Dreams Come True
5. Warming Up With \(\boldsymbol{p}\)-Stable Processes ..... 155
\(5.1 \quad p\)-Stable Processes as Conditionally Gaussian Processes ..... 155
5.2 A Lower Bound for \(p\)-Stable Processes ..... 156
5.3 Philosophy ..... 157
5.4 Simplification Through Abstraction ..... 158
5.5 1-Stable Processes ..... 161
5.6 Where Do We Stand? ..... 162
6. Bernoulli Processes ..... 163
6.1 Bernoulli r.v.s ..... 163
6.2 Boundedness of Bernoulli Processes ..... 164
6.3 Concentration of Measure ..... 167
6.4 Sudakov Minoration ..... 169
6.5 Comparison Principle ..... 174
6.6 Control in \(\ell^{\infty}\) Norm ..... 175
6.7 Peaky Parts of Functions ..... 178
6.8 Discrepancy Bounds for Empirical Processes ..... 183
6.9 Notes and Comments ..... 186
7. Random Fourier Series and Trigonometric Sums ..... 187
7.1 Translation Invariant Distances ..... 189
7.2 Basics ..... 192
7.2.1 Simplification Through Abstraction ..... 192
7.2.2 Setting ..... 192
7.2.3 Upper Bounds in the Bernoulli case ..... 193
7.2.4 Lower Bounds in the Gaussian Case ..... 194
7.3 Random Distances ..... 195
7.3.1 Basic Principles ..... 195
7.3.2 A General Upper Bound ..... 198
7.3.3 A Side Story ..... 200
7.4 The Marcus-Pisier Theorem ..... 202
7.4.1 The Marcus-Pisier Theorem ..... 202
7.4.2 Applications of the Marcus-Pisier Theorem ..... 204
7.5 Statement of Main Results ..... 206
7.5.1 General Setting ..... 206
7.5.2 Families of Distances ..... 208
7.5.3 Lower Bounds ..... 210
7.5.4 Upper Bounds ..... 213
7.5.5 Highlighting the Magic of Theorem 7.5.5 ..... 213
7.5.6 Combining Upper and Lower Bounds ..... 215
7.5.7 An Example: Tails in \(u^{-p}\) ..... 215
7.5.8 The Decomposition Theorem ..... 217
7.5.9 Convergence ..... 218
7.6 A Primer on Random Sets ..... 219
7.7 Proofs, Lower Bounds ..... 221
7.8 Proofs, Upper Bounds ..... 227
7.8.1 Road Map ..... 227
7.8.2 A Key Step ..... 227
7.8.3 Road Map: An overview of Decomposition Theorems ..... 229
7.8.4 Decomposition Theorem in the Bernoulli Case ..... 230
7.8.5 Upper Bounds in the Bernoulli case ..... 233
7.8.6 The Main Upper Bound ..... 234
7.8.7 Sums With Few Non-Zero Terms ..... 237
7.9 Proof of the Decomposition Theorem ..... 241
7.9.1 Constructing Decompositions ..... 241
7.9.2 Proof of Proposition 7.9.1 ..... 243
7.10 Proofs, Convergence ..... 246
7.11 Further Proofs ..... 250
7.11.1 Alternate Proof of Proposition 7.5.13 ..... 250
7.11.2 Proof of Theorem 7.5.18 ..... 254
7.12 Explicit Computations ..... 254
7.13 Vector-valued Series: A Theorem of Fernique ..... 259
7.14 Notes and Comments ..... 263
8. Partitioning Scheme and Families of Distances ..... 265
8.1 The Partitioning Scheme ..... 265
8.2 Tail Inequalities ..... 270
8.3 The Structure of Certain Canonical Processes ..... 277
9. Peaky Part of Functions ..... 285
9.1 Road Map ..... 285
9.2 Peaky Part of Functions, II ..... 285
9.3 Philosophy ..... 293
9.4 Chaining for Bernoulli Processes ..... 295
9.5 Notes and Comments ..... 298
10. Proof of the Bernoulli Conjecture ..... 299
10.1 Latata's Principle ..... 300
10.2 Philosophy, I ..... 304
10.3 Chopping Maps and Functionals ..... 304
10.3.1 Chopping Maps ..... 304
10.3.2 Basic Facts ..... 309
10.3.3 Functionals ..... 312
10.4 Philosophy, II ..... 315
10.5 Latala's Step ..... 316
10.6 Philosophy, III ..... 319
10.7 A Decomposition Lemma ..... 320
10.8 Building the Partitions ..... 323
10.9 Philosophy, IV ..... 327
10.10The Key Inequality ..... 328
10.11Philosophy, V ..... 333
10.12Proof of the Lataa-Bednorz Theorem ..... 334
10.13Philosophy, VI ..... 337
10.14A Geometric Characterisation of \(b(T)\). ..... 337
10.15Lower Bounds from Measures ..... 340
10.16Notes and Comments ..... 343
11. Random Series of Functions ..... 345
11.1 Road Map ..... 345
11.2 Random Series of Functions: General Setting ..... 346
11.3 Organization of the Chapter ..... 348
11.4 The Main Lemma ..... 349
11.5 Construction of the Majorizing Measure Using Convexity ..... 351
11.6 From Majorizing Measures to Partitions ..... 353
11.7 The General Lower Bound ..... 355
11.8 The Giné-Zinn Inequalities ..... 356
11.9 Proof of The Decomposition Theorem for Empirical Processes ..... 358
11.10The Decomposition Theorem for Random Series ..... 360
11.11Selector Processes, and Why They Matter ..... 362
11.12Proving the Generalized Bernoulli Conjecture ..... 363
11.13Notes and Comments ..... 367
12. Infinitely Divisible Processes ..... 369
12.1 Poisson r.v.s and Poisson Point Processes ..... 369
12.2 A Shortcut to Infinitely Divisible Processes ..... 371
12.3 Overview of Results ..... 373
12.3.1 The Main Lower Bound ..... 373
12.3.2 The Decomposition Theorem for Infinitely Divisible Processes ..... 374
12.3.3 Upper Bounds Through Bracketing ..... 376
12.3.4 Harmonizable Infinitely Divisible Processes ..... 376
12.3.5 Example: Harmonizable \(p\)-Stable Processes ..... 378
12.4 Proofs: The Bracketing Theorem ..... 379
12.5 Proofs: The Decomposition Theorem for Infinitely Divisible Processes ..... 380
12.6 Notes and Comments ..... 383
13. Unfulfilled Dreams ..... 385
13.1 Positive Selector Processes ..... 385
13.2 Explicitly Small Events ..... 386
13.3 My Lifetime Favorite Problem ..... 388
13.4 Classes of Sets ..... 390
Part III. Practicing
14. Empirical Processes, II ..... 399
14.1 Bracketing ..... 399
14.2 The Class of Squares of a Given Class ..... 401
14.3 When Not to Use Chaining ..... 412
15. Gaussian Chaos ..... 421
15.1 Order Two Gaussian Chaos ..... 421
15.1.1 Basic Facts ..... 421
15.1.2 When \(T\) is Small for the Distance \(d_{\infty}\) ..... 425
15.1.3 Covering Numbers ..... 431
15.1.4 Another Way to Bound \(S(T)\) ..... 432
15.1.5 Yet Another Way to Bound \(S(T)\) ..... 434
15.2 Tails of Multiple Order Gaussian Chaos ..... 436
15.3 Notes and Comments ..... 452
16. Convergence of Orthogonal Series; Majorizing Measures ..... 453
16.1 A Kind of Prologue: Chaining in a Metric Space and Pisier's Bound ..... 453
16.2 Introduction to Orthogonal Series; Paszkiewicz's Theorem ..... 455
16.3 Recovering the Classical Results ..... 457
16.4 Approach to Paszkiewicz's Theorem; Bednorz's Theorem ..... 460
16.5 Chaining, I ..... 465
16.6 Proof of Bednorz's Theorem ..... 468
16.7 Permutations ..... 477
16.8 Chaining, II ..... 486
16.9 Chaining, III ..... 500
16.10Notes and Comments ..... 502
17. Shor's Matching Theorem ..... 505
17.1 Introduction ..... 505
17.2 The Discrepancy Theorem ..... 506
17.3 Lethal Weakness of the Approach ..... 512
18. The Ultimate Matching Theorem in Dimension Three ..... 517
18.1 Introduction ..... 517
18.2 Regularization of \(\varphi\) ..... 521
18.3 Discretization ..... 523
18.4 Discrepancy Bound ..... 524
18.5 Geometry ..... 528
18.6 Probability, I ..... 535
18.7 Haar Basis Expansion ..... 541
18.8 Probability, II ..... 546
18.9 Final Effort ..... 548
19. Applications to Banach Space Theory ..... 555
19.1 Cotype of Operators ..... 555
19.1.1 Basic Definitions ..... 555
19.1.2 Operators From \(\ell_{N}^{\infty}\) ..... 557
19.1.3 Computing the Cotype-2 Constant with Few Vectors ..... 559
19.2 Unconditionality ..... 566
19.2.1 Classifying the Elements of \(B_{1}\) ..... 566
19.2.2 Subsets of \(B_{1}\) ..... 569
19.2.3 1-Unconditional Sequences and Gaussian Measures ..... 574
19.3 Probabilistic Constructions ..... 578
19.3.1 Restriction of Operators ..... 578
19.3.2 The \(\Lambda(p)\)-Problem ..... 587
19.4 Sidon Sets ..... 593
A. Discrepancy for Convex Sets ..... 601
A. 1 Introduction ..... 601
A. 2 Elements of Proof of the Upper Bound ..... 601
A. 3 The Lower Bound ..... 602
B. Some Deterministic Arguments ..... 607
B. 1 Hall's Matching Theorem ..... 607
B. 2 Proof of Lemma 4.7.11 ..... 609
B. 3 The Shor-Leighton Grid Matching Theorem ..... 610
B. 4 End of Proof of Theorem 17.2.1 ..... 615
B. 5 Proof of Proposition 17.3.1 ..... 617
B. 6 Proof of Proposition 17.2.4 ..... 619
C. Classical View of Infinitely Divisible Processes ..... 623
C. 1 Infinitely Divisible Random Variables ..... 623
C. 2 Infinitely Divisible Processes ..... 624
C. 3 Representation ..... 625
C. 4 p-Stable Processes ..... 626
D. Reading Suggestions ..... 627
D. 1 Partition Schemes ..... 627
D. 2 Geometry of Metric Spaces ..... 627
D. 3 Cover Times ..... 628
D. 4 Matchings ..... 628
D. 5 Super-concentration in the Sense of S. Chatterjee ..... 628
D. 6 High-Dimensional Statistics ..... 629
E. Research Directions ..... 631
E. 1 The Latała-Bednorz Theorem ..... 631
E. 2 The Ultimate Matching Conjecture ..... 631
E. 3 My Favorite Life-Time Problem ..... 631
E. 4 From a Set to its Convex Hull ..... 631
F. Solutions of Selected Exercises ..... 633
G. Comparison with the First Edition ..... 659
References ..... 661

\section*{0. Introduction}

This book had a previous edition [171]. The changes between the two editions are not only cosmetic or pedagogical, and the degree of improvement in the mathematics themselves is almost embarrassing at times. Besides significant simplifications in the arguments, several of the main conjectures of [171] have been solved and a new direction came to fruition. It would have been more appropriate to publish this text as a brand new book, but the improvements occurred gradually and the bureaucratic constraints of the editor did not allow a change at a late stage without further delay and uncertainty.

We first explain in broad terms the contents of this book, and then we detail some of the changes from [171].

What is the maximum level a certain river is likely to reach over the next 25 years? What is the likely magnitude of the strongest earthquake to occur during the life of a planned nuclear plant? These fundamental practical questions have motivated (arguably also fundamental) mathematics, some of which are the object of this book. The value \(X_{t}\) of the quantity of interest at time \(t\) is modeled by a random variable. What can be said about the maximum value of \(X_{t}\) over a certain range of \(t\) ? How can we guarantee that, with probability close to one, this maximum will not exceed a given threshold?

A collection of random variables \(\left(X_{t}\right)_{t \in T}\), where \(t\) belongs to a certain index set \(T\), is called a stochastic process, and the topic of this book is the study of the suprema of certain stochastic processes, and more precisely the search for upper and lower bounds for these suprema. The key word of the book is

\section*{INEQUALITIES.}

The "classical theory of processes" deals mostly with the case where \(T\) is a subset of the real line or of \(\mathbb{R}^{n}\). We do not focus on that case, and the book does not really expand on the most basic and robust results which are important in this situation. Our most important index sets are "highdimensional": the large sets of data which are currently the focus of so much attention consist of data which usually depend on many parameters. Our specific goal is to demonstrate the impact and the range of modern abstract methods, in particular through their treatment of several classical questions which are not accessible to "classical methods".

Andrey Kolmogorov invented the most important idea to bound stochastic processes: chaining. This wonderfully efficient method answers with little effort a number of basic questions, but fails to provide a complete understanding, even in natural situations. This is best discussed in the case of Gaussian processes, where the family \(\left(X_{t}\right)_{t \in T}\) consists of centered jointly Gaussian random variables (r.v.s). These are arguably the most important of all. A Gaussian process defines in a canonical manner a distance \(d\) on its index set \(T\) by the formula
\[
d(s, t)=\left(\mathrm{E}\left(X_{s}-X_{t}\right)^{2}\right)^{1 / 2}
\]

Probably the single most important conceptual progress about Gaussian processes was the gradual realization that the metric space ( \(T, d\) ) is the key object to understand them, even if \(T\) happens to be an interval of the real line. This led Richard Dudley to develop in 1967 an abstract version of Kolmgorov's chaining argument adapted to this situation. The resulting very efficient bound for Gaussian processes is unfortunately not always tight. Roughly speaking, "there sometimes remains a parasitic logarithmic factor in the estimates".

The discovery around 1985 (by Xavier Fernique and the author) of a precise (and in a sense, exact) relationship between the "size" of a Gaussian process and the "size" of this metric space provided the missing understanding in the case of these processes. Attempts to extend this result to other processes spanned a body of work which forms the core of this book.

A significant part of the book is devoted to situations where skill is required to "remove the last parasitic logarithm in the estimates". These situations occur with unexpected frequency in all kinds of problems. A particularly striking example is as follows. Consider \(n^{2}\) independent uniform random points \(\left(X_{i}\right)_{i \leq n^{2}}\) which are uniformly distributed in the unit square \([0,1]^{2}\). How far is a typical sample from being very uniformly spread on the unit square? To measure this we construct a one-to-one map \(\pi\) from \(\left\{1, \ldots, n^{2}\right\}\) to the vertices \(v_{1}, \ldots, v_{n^{2}}\) of a uniform \(n \times n\) grid in the unit square. If we try to minimize the average distance between \(X_{i}\) and \(v_{\pi(i)}\) we can do as well as about \(\sqrt{\log n} / n\) but no better. If we try to minimize the maximum distance between \(X_{i}\) and \(v_{\pi(i)}\), we can do as well as about \((\log n)^{3 / 4} / n\) but no better. The factor \(1 / n\) is just due to scaling, but the fractional powers of \(\log n\) require a surprising amount of work.

The book is largely self-contained, but it mostly deals with rather subtle questions such as the previous one. It also devotes considerable energy to the problem of finding lower bounds for certain processes, a topic far more difficult and less developed than the search for upper bounds. Even though some of the main ideas of at least Chapter 2 could (and should!) be taught at an elementary level, this is an advanced text.

This book is in a sense a continuation of the monograph [75], or at least of part of it. I made no attempt to cover again all the relevant material of
[75], but familiarity with [75] is certainly not a prerequisite, and maybe not even helpful. The way certain results are presented there is arguably obsolete, and, more importantly, many of the problems considered in [75] (in particular, limit theorems) are no longer the focus of much interest.

One of my main goals is to communicate as much as possible of my experience from working on stochastic processes, and I have covered most of my results in this area. A number of these results were proved many years ago. I still like them, but some seem to be waiting for their first reader. The odds of these results meeting this first reader while staying buried in the original papers seemed nil, but they might increase in the present book form. In order to present a somewhat coherent body of work, I have also included rather recent results by others in the same general direction. \({ }^{1}\) I find these results deep and very beautiful. They are sometimes difficult to access for the nonspecialist. Explaining them here in a unified and often simplified presentation could serve a useful purpose. Still, the choice of topics is highly personal and does not represent a systematic effort to cover all the important directions. I can only hope that the book contains enough state-of-art knowledge about sufficiently many fundamental questions to be useful.

Let me now try to outline the progress since the previous edition. \({ }^{2}\) While attempting to explain better my results to others, I ended up understanding them much better myself. The material of the previous edition was reduced by about 100 pages due to better proofs. \({ }^{3}\) More importantly, reexamination of the material resulted in new methods, and a new direction came to fruition, that of

\section*{DECOMPOSITION THEOREMS.}

The basic idea is that there are two fundamentally different way to control the size of a sum \(\sum_{i \leq N} X_{i}\). One may take advantage of cancellations between terms, or one may bound the sum by the sum of the absolute values. One may also interpolate between the two methods, which in that case means writing a decomposition \(X_{i}=X_{i}^{\prime}+X_{i}^{\prime \prime}\) and controlling the size of the sum \(\sum_{i \leq N} X_{i}^{\prime}\) by taking advantage of the cancellations between terms, but controlling the sum \(\sum_{i \leq N} X_{i}^{\prime \prime}\) by the sum of the absolute values. The same schoolboy idea, in the setting of stochastic processes, is that a process can be bounded on the one hand using chaining, and on the other hand can often be bounded by cruder methods, involving replacing certain sums by the sums of the absolute values. The amazing fact is that many processes can by controlled by interpolating between these two methods, that is can be decomposed into the sum two pieces, each of which can be controlled by one of these methods.

\footnotetext{
\({ }^{1}\) With one single exception I did not include results by others proved after the first edition of this book.
\({ }^{2}\) A precise comparison between the two editions may be found in Appendix G.
\({ }^{3}\) A limited quantity of material of secondary importance was also removed. The current edition is not shorter than the previous one because many details have been added, as well as an entire chapter on the new results, and a sketch of proof for many exercises.
}

Such is the nature of the landmark result of Bednorz and Latala [23], the proof of the Bernoulli conjecture, which is the towering result of this book. Several conjectures of [171] in the same general directions have been solved \({ }^{4}\) concerning in particular empirical processes and random series of functions.

Despite the considerable progress represented by the solution of these conjectures, a number of seemingly important questions remain open, and one of my main goals is to popularize these. Opinions differ as to what constitutes a really important problem, but I like those I explain here because they deal with fundamental structures. These problems might be challenging. At least, I tried my best to make progress on them, but they have seen little progress and received little attention.

I would like to express my infinite gratitude to Shahar Mendelson. While he was donating his time to help another of my projects, it became clear through our interactions that, while I had produced great efforts towards the quality of the mathematics contained in my books, I certainly had not put enough efforts in the exposition of this material. I concluded that there should be real room for improvement in the text of [171], and this is why I started to revise it, and this led to the major advances presented here.

While preparing the current text I have been helped by a number of people. I would like to thank some of them here (and to appologize to all those whom I do not mention). Ramon van Handel suggested a few almost embarrassing simplifications. Hengrui Luo and Zhenyuan Zhang suggested literately hundreds of improvements, and Rafal Meller's comments had a great impact too. Further luck had it that, almost at the last minute, my text attracted the attention of Kevin Tanguy whose efforts resulted in a higher level of detail and a gentler pace of exposition. In particular his and Zhang's efforts gave me the energy to make a fresh attempt at explaining and detailing the proof of the Bernoulli conjecture obtained by Bednorz and Latala in [23]. This proof is the most stupendously beautiful piece of mathematics I have met in my entire life. I wish the power of this result and the beauty of this proof become better understood.

I dedicate this work to the memory of Xavier Fernique. Fernique was a deeply original thinker. His groundbreaking contributions to the theory of Gaussian processes were viewed as exotic by mainstream probabilists, and he never got the recognition he deserved. I owe a great debt to Fernique: it is his work on Gaussian processes which made my own work possible, first on Gaussian processes, and then on all the situations beyond this case. This work occupied many of my most fruitful years. A large part of it is presented in the present volume. It would not have existed without Fernique's breakthroughs.

\footnotetext{
\({ }^{4}\) After another crucial contribution of Bednorz and Martynek [25].
}

\section*{1. What is this Book About?}

This short chapter describes the philosophy underlying this book, and some of its highlights. This description, often using words rather than formulas, is necessarily imprecise, and is only intended to provide some insight into our point of view.

\subsection*{1.1 Philosophy}

The practitioner of stochastic processes is likely to be struggling at any given time with his favorite model of the moment, a model which typically involves a rich and complicated structure. There is a near infinite supply of such models. The importance with which we view any one of them is likely to vary over time.

The first advice I received from my advisor Gustave Choquet was as follows: always consider a problem under the minimum structure in which it makes sense. This advice has literally shaped my mathematical life. It will probably be as fruitful in the future as it has been in the past. By following it, one is naturally led to study problems with a kind of minimal and intrinsic structure. Not so many structures are really basic, and one may hope that these will remain of interest for a very long time. This book is devoted to the study of such structures which arise when one tries to estimate the suprema of stochastic processes.

The feeling, real or imaginary, that one is studying objects of intrinsic importance is enjoyable, but the success of the approach of studying "minimal structures" has ultimately to be judged by its results. As we shall demonstrate, the tools arising from this approach provide the final words on a number of classical problems.

\subsection*{1.2 What is Chaining?}

A stochastic process is a collection of random variables (r.v.s) \(\left(X_{t}\right)_{t \in T}\) indexed by a set \(T\). To study it, Kolmogorov invented chaining, the main tool of this book. The fundamental idea of chaining is to replace the index set \(T\)
by a sequence of finite approximations \(T_{n}\), and to study the r.v.s \(X_{t}\) through successive approximations \(X_{\pi_{n}(t)}\) where \(\pi_{n}(t) \in T_{n}\). The first approximation consists of a single point \(t_{0}\) so \(T_{0}=\left\{t_{0}\right\}\). The fundamental relation is then
\[
X_{t}-X_{t_{0}}=\sum_{n \geq 1}\left(X_{\pi_{n}(t)}-X_{\pi_{n-1}(t)}\right)
\]

When \(T\) is finite, the only case we really need, the sum on the right is finite. This relation gives its name to the method: the chain of approximations \(\pi_{n}(t)\) links \(t_{0}\) and \(t\). To control the differences \(X_{t}-X_{t_{0}}\) it suffices then to control all the differences \(\left|X_{\pi_{n}(t)}-X_{\pi_{n-1}(t)}\right|\).

\subsection*{1.3 The Kolmogorov Conditions}

Kolmogorov stated the "Kolmogorov conditions", which robustly ensure the good behavior of a stochastic process indexed by a subset of \(\mathbb{R}^{m}\). These conditions are studied in any advanced probability course. If you have taken such a course, this section will refresh your memory about these conditions, and the next few sections will present the natural generalization of the chaining method in an abstract metric space, as it was understood in, say, 1970. Learning in detail about these historical developments now makes sense only if you have already heard of them, because the modern chaining method, which is presented in Chapter 2 is in a sense far simpler than the classical method. For this reason, the material up to Section 1.4 included is directed towards a reader who is already fluent in probability theory. If, on the other hand, you have never heard of these things, and if you find this material too difficult, you should start directly with Chapter 2, which is written at a far greater level of detail and assumes minimal familiarity with even basic probability theory.

We say that a process \(\left(X_{t}\right)_{t \in T}\), where \(T=[0,1]^{m}\), satisfies the Kolmogorov conditions if
\[
\forall s, t \in[0,1]^{m}, \mathrm{E}\left|X_{s}-X_{t}\right|^{p} \leq d(s, t)^{\alpha} .
\]
where \(d(s, t)\) denotes the Euclidean distance and \(p>0, \alpha>m\). Here E denotes mathematical expectation. In our notation the operator E applies to whatever expression is placed behind it, so that \(\mathrm{E}|Y|^{p}\) stands for \(\mathrm{E}\left(|Y|^{p}\right)\) and not for \((\mathrm{E}|Y|)^{p}\). This convention is in force throughout the book.

Let us apply the idea of chaining to processes satisfying the Kolmogorov conditions. The most obvious candidate for the approximating set \(T_{n}\) is the set \(G_{n}\) of points \(x\) in \(\left[0,1\left[^{m}\right.\right.\) such that the coordinates of \(2^{n} x\) are positive integers. \({ }^{1}\) Thus card \(G_{n}=2^{n m}\). It is completely natural to choose

\footnotetext{
\({ }^{1}\) There is no other reason for using the points \(x\) in \(\left[0,1\left[^{m}\right.\right.\) such that the coordinates of \(2^{n} x\) are positive integers rather than the points \(x\) in \([0,1]^{m}\) with the same property than the fact that there are \(2^{n m}\) such points rather than the typographically unpleasant number \(\left(2^{n}+1\right)^{m}\).
}
\(\pi_{n}(u) \in G_{n}\) as close to \(u\) as possible, so that \(d\left(u, \pi_{n}(u)\right) \leq \sqrt{m} 2^{-n}\), and \(d\left(\pi_{n}(u), \pi_{n-1}(u)\right) \leq d\left(\pi_{n}(u), u\right)+d\left(u, \pi_{n-1}(u)\right) \leq 3 \sqrt{m} 2^{-n}\).

For \(n \geq 1\) let us then define
\[
U_{n}=\left\{(s, t) ; s \in G_{n}, t \in G_{n}, d(s, t) \leq 3 \sqrt{m} 2^{-n}\right\} .
\]

Given \(s=\left(s_{1}, \ldots, s_{m}\right) \in G_{n}\), the number of points \(t=\left(t_{1}, \ldots, t_{m}\right) \in G_{n}\) with \(d(s, t) \leq 3 \sqrt{m} 2^{-n}\) is bounded independently of \(s\) and \(n\) because \(\left|t_{i}-s_{i}\right| \leq\) \(d(s, t)\) for each \(i \leq m\), so that we have the crucial property
\[
\operatorname{card} U_{n} \leq K(m) 2^{n m},
\]
where \(K(m)\) denotes a number depending only on \(m\), which need not be the same on each occurrence. Consider then the r.v.
\[
Y_{n}=\max \left\{\left|X_{s}-X_{t}\right| ;(s, t) \in U_{n}\right\},
\]
so that (and since \(G_{n-1} \subset G_{n}\) ) for each \(u\),
\[
\left|X_{\pi_{n}(u)}-X_{\pi_{n-1}(u)}\right| \leq Y_{n} .
\]

To avoid having to explain what is "a version of the process", and since we care only about inequalities, we will consider only the r.v.s \(X_{t}\) for \(t \in G=\) : \(\bigcup_{n \geq 0} G_{n}\). We first claim that
\[
\sup _{s, t \in G ; d(s, t) \leq 2^{-k}}\left|X_{s}-X_{t}\right| \leq 3 \sum_{n \geq k} Y_{n} .
\]

To prove this consider \(n \geq k\) such that \(s, t \in G_{n}\), so that \(s=\pi_{n}(s)\) and \(t=\pi_{n}(t)\). Assuming \(d(s, t) \leq 2^{-k}\), we have
\[
d\left(\pi_{k}(s), \pi_{k}(t)\right) \leq d\left(s, \pi_{k}(s)\right)+d(s, t)+d\left(t, \pi_{k}(t)\right) \leq 3 \sqrt{m} 2^{-k},
\]
so that \(\left(\pi_{k}(s), \pi_{k}(t)\right) \in U_{k}\) and thus
\[
\left|X_{\pi_{k}(s)}-X_{\pi_{k}(t)}\right| \leq Y_{k} .
\]

Next, for \(u \in\{s, t\}\),
\[
X_{u}-X_{\pi_{k}(u)}=X_{\pi_{n}(u)}-X_{\pi_{k}(u)}=\sum_{k \leq \ell<n} X_{\pi_{\ell+1}(u)}-X_{\pi_{\ell}(u)},
\]
and since \(\left|X_{\pi_{\ell+1}(u)}-X_{\pi_{\ell}(u)}\right| \leq Y_{\ell+1}\) we obtain \(\left|X_{u}-X_{\pi_{k}(u)}\right| \leq \sum_{\ell \geq k} Y_{\ell+1}\). To obtain (1.7) we then use the previous inequalities and the identity
\[
X_{s}-X_{t}=X_{s}-X_{\pi_{k}(s)}+X_{\pi_{k}(s)}-X_{\pi_{k}(t)}+X_{\pi_{k}(t)}-X_{t} .
\]

Let us now draw some consequences of (1.7). For a finite family of numbers \(V_{i} \geq 0\), we have
\[
\left(\max _{i} V_{i}\right)^{p} \leq \sum_{i} V_{i}^{p},
\]
and thus
\[
\mathrm{E} Y_{n}^{p} \leq \mathrm{E} \sum_{(s, t) \in U_{n}}\left|X_{s}-X_{t}\right|^{p} \leq K(m, \alpha) 2^{n(m-\alpha)}
\]
since \(\mathrm{E}\left|X_{s}-X_{t}\right|^{p} \leq K(m, \alpha) 2^{-n \alpha}\) for \((s, t) \in U_{n}\) by (1.2) and using (1.4). To proceed one needs to distinguish whether or not \(p \geq 1\). For specificity we assume \(p \geq 1\). Since, as we just proved, \(\left\|Y_{n}\right\|_{p}:=\left(\mathrm{E}\left|Y_{n}\right|^{p}\right)^{1 / p} \leq\) \(K(m, p, \alpha) 2^{n(m-\alpha) / p}\), the triangle inequality in \(L^{p}\) yields \({ }^{2}\)
\[
\left\|\sum_{n \geq k} Y_{n}\right\|_{p} \leq \sum_{n \geq k} K(m, p, \alpha) 2^{n(m-\alpha) / p} \leq K(m, p, \alpha) 2^{k(m-\alpha) / p}
\]

Combining with (1.7) we then obtain
\[
\left\|\sup _{s, t \in G ; d(s, t) \leq 2^{-k}}\left|X_{s}-X_{t}\right|\right\|_{p} \leq K(m, p, \alpha) 2^{k(m-\alpha) / p}
\]
a sharp inequality from which it is then simple to prove (with some loss of sharpness) results such as the fact that for \(0<\beta<\alpha-m\) one has
\[
\mathrm{E} \sup _{s, t \in G} \frac{\left|X_{s}-X_{t}\right|^{p}}{d(s, t)^{\beta}}<\infty
\]

Exercise 1.3.1. Prove (1.11). Hint: prove that
\[
\sum_{k \geq 0} \mathrm{E} \sup _{s, t \in G ; d(s, t) \leq 2^{-k}} 2^{k \beta}\left|X_{s}-X_{t}\right|^{p}<\infty
\]

Thus, chaining not only proves that the process \(\left(X_{t}\right)_{t \in T}\) has a continuous version, it also provides the very good estimate (1.10). One reason for which everything is so easy in this case is that the size of the terms \(X_{\pi_{n+1}(u)}-X_{\pi_{n}(u)}\) decreases like a geometric series.

Let us then pause for a moment and reflect on what we have been doing.
- The Euclidean metric structure of \(T\) is not really intrinsic to the problem. Far more intrinsic is the (quasi) distance on \(T\) given by \(\delta(s, t)=\left\|X_{s}-X_{t}\right\|_{p}\). The condition (1.2), which we may now write as \(\delta(s, t) \leq d(s, t)^{\alpha / p}\) simply enforces a kind of "smallness condition" on the metric space \((T, \delta)\).
- The use of the bound (1.6) is rather pessimistic, as it bounds each of the increments along the chain by the worst possible case among each increment.

\footnotetext{
\({ }^{2}\) There of course the two occurrences of the constant \(K(m, p, \alpha)\) are not the same.
}

These two remarks contain in germ much of the future progress we will make. Following the first remark, we will learn, starting with the next section, to look at problems in a more intrinsic manner. And our sharp chaining methods will avoid the crude bound of each increment by the worst possible case.

There are many variations on the previous ideas. The next two exercises explore one.

Exercise 1.3.2. Consider a convex function \(\varphi \geq 0\) with \(\varphi(0)=0\). Prove that for r.v.s \(V_{i} \geq 0\) one has
\[
\mathrm{E} \max _{i} V_{i} \leq \varphi^{-1}\left(\sum_{i} \mathrm{E} \varphi\left(V_{i}\right)\right)
\]

Exercise 1.3.3. Consider the function \(\varphi\) as above, and consider positive numbers \(c_{n}, d_{n}\). Assume that the process \(\left(X_{t}\right)_{t \in T}\) satisfies
\[
\forall n \geq 0, \forall s, t \in T, d(s, t) \leq 3 \sqrt{m} 2^{-n} \Rightarrow \mathrm{E} \varphi\left(\frac{\left|X_{s}-X_{t}\right|}{c_{n}}\right) \leq d_{n}
\]

Prove that
\[
\mathrm{E} \sup _{s, t \in G, d(s, t) \leq 2^{-k}}\left|X_{s}-X_{t}\right| \leq 3 \sum_{n \geq k} c_{n} \varphi^{-1}\left(K(m) 2^{n m} d_{n}\right)
\]

The series in (1.15) has no reason to converge like a geometric series, so we already are being more sophisticated than in the case of the Kolmogorov conditions. \({ }^{3}\)

\subsection*{1.4 Chaining in a Metric Space: Dudley's Bound}

Suppose now that we want to study the uniform convergence on \([0,1]\) of a random Fourier series \(X_{t}=\sum_{k \geq 1} a_{k} g_{k} \cos (2 \pi i k t)\) where \(a_{k}\) are numbers and \(\left(g_{k}\right)\) are independent standard Gaussian r.v.s. The Euclidean structure of \([0,1]\) is not intrinsic to the problem. Far more relevant is the distance \(d\) given by
\[
d(s, t)^{2}=\mathrm{E}\left(X_{s}-X_{t}\right)^{2}=\sum_{k} a_{k}^{2}(\cos (2 i \pi k s)-\cos (2 i \pi k t))^{2}
\]

This simple idea took a very long time to emerge. Once one thinks about the distance \(d\), then in turn the fact that the index set \(T\) is \([0,1]\) is no longer very

\footnotetext{
\({ }^{3}\) In the left-hand side of (1.15) we would like to do better than controlling the expectation, but one really needs some regularity of the function \(\varphi\) for this. It suffices here to say that when \(\varphi(x)=|x|^{p}\) for \(p \geq 1\) we may replace the expectation by the norm of \(L^{p}\), proceeding exactly as we did in the case of the Kolmogorov conditions.
}
relevant because this particular structure does not connect very well with the distance \(d\). One is then led to consider Gaussian processes indexed by an abstract set \(T .^{4}\) We say that \(\left(X_{t}\right)_{t \in T}\) is a Gaussian process when the family \(\left(X_{t}\right)_{t \in T}\) is jointly Gaussian centered. \({ }^{5}\) Then, just as in (1.16), the process induces a canonical distance \(d\) on \(T\) given by \(d(s, t)=\left(\mathrm{E}\left(X_{s}-X_{t}\right)^{2}\right)^{1 / 2}\). We will express that Gaussian r.v.s have small tails by the inequality
\[
\forall s, t \in T, \mathrm{E} \varphi\left(\frac{\left|X_{s}-X_{t}\right|}{d(s, t)}\right) \leq 1
\]
where \(\varphi(x)=\exp \left(x^{2} / 4\right)-1\). This inequality holds because if \(g\) is a standard Gaussian r.v. then \(\operatorname{Eexp}\left(g^{2} / 4\right) \leq 2 .{ }^{6}\)

To perform chaining for such a process, in the absence of further structure on our metric space ( \(T, d\) ), how do we choose the approximating sets \(T_{n}\) ? Thinking back to the Kolmogorov conditions it is very natural to introduce the following definition.

Definition 1.4.1. For \(\epsilon>0\) the covering number \(N(T, d, \epsilon)\) of a metric space ( \(T, d\) ) is the smallest integer \(N\) such that \(T\) can be covered by \(N\) balls of radius \(\epsilon .{ }^{7}\)

Equivalently, \(N(T, d, \epsilon)\) is the smallest number \(N\) such that there exists a set \(V \subset T\) with card \(V \leq N\) and such that each point of \(T\) is within distance \(\epsilon\) of \(V\).

Let us denote by \(\Delta(T)=\sup _{s, t \in T} d(s, t)\) the diameter of \(T\), and observe that \(N(T, d, \Delta(T))=1\). We construct our approximating sets \(T_{n}\) as follows. Consider the largest integer \(n_{0}\) with \(\Delta(T) \leq 2^{-n_{0}}\). For \(n \geq n_{0}\) consider a set \(T_{n} \subset T\) with \(\operatorname{card} T_{n}=N\left(T, d, 2^{-n}\right)\) such that each point of \(T\) is within distance \(2^{-n}\) of a point of \(T_{n} .{ }^{8}\) In particular \(T_{0}\) consists of a single point.

We then perform the chaining as in the case of the Kolmogorov conditions, using for \(\pi_{n}(t)\) a point in \(T_{n}\) with \(d\left(t, \pi_{n}(t)\right) \leq 2^{-n}\). Consider
\[
U_{n}=\left\{(s, t) ; s, t \in T_{n}, d(s, t) \leq 3 \cdot 2^{-n}\right\}
\]
so that
\[
\operatorname{card} U_{n} \leq\left(\operatorname{card} T_{n}\right)^{2}=N\left(T, d, 2^{-n}\right)^{2}
\]

\footnotetext{
\({ }^{4}\) Let us stress the point. Even though the index set is a subset of \(\mathbb{R}^{m}\) we have no chance to really understand the process unless we forget this irrelevant structure.
\({ }^{5}\) Centered means that \(\mathrm{E} X_{t}=0\) for each \(t\).
\({ }^{6}\) Starting with the next chapter we will control the r.v.s \(\left|X_{s}-X_{t}\right|\) through their tail properties, and (1.17) is just another way to present the same situation.
\({ }^{7}\) Here our balls are closed balls. One could also use open balls in this definition. There seems to be no universal agreement about this. For our purpose it makes no difference whatsoever.
\({ }^{8}\) We do not require that \(T_{n} \subset T_{n+1}\). In Section 1.3 it does happen that \(G_{n} \subset G_{n+1}\) but this was not really used in the arguments.
}

This crude bound is hard to improve in general and should be compared to (1.4). We now apply (1.13) to the r.v.s \(V_{i}=\left|X_{s}-X_{t}\right| /\left(3 \cdot 2^{-n}\right)\) for \(i=(s, t) \in U_{n}\). Since \(\mathrm{E} \varphi\left(V_{i}\right) \leq 1\) we obtain that the r.v.
\[
Y_{n}=\max \left\{\left|X_{s}-X_{t}\right| ;(s, t) \in U_{n}\right\}
\]
satisfies
\[
\mathrm{E} Y_{n} \leq 3 \cdot 2^{-n} \varphi^{-1}\left(N\left(T, d, 2^{-n}\right)^{2}\right)
\]
and exactly as in the case of the Kolmogorov conditions we obtain
\[
\mathrm{E} \sup _{d(s, t) \leq 2^{-k}}\left|X_{s}-X_{t}\right| \leq L \sum_{n \geq k} 2^{-n} \varphi^{-1}\left(N\left(T, d, 2^{-n}\right)^{2}\right)
\]
where \(L\) is a number (which may change between occurrences). We delay the exercise of writing this inequality in integral form as
\[
\mathrm{E} \sup _{d(s, t) \leq \delta}\left|X_{s}-X_{t}\right| \leq L \int_{0}^{\delta} \varphi^{-1}\left(N(T, d, \epsilon)^{2}\right) \mathrm{d} \epsilon
\]

In the case of the function \(\varphi(x)=\exp \left(x^{2} / 4\right)-1\), so that \(\varphi^{-1}(x)=\) \(2 \sqrt{\log (1+x)}\), inequality (1.18) is easily shown to be equivalent to the following more elegant formulation:

Theorem 1.4.2 (Dudley's bound). If \(\left(X_{t}\right)_{t \in T}\) is a Gaussian process with natural distance \(d\) then
\[
\mathrm{E} \sup _{d(s, t) \leq \delta}\left|X_{s}-X_{t}\right| \leq L \int_{0}^{\delta} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon
\]

This very general inequality is by far the most useful result on continuity of Gaussian processes.

Exercise 1.4.3. Prove that the previous bound gives the correct uniform modulus of continuity for Brownian motion on \([0,1]\) : for \(\delta \leq 1\),
\[
\mathrm{E} \sup _{|s-t| \leq \delta}\left|B_{s}-B_{t}\right| \leq L \sqrt{\delta \log (2 / \delta)}
\]

The message of Chapter 2 is simple:
- However useful, Dudley's bound is not optimal in a number of fundamentally important situations.
- It requires no more work to obtain a better bound which is optimal in every situation.

\subsection*{1.5 Overall Plan of the Book}

A specific feature of the index set \(T=[0,1]^{m}\) (provided with the Euclidean distance) occurring in the Kolmogorov conditions is that it is really " \(m\) dimensional" and "the same around each point". This is not the case for index sets which occur in a great many natural situations. If one had to summarize in one sentence the content of the upper bounds presented in this book, it would be that they develop methods which are optimal even when this feature does not occur.

The main tools are built in Parts I and II. Part I is devoted to the most important situation we consider in the book, the study of Gaussian processes, and we learn the basic concepts on how to measure the "size" of a metric space. The effectiveness of the corresponding tools is then demonstrated by proving classical results on matchings.

The goal of Part II is to extend the results of the Gaussian case to other more general processes. This program of building the proper tools to go beyond the Gaussian case was started by the author soon after he obtained his results on Gaussian processes (which are presented in Chapter 2). It is a significant endeavor which requires a number of new concepts. The most important of these is the idea of families of distances. We can no longer entirely describe the situation using a single distance on the index set (as is the case for Gaussian processes). In some sense this program has been completed. Most of the results which were dreamed by the author \({ }^{9}\) between 1985 and 1990 are now proved in Chapter 11.

Part III explores situations which belong to the same circle of ideas but in diverse directions.

\subsection*{1.6 Does this Book Contain any Ideas?}

At this stage it is not really possible to precisely describe any of the new ideas which will be presented, but if the following statements are not crystal-clear to you, you may have something to learn from this book.

Idea 1. It is possible to organize chaining optimally using increasing sequences of partitions.

Idea 2. There is an automatic device to construct such sequences of partitions, using "functionals", quantities which measure the size of the subsets of the index set. This yields a complete understanding of boundedness of Gaussian processes.

Idea 3. Ellipsoids are much smaller than one would think, because they (and more generally, sufficiently convex bodies) are thin around the edges.

\footnotetext{
\({ }^{9}\) Including some which sounded like crazily optimistic conjectures!
}

This explains the funny fractional powers of logarithms in certain matching theorems.

Idea 4. One may witness that a metric space is large by the fact that it contains large trees, or equivalently that it supports an extremely scattered probability measure.

Idea 5. Consider a set \(T\) on which you are given a distance \(d\) and a random distance \(d_{\omega}\) such that, given \(s, t \in T\), it is rare that the distance \(d_{\omega}(s, t)\) is much smaller that \(d(s, t)\). Then if in the appropriate sense ( \(T, d\) ) is large, it must be the case that ( \(T, d_{\omega}\) ) is typically large. This principle enormously constrains the structure of many bounded processes built on random series.

Idea 6. There are different ways a random series might converge. It might converge because chaining witnesses that there is cancellation between terms, or it might converge because the sum of the absolute values of its terms already converges. Many processes built on random series can be split in two parts, each one converging according to one of the previous phenomena.

The book contains many more ideas, but you will have to read more to discover them.

\subsection*{1.7 Overview by Chapters}

\subsection*{1.7.1 Gaussian Processes and the Generic Chaining}

This subsection gives an overview of Chapter 2. More generally, Subsection 1.7. \(n\) gives the overview for Chapter \(n+1\).

The most important question considered in this book is the boundedness of Gaussian processes. The key object is the metric space ( \(T, d\) ) where \(T\) is the index set and \(d\) the intrinsic distance (0.1). As investigated in Section 2.11 this metric space is far from being arbitrary: it is isometric to a subset of a Hilbert space. It is, however, a deadly trap to try to use this specific property of the metric space ( \(T, d\) ). The proper approach is to just think of it as a general metric space.

After reviewing some elementary facts, in Section 2.4 we explain the basic idea of the "generic chaining", one of the key ideas of this work. Chaining is a succession of steps that provide successive approximations of the index space ( \(T, d\) ). In the Kolmogorov chaining, for each \(n\) the difference between the \(n\)-th and the \((n+1)\)-th approximation of the process, which we call here "the variation of the process during the \(n\)-th chaining step", is "controlled uniformly over all possible chains". Generic chaining allows that the variation of the process during the \(n\)-th chaining step "may depend on which chain we follow". Once the argument is properly organized, it is not any more complicated than the classical argument. It is in fact exactly the same. Yet, while
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-025.jpg?height=909&width=459&top_left_y=375&top_left_x=632)

Fig. 1.1. Dependence chart between chapters. Very marginal dependence is not indicated.

Dudley's classical bound is not always sharp, the bound obtained through the generic chaining is optimal. Entropy numbers are reviewed in Section 2.5.

It is technically convenient to formulate the generic chaining bound using special sequences of partitions of the metric space ( \(T, d\) ), that we shall call admissible sequences throughout the book. The key to make the generic chaining bound useful is then to be able to construct admissible sequences. These admissible sequences measure an aspect of the "size" of the metric space and are introduced in Section 2.7. In Section 2.8 we introduce another method to measure the "size" of the metric space, through the behavior of certain "functionals", which are simply numbers attached to each subset of the entire space. The fundamental fact is that the two measures of the size of the metric space one obtains through either admissible sequences or through functionals are equivalent in full generality. This is proved in Section 2.8 for the easy part (that the admissible sequence approach provides a larger measure of size than the functional approach) and in Section 2.9 for the converse. This converse is, in effect, an algorithm to construct sequences of partitions in a metric space given a functional. Functionals are of considerable use throughout the book.

In Section 2.10 we prove that the generic bound can be reversed for Gaussian processes, therefore providing a characterization of their sampleboundedness. Generic chaining entirely explains the size of Gaussian processes, and the dream of Section 2.12 is that a similar situation will occur for many processes.

In Section 2.11 we explain why a Gaussian process in a sense is nothing but a subset of Hilbert space. Remarkably, a number of basic questions remain unanswered, such as how to relate through geometry the size of a subset of Hilbert space seen as a Gaussian process with the corresponding size of its convex hull.

Dudley's bound fails to explain the size of the Gaussian processes indexed by ellipsoids in Hilbert space. This is investigated in Section 2.13. Ellipsoids will play a basic role in Chapter 4.

\subsection*{1.7.2 Trees and Other Measures of Size}

We describe different notions of trees, and show how one can measure the "size" of a metric space by the size of the largest trees it contains, in a way which is equivalent to the measures of size introduced in Chapter 2. This idea played an important part in the history of Gaussian processes. Its appeal is mostly that trees are easy to visualize. Building a large tree in a metric space is an efficient method to bound its size from below. We then learn a method of Fernique to measure the size of a metric space through certain properties of the probability measures on it. It will be amenable to vast generalizations.

\subsection*{1.7.3 Matching Theorems}

Chapter 4 makes the point that the generic chaining (or some equivalent form of it) is already required to really understand the irregularities occurring in the distribution of \(N\) points \(\left(X_{i}\right)_{i \leq N}\) independently and uniformly distributed in the unit square. These irregularities are measured by the "cost" of pairing (=matching) these points with \(N\) fixed points that are very uniformly spread, for various notions of cost.

These optimal results involve mysterious powers of \(\log N\). We are able to trace them back to the geometry of ellipsoids in Hilbert space, so we start the chapter with an investigation of these ellipsoids in Section 4.1. The philosophy of the main result, the Ellipsoid Theorem, is that an ellipsoid is in some sense somewhat smaller than it appears at first. This is due to convexity: an ellipsoid gets "thinner" when one gets away from its center. The Ellipsoid Theorem is a special case of a more general result (with the same proof) about the structure of sufficiently convex bodies, one that will have important applications in Chapter 19.

In Section 4.3 we provide general background on matchings. In Section 4.5 we investigate the case where the cost of a matching is measured
by the average distance between paired points. We prove the result of Ajtai, Komlós, Tusnády, that the expected cost of an optimal matching is at most \(L \sqrt{\log N} / \sqrt{N}\) where \(L\) is a number. The factor \(1 / \sqrt{N}\) is simply a scaling factor, but the fractional power of log is optimal as shown in Section 4.6. In Section 4.7 we investigate the case where the cost of a matching is measured instead by the maximal distance between paired points. We prove the theorem of Leighton and Shor that the expected cost of a matching is at most \(L(\log N)^{3 / 4} / \sqrt{N}\), and the power of \(\log\) is shown to be optimal in Section 4.8.

With the exception of Section 4.1, the results of Chapter 4 are not connected to any subsequent material before Chapter 17.

\subsection*{1.7.4 Warming up With \(\boldsymbol{p}\)-stable Processes}

With this chapter we start the program of vastly extending the results of Chapter 2 concerning Gaussian processes. We outline several of the fruitful methods on the class of \(p\)-stable processes, based on their property of being conditionally Gaussian.

\subsection*{1.7.5 Bernoulli Processes}

Random signs are obviously important r.v.s, and occur frequently in connection with "symmetrization procedures", a very useful tool. In a Bernoulli process the individual random variables \(X_{t}\) are linear combinations of independent random signs. Each Bernoulli process is associated with a Gaussian process in a canonical manner, when one replaces the random signs by independent standard Gaussian r.v.s. The Bernoulli process has better tails than the corresponding Gaussian process (it is "subgaussian") and is bounded whenever the corresponding Gaussian process is bounded. There is, however, a completely different reason for which a Bernoulli process might be bounded; namely, that the sum of the absolute values of the coefficients of the random signs remain bounded independently of the index \(t\). A natural question is then to decide whether these two extreme situations are the only fundamental reasons why a Bernoulli process can be bounded, in the sense that a suitable "mixture" of them occurs in every bounded Bernoulli process. This was the "Bernoulli Conjecture" (to be stated formally on page 167), which has been so brilliantly solved by W. Bednorz and R. Latała.

It is a long road to the solution of the Bernoulli conjecture, and we start to build the main tools bearing on Bernoulli processes. A linear combination of independent random signs looks like a Gaussian r.v. when the coefficients of the random signs are small. We can expect that a Bernoulli process will look like a Gaussian process when these coefficients are suitably small. This is a fundamental idea: the key to understanding Bernoulli processes is to reduce to situations where these coefficients are small.

The Bernoulli conjecture, on which the author worked so many years, greatly influenced the way he looked at various processes. In the case of empirical processes, this is explained in Section 6.8.

\subsection*{1.7.6 Random Fourier Series and Trigonometric Sums}

The basic example of a random Fourier series is
\[
X_{t}=\sum_{k \geq 1} \xi_{k} \exp (2 \pi \mathrm{i} k t)
\]
where \(\mathrm{i}^{2}=-1\), where \(t \in[0,1]\) and the r.v.s \(\xi_{k}\) are independent symmetric. In this chapter we provide a final answer to the question of the convergence of such series.

The fundamental case where \(\xi_{k}=a_{k} g_{k}\) for numbers \(a_{k}\) and independent Gaussian r.v.s ( \(g_{k}\) ) is of great historical importance. There is, however, another motivation for the study of such series. The generic chaining and related methods are well adapted to the case of a "non-homogeneous index space". The study of certain of the processes we will consider in the next chapters is already subtle even in the absence of the extra difficulty due to this lack of homogeneity. The setting of random Fourier series allows us to put aside the issue of lack of homogeneity and to concentrate on the other difficulties, and played a great part in the development of the theory. It provides an ideal setting to understand a basic fact: many processes can be exactly controlled, not by using one or two distances, but by using an entire family of distances. This concept of "family of distances" will play a major role later. It is also while analyzing the lower bounds discovered in the setting of random Fourier series that the author discovered the method which allows to extend these bounds to general random series as explained in Chapter 11. In this chapter we also meet our first "decomposition theorem": there are two distinct reasons which explain the size of a random trigonometric sum. First, there can be a lot of cancellation between the terms. Second, it may happen that the sum of the absolute values of the terms is already small. We show that every random trigonometric sum is the sum of two such pieces, one of each type.

\subsection*{1.7.7 Partition Scheme for Families of Distances}

Once one has survived the initial surprise of the new idea that many processes are naturally associated to an entire family of distances, it is very pleasant to realize that the tools of Section 2.9 can be extended to this setting with essentially the same proof. This is the purpose of Section 8.1.

In Section 8.3 we apply these tools to the situation of "canonical processes" where the r.v.s \(X_{t}\) are linear combinations of independent copies of symmetric r.v.s with density proportional to \(\exp \left(-|x|^{\alpha}\right)\) where \(\alpha \geq 1\) (and to considerably more general situations as discovered by R. Latała). In these
situations, the size of the process can be completely described from the geometry of the index space, a far reaching extension of the Gaussian case.

\subsection*{1.7.8 Peaky parts of Functions}

We learn how to measure the size of sets of functions on a measured space using an appropriate family of distances. We show that when we control this size, for each function of the set we can distinguish its "peaky part" in a coherent way over the whole set of functions which then has in a sense a simple structure, as it is built from simpler pieces.

\subsection*{1.7.9 Proof of the Bernoulli Conjecture}

Having learned how to manipulate "families of distances" we are now better prepared to prove the Bernoulli conjecture. This is the (overwhelmingly important) Latała-Bednorz theorem. The challenging proof occupies most of Chapter \(10 .^{10}\) In the last section we investigate how to get lower bounds on Bernoulli processes using "witnessing measures".

\subsection*{1.7.10 Random Series of Functions}

For a large class of random series of functions, we prove in full generality that chaining explains all the part of the boundedness of these processes created by cancellations, in the spirit of the Bernoulli conjecture. This covers both the cases of empirical processes and of the closely related class of selector processes. Our main tool is to reduce to processes which are conditionally Bernoulli processes and to use the Latała-Bednorz theorem and its consequences.

\subsection*{1.7.11 Infinitely Divisible Processes}

The infinitely divisible processes we study are indexed by a general set, and are to Lévy processes what a general Gaussian process (index by an arbitrary index set) is to Brownian motion (a Gaussian process indexed by \(\mathbb{R}\) with stationary increments). We extend to these processes our results on random series of functions: chaining explains all the part of the boundedness of these processes which is due to cancellations. The results are described in complete detail with all definitions in Section 12.3.

\footnotetext{
\({ }^{10}\) It is a good research program to discover a more intuitive approach to this result.
}

\subsection*{1.7.12 Unfulfilled Dreams}

Having proved in several general settings that "chaining explains all the part of the boundedness which is due to cancellation", we concentrate on the problem of describing the "part of the boundedness which owes nothing to cancellation". We propose sweeping conjectures. The underlying hope behind these conjectures is that, ultimately, a bound for a selector process always arises from the use of the 'union bound' \(\mathrm{P}\left(\cup_{n} A_{n}\right) \leq \sum_{n} \mathrm{P}\left(A_{n}\right)\) in a simple situation, the use of basic principles such as linearity and positivity, or combinations of these.

\subsection*{1.7.13 Empirical Processes}

We focus on a special yet fundamental topic: the control of the supremum of the empirical process over a class of functions.

We demonstrate again the power of the chaining scheme of Section 9.4 by providing a sharper version of Ossiander's bracketing theorem with a very simple proof. We then illustrate various techniques by presenting proofs of two deep recent results.

\subsection*{1.7.14 Gaussian Chaos}

Our satisfactory understanding of the properties of Gaussian processes should bring information about processes that are, in various senses, related to Gaussian processes. Such is the case of an order two Gaussian chaos (which is essentially a family of second degree polynomials of Gaussian random variables). It seems at present a hopelessly difficult task to give lower and upper bounds of the same order for these processes, but in Section 15.1 we obtain a number of results in this direction. Chaos processes are very instructive because there exist other methods than chaining to control their size (a situation which we do not expect to occur for processes defined as sums of a random series).

In Section 15.2 we study the tails of a single multiple-order Gaussian chaos, and present (yet another) deep result of R. Latała which provides a rather complete description of the size of these tails.

\subsection*{1.7.15 Convergence of Orthogonal Series; Majorizing Measures}

The old problem of characterizing the sequences ( \(a_{m}\) ) such that for each orthonormal sequence ( \(\varphi_{m}\) ) the series \(\sum_{m \geq 1} a_{m} \varphi_{m}\) converges a.s. was solved by A. Paszkiewicz. Using a more abstract point of view, we present a very much simplified proof of his results (due essentially to W. Bednorz). This leads us to the question of discussing when a certain condition on the "increments" of a process implies its boundedness. When the increment condition is of "polynomial type", this is more difficult than in the case of Gaussian processes,
and requires the notion of "majorizing measure". We present several elegant results of this theory, in their seemingly final forms recently obtained by W . Bednorz.

\subsection*{1.7.16 Shor's Matching Theorem}

This chapter continues Chapter 4. We prove a deep improvement of the Ajtai, Komlós, Tusnády theorem due to P. Shor. Unfortunately, due mostly to our lack of geometrical understanding, the best conceivable matching theorem, which would encompass this result as well as those of Chapter 4, and much more, remains as a challenging problem, "the ultimate matching conjecture" (a conjecture which is solved in the next chapter in dimension \(\geq 3\) ).

\subsection*{1.7.17 The Ultimate Matching Theorem in Dimension Three}

In this case, which is easier than the case of dimension two (but still apparently rather non-trivial), we are able to obtain the seemingly final result about matchings, a strong version of "the ultimate matching conjecture". There are no more fractional powers of \(\log N\) here, but in a random sample of \(N\) points uniformly distributed in \([0,1]^{3}\), local irregularities occur at all scales between \(N^{-1 / 3}\) and \((\log N)^{1 / 3} N^{-1 / 3}\), and our result can be seen as a precise global description of these irregularities.

\subsection*{1.7.18 Applications to Banach Space Theory}

Chapter 19 gives applications to Banach space theory. As interest in this theory has decreased in recent years, we have not reproduced many of the results of [171], and we urge the interested reader to consult this earlier edition. We have kept only the results which make direct use of results presented elsewhere in the book (rather than including results based on the methods of the book). In Section 19.1.2, we study the cotype of operators from \(\ell_{N}^{\infty}\) into a Banach space. In Section 19.1.3, we prove a comparison principle between Rademacher (=Bernoulli) and Gaussian averages of vectors in a finite dimensional Banach space, and we use it to compute the Rademacher cotype-2 of a finite dimensional space using only a few vectors. In Section 19.2.1 we discover how to classify the elements of the unit ball of \(L^{1}\) "according to the size of the level sets". In Section 19.2.3 we explain, given a 1 -unconditional sequence \(\left(e_{i}\right)_{i \leq N}\) in a Banach space \(E\) how to "compute" the quantity \(\mathrm{E}\left\|\sum_{i} g_{i} e_{i}\right\|\) when \(g_{i}\) are independent Gaussian r.v.s, a further variation on the fundamental theme of the interplay between the \(L^{1}, L^{2}\) and \(L^{\infty}\) norms. In Section 19.3.1 we study the norm of the restriction of an operator from \(\ell_{N}^{q}\) to the subspace generated by a randomly chosen small proportion of the coordinate vectors, and in Section 19.3.2 we use these results to deduce the celebrated results of J. Bourgain on the \(\Lambda_{p}\) problem. Recent results of Gilles Pisier on Sidon sets conclude this chapter in Section 19.4.

\title{
Part I
}

\section*{The Generic Chaining}

\section*{2. Gaussian Processes and the Generic Chaining}

\subsection*{2.1 Overview}

The overview of this chapter is given in Chapter 1, Subsection 1.7.1. More generally, Subsection 1.7. \(n\) is the overview of Chapter \(n+1\).

\subsection*{2.2 Measuring the Size of the Supremum}

In this section we consider a metric space \((T, d)\) and a process \(\left(X_{t}\right)_{t \in T}\). Unless explicitly specified otherwise (and even when we forget to repeat it) we will always assume that the process is centered, i.e.
\[
\forall t \in T, \quad \mathrm{E} X_{t}=0
\]

We will measure the "size of the process \(\left(X_{t}\right)_{t \in T}\) " by the quantity \(\mathrm{E}_{\sup _{t \in T}} X_{t}\). Why this quantity is a good measure of the "size of the process", is explained in Lemma 2.2.1 below.

When \(T\) is uncountable it is not obvious what the quantity \(\mathrm{E}_{\sup _{t \in T}} X_{t}\) means. \({ }^{1}\) We define it by the following formula:
\[
\mathrm{E} \sup _{t \in T} X_{t}=\sup \left\{\mathrm{E} \sup _{t \in F} X_{t} ; F \subset T, F \text { finite }\right\}
\]
where the right-hand side makes sense as soon as each r.v. \(X_{t}\) is integrable. This will be the case in almost all the situations considered in this book.

Let us say that a process \(\left(X_{t}\right)_{t \in T}\) is symmetric if it has the same law as the process \(\left(-X_{t}\right)_{t \in T}\). Almost all the processes we shall consider are symmetric (although this hypothesis is not necessary for some of our results). The following lemma justifies using the quantity \(\mathrm{Esup}_{t} X_{t}\) to measure "the size of a symmetric process".

Lemma 2.2.1. If the process \(\left(X_{t}\right)_{t \in T}\) is symmetric then
\[
\mathrm{E} \sup _{s, t \in T}\left|X_{s}-X_{t}\right|=2 \mathrm{E} \sup _{t \in T} X_{t}
\]

\footnotetext{
\({ }^{1}\) Such questions are treated in detail e.g. in [75] pages 42-43.
}

Proof. We note that
\[
\sup _{s, t \in T}\left|X_{s}-X_{t}\right|=\sup _{s, t \in T}\left(X_{s}-X_{t}\right)=\sup _{s \in T} X_{s}+\sup _{t \in T}\left(-X_{t}\right),
\]
and we take expectations. \({ }^{2}\)
Exercise 2.2.2. Consider a symmetric process \(\left(X_{t}\right)_{t \in T}\). Given any \(t_{0}\) in \(T\) prove that
\[
\mathrm{E} \sup _{t \in T}\left|X_{t}\right| \leq 2 \mathrm{E} \sup _{t \in T} X_{t}+\mathrm{E}\left|X_{t_{0}}\right| \leq 3 \mathrm{E} \sup _{t \in T}\left|X_{t}\right| .
\]

The previous exercise is easy, but this need not be always the case. The author has never taught this material in a classroom, and cannot really evaluate the level of difficulty of the exercises for a beginner. So please do not feel discouraged if most of the exercises feel like research problems. \({ }^{3}\) A sketch of a solution is provided for almost every exercise. For the exercises which are too difficult, understanding this very concise sketch is in itself a good exercise. Just try to peek at the solution one line at a time.

In this book, we often state inequalities about the supremum of a symmetric process using the quantity \(\operatorname{Esup}_{t \in T} X_{t}\) simply because this quantity looks typographically more elegant than the equivalent \({ }^{4}\) quantity \(\mathrm{Esup}_{s, t \in T} \mid X_{s}-\) \(X_{t} \mid\). It is good to remember that when \(X_{t_{0}}=0\) for some \(t_{0} \in T\), (2.3) shows that there is not so much difference between \(\mathrm{E} \sup _{t \in T} X_{t}\) and \(\mathrm{E} \sup _{t \in T}\left|X_{t}\right|\).

We actually often need to control the tails of the r.v. \(\sup _{s, t \in T}\left|X_{s}-X_{t}\right|\), not only its first moment. Emphasis is given to the first moment because this is the difficult part, and once this is achieved, control of higher moments is often provided by the same arguments.

\subsection*{2.3 The Union Bound and Other Basic Facts}

From now on we assume that the process \(\left(X_{t}\right)_{t \in T}\) satisfies the increment condition:
\[
\forall u>0, \mathrm{P}\left(\left|X_{s}-X_{t}\right| \geq u\right) \leq 2 \exp \left(-\frac{u^{2}}{2 d(s, t)^{2}}\right)
\]

\footnotetext{
\({ }^{2}\) To be really rigorous, we should first consider the case where \(T\) is finite and then appeal to (2.2), but it is better to skip this kind of tedious detail.
\({ }^{3}\) I had feedback from talented readers who felt that way. Consequently, I did not shy away to state as "exercises" rather non-trivial material complementing the text while being fully aware that one has to have achieved a rather complete understanding of the concepts as well as a mastery of the techniques to solve them.
\({ }^{4}\) Equivalent does not mean equal, we have been dropping a factor 2 here. Generally speaking the methods of this book are not appropriate to find sharp numerical constants and all the crucial inequalities are "sharp within a multiplicative constant".
}
where \(d\) is a distance on \(T\). In particular this is the case when \(\left(X_{t}\right)_{t \in T}\) is a Gaussian process and \(d(s, t)^{2}=\mathrm{E}\left(X_{s}-X_{t}\right)^{2}\). Our goal is to find bounds on \(\mathrm{E}_{\sup _{t \in T}} X_{t}\) depending on the structure of the metric space ( \(T, d\) ). We will assume that \(T\) is finite, which, as shown by (2.2), does not decrease generality.

Given any \(t_{0}\) in \(T\), the centering hypothesis (2.1) implies
\[
\mathrm{E} \sup _{t \in T} X_{t}=\mathrm{E} \sup _{t \in T}\left(X_{t}-X_{t_{0}}\right)
\]

The latter form has the advantage that we now seek estimates for the expectation of the non-negative r.v. \(Y=\sup _{t \in T}\left(X_{t}-X_{t_{0}}\right)\). For such a variable we have the formula
\[
\mathrm{E} Y=\int_{0}^{\infty} \mathrm{P}(Y \geq u) \mathrm{d} u
\]

Let us note that since the function \(u \mapsto \mathrm{P}(Y \geq u)\) is non-increasing, for any \(u>0\) we have the following
\[
\mathrm{E} Y \geq u \mathrm{P}(Y \geq u)
\]

In particular \(\mathrm{P}(Y \geq u) \leq \mathrm{E} Y / u\), a very important fact known as Markov's inequality. Arguments such as the following one will be of constant use.

Exercise 2.3.1. Consider a r.v. \(Y \geq 0\) and \(a>0\). Prove that \(\mathrm{P}(Y \leq a \mathrm{E} Y) \geq\) \(1-1 / a\).

Let us stress the content of this result. It will be used when \(Y\) is a kind a random error, of very small expectation, \(\mathrm{E} Y=b^{2}\) where \(b\) is small. Then most of time \(Y\) is small: \(\mathrm{P}(Y \leq b) \geq 1-b\).

According to (2.6) it is natural to look for bounds of
\[
\mathrm{P}\left(\sup _{t \in T}\left(X_{t}-X_{t_{0}}\right) \geq u\right)
\]

The first bound that comes to mind is the "union bound"
\[
\mathrm{P}\left(\sup _{t \in T}\left(X_{t}-X_{t_{0}}\right) \geq u\right) \leq \sum_{t \in T} \mathrm{P}\left(X_{t}-X_{t_{0}} \geq u\right)
\]

It seems worthwhile to immediately draw some consequences from this bound, and to discuss at leisure a number of other simple, yet fundamental facts. This will take a bit over three pages, after which we will come back to the main story of bounding \(Y\). Throughout this work, \(\Delta(T)\) denotes the diameter of \(T\),
\[
\Delta(T)=\sup _{t_{1}, t_{2} \in T} d\left(t_{1}, t_{2}\right)
\]

When we need to make clear which distance we use in the definition of the diameter, we will write \(\Delta(T, d)\) rather than \(\Delta(T)\). Consequently (2.4) and (2.9) imply
\[
\mathrm{P}\left(\sup _{t \in T}\left(X_{t}-X_{t_{0}}\right) \geq u\right) \leq 2 \operatorname{card} T \exp \left(-\frac{u^{2}}{2 \Delta(T)^{2}}\right)
\]

Let us now record a simple yet important computation, that will allow us to use the information (2.11).

Lemma 2.3.2. Consider a r.v. \(Y \geq 0\) which satisfies
\[
\forall u>0, \mathrm{P}(Y \geq u) \leq A \exp \left(-\frac{u^{2}}{B^{2}}\right)
\]
for certain numbers \(A \geq 2\) and \(B>0\). Then
\[
\mathrm{E} Y \leq L B \sqrt{\log A}
\]

Here, as in the entire book, \(L\) denotes a universal constant. \({ }^{5}\) We make the convention that this constant is not necessarily the same on each occurrence (even in the same equation). This should be remembered at all times. One of the benefits of the convention (as opposed to writing explicit constants) is to make clear that one is not interested in getting sharp constants. Getting sharp constants might be useful for certain applications, but it is a different game. \({ }^{6}\) The convention is very convenient, but one needs to get used to it. Now is the time for this, so we urge the reader to pay the greatest attention to the next exercise.

Exercise 2.3.3. (a) Prove that for \(x, y \in \mathbb{R}^{+}\)we have \(x y-L x^{3} \leq L y^{3 / 2}\). (Please understand this statement as: given a number \(L_{1}\), there exists a number \(L_{2}\) such that for all \(x, y \in \mathbb{R}\) we have \(x y-L_{1} x^{1 / 3} \leq L_{2} y^{3 / 2}\).)
(b) Consider a function \(p(u) \leq 1\) for \(u \geq 0\). Assume that for \(u>L\) we have \(p(u) \leq L \exp \left(-u^{2} / L\right)\). Prove that for all \(u>0\) we have \(p(L u) \leq 2 \exp \left(-u^{2}\right)\). (Of course, this has to be understood as follows: assume that for a certain number \(L_{1}\), for \(u>L_{1}\) we have \(p(u) \leq L_{1} \exp \left(-u^{2} / L_{1}\right)\). Prove that there exists a number \(L_{2}\) such that for all \(u>0\) we have \(p\left(L_{2} u\right) \leq \exp \left(-u^{2}\right)\).)
(c) Consider an integer \(N \geq 1\). Prove that
\[
N^{L} \exp \left(-(\log N)^{3 / 2} / L\right) \leq L \exp \left(-(\log N)^{3 / 2} / L\right)
\]

Proof of Lemma 2.3.2. We use (2.6) and we observe that since \(\mathrm{P}(Y \geq u) \leq 1\), for any number \(u_{0}\) we have

\footnotetext{
\({ }^{5}\) When meeting an unknown notation such as this previous \(L\), the reader might try to look at the index, where some of the most common notation is recorded.
\({ }^{6}\) Our methods here are not appropriate for this.
}
\[
\begin{aligned}
\mathrm{E} Y=\int_{0}^{\infty} \mathrm{P}(Y \geq u) \mathrm{d} u & =\int_{0}^{u_{0}} \mathrm{P}(Y \geq u) \mathrm{d} u+\int_{u_{0}}^{\infty} \mathrm{P}(Y \geq u) \mathrm{d} u \\
& \leq u_{0}+\int_{u_{0}}^{\infty} A \exp \left(-\frac{u^{2}}{B^{2}}\right) \mathrm{d} u \\
& \leq u_{0}+\frac{1}{u_{0}} \int_{u_{0}}^{\infty} u A \exp \left(-\frac{u^{2}}{B^{2}}\right) \mathrm{d} u \\
& =u_{0}+\frac{A B^{2}}{2 u_{0}} \exp \left(-\frac{u_{0}^{2}}{B^{2}}\right)
\end{aligned}
\]

The choice of \(u_{0}=B \sqrt{\log A}\) gives the bound
\[
B \sqrt{\log A}+\frac{B}{2 \sqrt{\log A}} \leq L B \sqrt{\log A}
\]
since \(A \geq 2\).
Next, recalling that the process \(\left(X_{t}\right)_{t \in T}\) is assumed to satisfy (2.4) throughout the section, we claim that
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \Delta(T) \sqrt{\log \operatorname{card} T}
\]

Indeed, this is obvious if card \(T=1\). If card \(T \geq 2\) it follows from (2.11) that (2.12) holds for \(Y=\sup _{t \in T}\left(X_{t}-X_{t_{0}}\right)\) with \(A=2 \operatorname{card} T\) and \(B=\Delta(T)\), and the result follows from (2.13) since \(\log (2 \operatorname{card} T) \leq 2 \log \operatorname{card} T\) and \(\mathrm{E} Y=\) \(\mathrm{E} \sup _{t \in T} X_{t}\).

The following special case is fundamental.
Lemma 2.3.4. If \(\left(g_{k}\right)_{k \geq 1}\) are standard Gaussian r.v.s then
\[
\mathrm{E} \sup _{k \leq N} g_{k} \leq L \sqrt{\log N}
\]

Exercise 2.3.5. (a) Prove that (2.16) holds for any r.v.s \(g_{k}\) which satisfy
\[
\mathrm{P}\left(g_{k} \geq u\right) \leq 2 \exp \left(-\frac{u^{2}}{2}\right)
\]
for \(u>0\).
(b) For \(N \geq 2\) construct \(N\) centered r.v.s \(\left(g_{k}\right)_{k \leq N}\) satisfying (2.17), and taking only the values \(0, \pm \sqrt{\log N}\) and for which \(\operatorname{Esup}_{k \leq N} g_{k} \geq \sqrt{\log N} / L\). (You are not yet asked to make these r.v.s independent.)
(c) After learning (2.18) below, solve (b) with the further requirement that the r.v.s \(g_{k}\) are independent. If this is too hard, look at Exercise 2.3.7, (b) below.

This is taking us a bit ahead, but an equally fundamental fact is that when the r.v.s \(\left(g_{k}\right)\) are jointly Gaussian, and "significantly different from each other"
i.e. \(\mathrm{E}\left(g_{k}-g_{\ell}\right)^{2} \geq a^{2}>0\) for \(k \neq \ell\), the bound (2.16) can be reversed, i.e. \(\operatorname{Esup}_{k \leq N} g_{k} \geq a \sqrt{\log N} / L\), a fact known as Sudakov's minoration. Sudakov's minoration is a non-trivial fact, and to understand it, it should be really useful to solve Exercise 2.3.7 below. However, before that let us point out a simple fact, that will be used many times.

Exercise 2.3.6. Consider independent events \(\left(A_{k}\right)_{k \geq 1}\). Prove that
\[
\mathrm{P}\left(\bigcup_{k \leq N} A_{k}\right) \geq 1-\exp \left(-\sum_{k \leq N} \mathrm{P}\left(A_{k}\right)\right)
\]

In words: independent events such that the sum of their probabilities is small are basically disjoint.

Exercise 2.3.7. (a) Consider independent r.v.s \(Y_{k} \geq 0\) and \(u>0\) with
\[
\sum_{k \leq N} \mathrm{P}\left(Y_{k} \geq u\right) \geq 1
\]

Prove that
\[
\mathrm{E} \sup _{k \leq N} Y_{k} \geq \frac{u}{L}
\]

Hint: use (2.18) to prove that \(\mathrm{P}\left(\sup _{k \leq N} Y_{k} \geq u\right) \geq 1 / L\).
(b) We assume (2.19), but now \(Y_{k}\) need not be \(\geq 0\). Prove that
\[
\mathrm{E} \sup _{k \leq N} Y_{k} \geq \frac{u}{L}-\mathrm{E}\left|Y_{1}\right|
\]

Hint: observe that for each event \(\Omega\) we have \(\mathrm{E} 1_{\Omega} \sup _{k} Y_{k} \geq-\mathrm{E}\left|Y_{1}\right|\).
(c) Prove that if \(\left(g_{k}\right)_{k \geq 1}\) are independent standard Gaussian r.v.s then \(\mathrm{E} \sup _{k \leq N} g_{k} \geq \sqrt{\log N} / L\).

Before we go back to our main story, we consider in detail the consequences of an "exponential decay of tails" such as in (2.12). This is the point of the next exercise.

Exercise 2.3.8. (a) Assume that for a certain \(B>0\) the r.v. \(Y \geq 0\) satisfies
\[
\forall u>0, \mathrm{P}(Y \geq u) \leq 2 \exp \left(-\frac{u}{B}\right)
\]

Prove that
\[
\mathrm{E} \exp \left(\frac{Y}{2 B}\right) \leq L
\]

Prove that for \(x, a>0\) one has \((x / a)^{a} \leq \exp x\). Use this for \(a=p\) and \(x=Y / 2 B\) to deduce from (2.21) that for \(p \geq 1\) one has
\[
\left(\mathrm{E} Y^{p}\right)^{1 / p} \leq L p B
\]
(b) Assuming now that for a certain \(B>0\) one has
\[
\forall u>0, \mathrm{P}(Y \geq u) \leq 2 \exp \left(-\frac{u^{2}}{B^{2}}\right)
\]
prove similarly (or deduce from (a)) that \(\operatorname{Eexp}\left(Y^{2} / 2 B^{2}\right) \leq L\), and that for \(p \geq 1\) one has
\[
\left(\mathrm{E} Y^{p}\right)^{1 / p} \leq L B \sqrt{p}
\]
(c) Consider a r.v. \(Y \geq 0\) and a number \(B>0\). Assuming that for \(p \geq 1\) we have \(\left(\mathrm{E} Y^{p}\right)^{1 / p} \leq B p\) prove that for \(u>0\) we have \(\mathrm{P}(Y>u) \leq\) \(2 \exp (-u /(L B))\). Assuming that for each \(p \geq 1\) we have \(\left(\mathrm{EY}^{p}\right)^{1 / p} \leq B \sqrt{p}\) prove that for \(u>0\) we have \(\mathrm{P}(Y>u) \leq 2 \exp \left(-u^{2} /\left(L B^{2}\right)\right)\).

In words, (2.22) states that "as \(p\) increases, the \(L^{p}\) norm of an exponentially integrable r.v. does not grow faster than \(p\)," and (2.24) asserts that if the square of the r.v. is exponentially integrable, then its \(L^{p}\) norm does not grow faster than \(\sqrt{p}\). These two statements are closely related. More generally it is very classical to relate the size of the tails of a r.v. with the rate of growth of its \(L^{p}\) norm. This is not explicitly used in the sequel, but is good to know as background information. As the following shows, (2.24) provides the correct rate of growth in the case of Gaussian r.v.s.

Exercise 2.3.9. If \(g\) is a standard Gaussian r.v. it follows from (2.24) that for \(p \geq 1\) one has \(\left(\mathrm{E}|g|^{p}\right)^{1 / p} \leq L \sqrt{p}\). Prove one has also
\[
\left(\mathrm{E}|g|^{p}\right)^{1 / p} \geq \frac{\sqrt{p}}{L}
\]

One knows how to compute exactly \(\mathrm{E}|g|^{p}\), from which one can deduce (2.25). You are however asked to provide a proof in the spirit of this work by deducing (2.25) solely from the information that, say, for \(u>0\) we have (choosing on purpose crude constants) \(\mathrm{P}(|g| \geq u) \geq \exp \left(-u^{2} / 3\right) / 100\).

You will find basically no exact computations in this book. The aim is different. We study quantities which are far too complicated to be computed exactly, and we try to bound them from above, and sometimes from below by simpler quantities with as little a gap as possible between the upper and the lower bounds. Ideally the gap is only a (universal) multiplicative constant.

\subsection*{2.4 The Generic Chaining}

We go back to our main story. The bound (2.9) (and hence (2.15)) will be effective if the variables \(X_{t}-X_{t_{0}}\) are rather uncorrelated (and if there are not too many of them). But it will be a disaster if many of the variables \(\left(X_{t}\right)_{t \in T}\) are nearly identical. Thus it seems a good idea to gather those variables \(X_{t}\) which are nearly identical. To do this, we consider a subset \(T_{1}\) of \(T\), and
for \(t\) in \(T\) we consider a point \(\pi_{1}(t)\) in \(T_{1}\), which we think of as a (first) approximation of \(t\). The elements of \(T\) which correspond to the same point \(\pi_{1}(t)\) are, at this level of approximation, considered as identical. We then write
\[
X_{t}-X_{t_{0}}=X_{t}-X_{\pi_{1}(t)}+X_{\pi_{1}(t)}-X_{t_{0}}
\]

The idea is that it will be effective to use (2.9) for the variables \(X_{\pi_{1}(t)}-X_{t_{0}}\), because there are not too many of them and, if we have done a good job at finding \(\pi_{1}(t)\), they are rather different from each other (at least in some global sense). On the other hand, since \(\pi_{1}(t)\) is an approximation of \(t\), the variables \(X_{t}-X_{\pi_{1}(t)}\) are "smaller" than the original variables \(X_{t}-X_{t_{0}}\), so that their supremum should be easier to handle. The procedure will then be iterated.

Let us set up the general procedure. For \(n \geq 0\), we consider a subset \(T_{n}\) of \(T\), and for \(t \in T\) we consider \(\pi_{n}(t)\) in \(T_{n}\). (The idea is that the points \(\pi_{n}(t)\) are successive approximations of \(t\).) We assume that \(T_{0}\) consists of a single element \(t_{0}\), so that \(\pi_{0}(t)=t_{0}\) for each \(t\) in \(T\). The fundamental relation is
\[
X_{t}-X_{t_{0}}=\sum_{n \geq 1}\left(X_{\pi_{n}(t)}-X_{\pi_{n-1}(t)}\right)
\]
which holds provided we arrange that \(\pi_{n}(t)=t\) for \(n\) large enough, in which case the series is actually a finite sum. Relation (2.27) decomposes the increments of the process \(X_{t}-X_{t_{0}}\) along the "chain" \(\left(\pi_{n}(t)\right)_{n \geq 0}\) (and this is why this method is called "chaining").

It will be convenient to control the set \(T_{n}\) through its cardinality with the condition
\[
\operatorname{card} T_{n} \leq N_{n}
\]
where
\[
N_{0}=1 ; N_{n}=2^{2^{n}} \text { if } n \geq 1
\]

The notation (2.29) will be used throughout the book. It is at this stage that the procedure to control \(T_{n}\) differs from the traditional one, and it is the crucial point of the generic chaining method.

It is good to notice right away that \(\sqrt{\log N_{n}}\) is about \(2^{n / 2}\), which will explain the ubiquity of this latter quantity. The occurrence of the function \(\sqrt{\log x}\) itself is related to the fact that it is the inverse of the function \(\exp \left(x^{2}\right)\), and that the function \(\exp \left(-x^{2}\right)\) governs the size of the tails of a Gaussian r.v. Let us also observe the fundamental inequality
\[
N_{n}^{2} \leq N_{n+1}
\]
which makes it very convenient to work with this sequence.
Since \(\pi_{n}(t)\) approximates \(t\), it is natural to assume that \({ }^{7}\)

\footnotetext{
\({ }^{7}\) The notation \(:=\) below stresses that this is a definition, so that you should not worry that your memory failed and that you did not see this before.
}
\[
d\left(t, \pi_{n}(t)\right)=d\left(t, T_{n}\right):=\inf _{s \in T_{n}} d(t, s)
\]

For \(u>0\), (2.4) implies
\[
\mathrm{P}\left(\left|X_{\pi_{n}(t)}-X_{\pi_{n-1}(t)}\right| \geq u 2^{n / 2} d\left(\pi_{n}(t), \pi_{n-1}(t)\right)\right) \leq 2 \exp \left(-u^{2} 2^{n-1}\right)
\]

The number of possible pairs ( \(\pi_{n}(t), \pi_{n-1}(t)\) ) is bounded by
\[
\operatorname{card} T_{n} \cdot \operatorname{card} T_{n-1} \leq N_{n} N_{n-1} \leq N_{n+1}=2^{2^{n+1}}
\]

We define the (favorable) event \(\Omega_{u, n}\) by
\[
\forall t,\left|X_{\pi_{n}(t)}-X_{\pi_{n-1}(t)}\right| \leq u 2^{n / 2} d\left(\pi_{n}(t), \pi_{n-1}(t)\right)
\]
and we define \(\Omega_{u}=\bigcap_{n \geq 1} \Omega_{u, n}\). Then
\[
p(u):=\mathrm{P}\left(\Omega_{u}^{c}\right) \leq \sum_{n \geq 1} \mathrm{P}\left(\Omega_{u, n}^{c}\right) \leq \sum_{n \geq 1} 2 \cdot 2^{2^{n+1}} \exp \left(-u^{2} 2^{n-1}\right)
\]

Here again, at the crucial step, we have used the union bound: \(\mathrm{P}\left(\Omega_{u}^{c}\right) \leq\) \(\sum_{n \geq 1} \mathrm{P}\left(\Omega_{u, n}^{c}\right)\). When \(\Omega_{u}\) occurs, (2.27) yields
\[
\left|X_{t}-X_{t_{0}}\right| \leq u \sum_{n \geq 1} 2^{n / 2} d\left(\pi_{n}(t), \pi_{n-1}(t)\right)
\]
so that
\[
\sup _{t \in T}\left|X_{t}-X_{t_{0}}\right| \leq u S
\]
where
\[
S:=\sup _{t \in T} \sum_{n \geq 1} 2^{n / 2} d\left(\pi_{n}(t), \pi_{n-1}(t)\right)
\]

Thus
\[
\mathrm{P}\left(\sup _{t \in T}\left|X_{t}-X_{t_{0}}\right|>u S\right) \leq p(u)
\]

For \(n \geq 1\) and \(u \geq 3\) we have
\[
u^{2} 2^{n-1} \geq \frac{u^{2}}{2}+u^{2} 2^{n-2} \geq \frac{u^{2}}{2}+2^{n+1}
\]
from which it follows that
\[
p(u) \leq L \exp \left(-\frac{u^{2}}{2}\right)
\]

We observe here that since \(p(u) \leq 1\) the previous inequality holds not only for \(u \geq 3\) but also for \(u>0\), because \(1 \leq \exp (9 / 2) \exp \left(-u^{2} / 2\right)\) for \(u \leq 3\). This type of argument (i.e. changing the universal constant in front of the exponential, cf. Exercise (2.3.3)(b)) will be used repeatedly. Therefore
\[
\mathrm{P}\left(\sup _{t \in T}\left|X_{t}-X_{t_{0}}\right|>u S\right) \leq L \exp \left(-\frac{u^{2}}{2}\right) .
\]

In particular (2.33) implies
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L S
\]

The triangle inequality yields
\[
d\left(\pi_{n}(t), \pi_{n-1}(t)\right) \leq d\left(t, \pi_{n}(t)\right)+d\left(t, \pi_{n-1}(t)\right)=d\left(t, T_{n}\right)+d\left(t, T_{n-1}\right)
\]
so that (making the change of variable \(n=n^{\prime}+1\) in the second sum below)
\[
S \leq \sup _{t \in T} \sum_{n \geq 1} 2^{n / 2} d\left(t, T_{n}\right)+\sup _{t \in T} \sum_{n \geq 1} 2^{n / 2} d\left(t, T_{n-1}\right) \leq 3 \sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right)
\]
and we have proved the fundamental bound
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right)
\]

Now, how do we construct the sets \(T_{n}\) ? It is obvious that we should try to make the right-hand side of (2.34) small, but this is obvious only because we have used an approach which naturally leads to this bound. In the next section, we investigate how this was traditionally done. Before this, we urge the reader to fully understand the next exercise. It will be crucial to understand a typical case where the traditional methods are not effective.

Exercise 2.4.1. Consider a countable metric space, \(T=\left\{t_{1}, t_{2}, \ldots\right\}\). Assume that for each \(i \geq 2\) we have \(d\left(t_{1}, t_{i}\right) \leq 1 / \sqrt{\log i}\). Prove that if \(T_{n}=\left\{t_{1}, t_{2}, \ldots, t_{N_{n}}\right\}\) then for each \(t \in T\) we have \(\sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right) \leq L\).

We end this section by reviewing at a high level the scheme of the previous proof (which will be used again and again). The goal is to bound EY where \(Y\) is a r.v. \(\geq 0\) (here \(Y=\sup _{t}\left(X_{t}-X_{t_{0}}\right)\).) The method consists of two steps:
- Given a parameter \(u \geq 0\) one identifies a "good set" \(\Omega_{u}\), where some undesirable events do not happen. As \(u\) becomes large \(\mathrm{P}\left(\Omega_{u}^{c}\right)\) becomes small.
- When \(\Omega_{u}\) occurs we bound \(Y\), say \(Y \leq f(u)\) where \(f\) is an increasing function on \(\mathbb{R}^{+}\).

One then obtains the bound
\[
\begin{aligned}
\mathrm{E} Y=\int_{0}^{\infty} \mathrm{P}(Y \geq u) \mathrm{d} u \leq f(0) & +\int_{f(0)}^{\infty} \mathrm{P}(Y \geq u) \mathrm{d} u \\
& =f(0)+\int_{0}^{\infty} f^{\prime}(u) \mathrm{P}(Y \geq f(u)) \mathrm{d} u
\end{aligned}
\]
where we have used a change of variable in the last equality. Now, since \(Y \leq f(u)\) on \(\Omega_{u}\), we have \(\mathrm{P}(Y \geq f(u)) \leq \mathrm{P}\left(\Omega_{u}^{c}\right)\) and finally
\[
\mathrm{E} Y \leq f(0)+\int_{0}^{\infty} f^{\prime}(u) \mathrm{P}\left(\Omega_{u}^{c}\right) \mathrm{d} u
\]

In practice, we will always have \(\mathrm{P}\left(\Omega_{u}^{c}\right) \leq L \exp (-u / L)\) and \(f(u)=A+\) \(u^{\alpha} B\), yielding the bound \(\mathrm{E} Y \leq A+K(\alpha) B\).

\subsection*{2.5 Entropy Numbers}

For a number of years, chaining was systematically performed (as in Section 1.4) by choosing the sets \(T_{n}\) so that \(\sup _{t \in T} d\left(t, T_{n}\right)\) is as small as possible for \(\operatorname{card} T_{n} \leq N_{n}\). We define
\[
e_{n}(T)=e_{n}(T, d)=\inf _{T_{n} \subset T, \operatorname{card} T_{n} \leq N_{n}} \sup _{t \in T} d\left(t, T_{n}\right)
\]
where the infimum is taken over all subsets \(T_{n}\) of \(T\) with card \(T_{n} \leq N_{n}\). (Since here \(T\) is finite, the infimum is actually a minimum.) We call the numbers \(e_{n}(T)\) the entropy numbers.

Let us recall that in a metric space a (closed) ball is a set of the type \(B(t, r)=\{s \in T ; d(s, t) \leq r\}\). Balls are basic sets in a metric space and will be of constant use. It should be obvious to reformulate (2.36) as follows: \(e_{n}(T)\) is the infimum of the set of numbers \(r \geq 0\) such that \(T\) can be covered by \(\leq N_{n}\) balls of radius \(\leq r\) (the set \(T_{n}\) in (2.36) being the set of centers of these balls).

The definition (2.36) is not consistent with the conventions of Operator Theory, which uses \(e_{2^{n}}\) to denote what we call \(e_{n} .{ }^{8}\) When \(T\) is infinite, the numbers \(e_{n}(T)\) are also defined by (2.36) but are not always finite (e.g. when \(T=\mathbb{R}\) ).

Let us note that, since \(N_{0}=1\),
\[
\frac{\Delta(T)}{2} \leq e_{0}(T) \leq \Delta(T)
\]

Recalling that \(T\) is finite, let us then choose for each \(n\) a subset \(T_{n}\) of \(T\) with \(\operatorname{card} T_{n} \leq N_{n}\) and \(e_{n}(T)=\sup _{t \in T} d\left(t, T_{n}\right)\). Since \(d\left(t, T_{n}\right) \leq e_{n}(T)\) for each \(t\), (2.34) implies the following.

Proposition 2.5.1 (Dudley's entropy bound [38]). Under the increment condition (2.4), it holds that
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \sum_{n \geq 0} 2^{n / 2} e_{n}(T)
\]

\footnotetext{
\({ }^{8}\) We can't help it if Operator Theory gets it wrong.
}

We proved this bound only when \(T\) is finite, but using (2.2) it also extends to the case where \(T\) is infinite, as is shown by the following easy fact.

Lemma 2.5.2. If \(U\) is a subset of \(T\), we have \(e_{n}(U) \leq 2 e_{n}(T)\).
The point here is that in the definition of \(e_{n}(U)\) we insist that the balls are centered in \(U\), not in \(T\).

Proof. Indeed, if \(a>e_{n}(T)\), by definition one can cover \(T\) by \(N_{n}\) balls (for the distance \(d\) ) with radius \(a\), and the intersections of these balls with \(U\) are of diameter \(\leq 2 a\), so \(U\) can be covered by \(N_{n}\) balls in \(U\) with radius \(2 a\).

Exercise 2.5.3. Prove that the factor 2 in the inequality \(e_{n}(U) \leq 2 e_{n}(T)\) cannot be improved even if \(n=0\).

Dudley's entropy bound is usually formulated using the covering numbers of Definition 1.4.1. These relate to the entropy numbers by the formula
\[
e_{n}(T)=\inf \left\{\epsilon ; N(T, d, \epsilon) \leq N_{n}\right\} .
\]

Indeed, it is obvious by definition of \(e_{n}(T)\) that for \(\epsilon>e_{n}(T)\), we have \(N(T, d, \epsilon) \leq N_{n}\), and that if \(N(T, d, \epsilon) \leq N_{n}\) we have \(e_{n}(T) \leq \epsilon\). Consequently,
\[
\begin{aligned}
\epsilon<e_{n}(T) & \Rightarrow N(T, d, \epsilon)>N_{n} \\
& \Rightarrow N(T, d, \epsilon) \geq 1+N_{n}
\end{aligned}
\]

Therefore
\[
\sqrt{\log \left(1+N_{n}\right)}\left(e_{n}(T)-e_{n+1}(T)\right) \leq \int_{e_{n+1}(T)}^{e_{n}(T)} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon
\]

Since \(\log \left(1+N_{n}\right) \geq 2^{n} \log 2\) for \(n \geq 0\), summation over \(n \geq 0\) yields
\[
\sqrt{\log 2} \sum_{n \geq 0} 2^{n / 2}\left(e_{n}(T)-e_{n+1}(T)\right) \leq \int_{0}^{e_{0}(T)} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon
\]

Now,
\[
\begin{aligned}
\sum_{n \geq 0} 2^{n / 2}\left(e_{n}(T)-e_{n+1}(T)\right) & =\sum_{n \geq 0} 2^{n / 2} e_{n}(T)-\sum_{n \geq 1} 2^{(n-1) / 2} e_{n}(T) \\
& \geq\left(1-\frac{1}{\sqrt{2}}\right) \sum_{n \geq 0} 2^{n / 2} e_{n}(T)
\end{aligned}
\]
so (2.39) yields
\[
\sum_{n \geq 0} 2^{n / 2} e_{n}(T) \leq L \int_{0}^{e_{0}(T)} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon
\]

Hence Dudley's bound now appears in the familiar form
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \int_{0}^{\infty} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon
\]

Here, since \(\log 1=0\), the integral takes place in fact over \(0 \leq \epsilon \leq e_{0}(T)\). The right-hand side is often called Dudley's entropy integral.

Exercise 2.5.4. Prove that
\[
\int_{0}^{\infty} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon \leq L \sum_{n \geq 0} 2^{n / 2} e_{n}(T)
\]
showing that (2.38) is not an improvement over (2.41).
Exercise 2.5.5. Assume that for each \(0<\epsilon<A\) and some \(\alpha>0\) we have \(\log N(T, d, \epsilon) \leq(A / \epsilon)^{\alpha}\). Prove that \(e_{n}(T) \leq K(\alpha) A 2^{-n / \alpha}\).

Here \(K(\alpha)\) is a number depending only on \(\alpha .{ }^{9}\) This, and similar notation are used throughout the book. It is understood that such numbers need not be the same on every occurrence, and it would help to remember this at all times. The difference between the notations \(K\) and \(L\) is that \(L\) is a universal constant, i.e. a number that does not depend on anything, while \(K\) might depend on some parameters, such as \(\alpha\) here.

When writing a bound such as (2.41), the immediate question is: how sharp is it? The word "sharp" is commonly used, even though people do not agree on what it means exactly. Let us say that a bound of the type \(A \leq L B\) can be reversed if it is true that \(B \leq L A\). We are not concerned with the value of the universal constants. \({ }^{10}\) Inequalities which can be reversed are our best possible goal. Then, in any circumstance, \(A\) and \(B\) are of the same order.

We give now a simple (and classical) example that illustrates well the difference between Dudley's bound (2.38) and the bound (2.34) and which shows in particular that Dudley's bound cannot be reversed. Consider an independent sequence \(\left(g_{i}\right)_{i \geq 1}\) of standard Gaussian r.v.s. Set \(X_{1}=0\) and for \(i \geq 2\) set
\[
X_{i}=\frac{g_{i}}{\sqrt{\log i}}
\]

Consider an integer \(s \geq 3\) and the process \(\left(X_{i}\right)_{1 \leq i \leq N_{s}}\) so the index set is \(T=\) \(\left\{1,2, \ldots, N_{s}\right\}\). The distance \(d\) associated to the process, given by \(d(i, j)^{2}=\) \(\mathrm{E}\left(X_{s}-X_{t}\right)^{2}\), satisfies for \(i, j \geq 2, i \neq j\),

\footnotetext{
\({ }^{9}\) It just happens that in this particular case \(K(\alpha)=1\) works, but we typically do not care about the precise dependence of \(K(\alpha)\) on \(\alpha\).
\({ }^{10}\) Not that these values are unimportant, but our methods are not appropriate for this.
}
\[
\frac{1}{\sqrt{\log (\min (i, j))}} \leq d(i, j) \leq \frac{2}{\sqrt{\log (\min (i, j))}}
\]

Consider \(1 \leq n \leq s-1\) and \(T_{n} \subset T\) with \(\operatorname{card} T_{n}=N_{n}\). There exists \(i \leq N_{n}+1\) with \(i \notin T_{n}\). Then (2.43) implies that \(d(i, j) \geq 2^{-n / 2} / L\) for \(j \in T_{n}\). This proves that the balls of radius \(2^{-n / 2} / L\) centered on \(T_{n}\) do not cover \(T\), so that \(e_{n}(T) \geq 2^{-n / 2} / L\). Therefore
\[
\sum_{n} 2^{n / 2} e_{n}(T) \geq \frac{s-1}{L}
\]

In the reverse direction, since for \(i \geq 1\) we have \(d(1, i) \leq 1 / \sqrt{\log i}\), Exercise 2.4.1 proves that the bound (2.34) is \(\leq L\). Thus the bound (2.38) is worse than the bound (2.34) by a factor about \(s\).

Exercise 2.5.6. Prove that when \(T\) is finite, the bound (2.41) cannot be worse than (2.34) by a factor greater than about \(\log \log \operatorname{card} T\). This shows that the previous example is in a sense extremal. Hint: use \(2^{n / 2} e_{n}(T) \leq\) \(L \sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right)\) and \(e_{n}(T)=0\) if \(N_{n} \geq \operatorname{card} T\).

How does one estimate covering numbers (or, equivalently, entropy numbers)? Let us first stress a trivial but nonetheless fundamental fact.
Lemma 2.5.7. Consider a number \(\epsilon>0\) and a subset \(W\) of \(T\) maximal with respect to the property
\[
s, t \in W \Rightarrow d(s, t)>\epsilon
\]

Then \(N(T, d, \epsilon) \leq \operatorname{card} W\).
Proof. Since \(W\) is maximum the balls of radius \(a\) centered at the points of \(W\) cover \(T\).

Exercise 2.5.8. Consider a probability measure \(\mu\) on \(T\), a number \(\epsilon>0\) and a number \(a\). Let \(U=\{t \in T ; \mu(B(t, \epsilon) \geq a\}\). Prove that \(N(U, d, 2 \epsilon) \leq 1 / a\).

The next exercise introduces the reader to "volume estimates", a simple yet fundamental method for this purpose. It deserves to be fully understood. If this exercise is too hard, you can find all the details below in the proof of Lemma 2.13.7.

Exercise 2.5.9. (a) If ( \(T, d\) ) is a metric space, define the packing number \(N^{*}(T, d, \epsilon)\) as the largest integer \(N\) such that \(T\) contains \(N\) points with mutual distances \(\geq \epsilon\). Prove that \(N(T, d, \epsilon) \leq N^{*}(T, d, \epsilon)\). Prove that if \(\epsilon^{\prime}>2 \epsilon\) then \(N^{*}\left(T, d, \epsilon^{\prime}\right) \leq N(T, d, \epsilon)\).
(b) Consider a distance \(d\) on \(\mathbb{R}^{k}\) which arises from a norm \(\|\cdot\|, d(x, y)=\) \(\|x-y\|\), and denote by \(B\) the unit ball of center 0 . Let us denote by \(\operatorname{Vol}(A)\) the \(k\)-dimensional volume of a subset \(A\) of \(\mathbb{R}^{k}\). By comparing volumes, prove that for any subset \(A\) of \(\mathbb{R}^{k}\),
\[
N(A, d, \epsilon) \geq \frac{\operatorname{Vol}(A)}{\operatorname{Vol}(\epsilon B)}
\]
and
\[
N(A, d, 2 \epsilon) \leq N^{*}(A, d, 2 \epsilon) \leq \frac{\operatorname{Vol}(A+\epsilon B)}{\operatorname{Vol}(\epsilon B)}
\]
(c) Conclude that
\[
\left(\frac{1}{\epsilon}\right)^{k} \leq N(B, d, \epsilon) \leq\left(\frac{2+\epsilon}{\epsilon}\right)^{k}
\]
(d) Use (c) to find estimates of \(e_{n}(B)\) of the correct order for each value of \(n\). Hint: \(e_{n}(B)\) is about \(2^{-2^{n} / k}\). This decreases very fast as \(n\) increases. Estimate Dudley's bound for \(B\) provided with the distance \(d\).
(e) Prove that if \(T\) is a subset of \(\mathbb{R}^{k}\) and if \(n_{0}\) is any integer then for \(n \geq n_{0}\) one has \(e_{n+1}(T) \leq L 2^{-2^{n} / k} e_{n_{0}}(T)\). Hint: cover \(T\) by \(N_{n_{0}}\) balls of radius \(2 e_{n_{0}}(T)\) and cover each of these by balls of smaller radius using (d).
(f) This part provides a generalization of (2.45) and (2.46) to a more abstract setting, but with the same proofs. Consider a metric space ( \(T, d\) ) and a positive measure \(\mu\) on \(T\) such that all balls of a given radius have the same measure, \(\mu(B(t, \epsilon))=\varphi(\epsilon)\) for each \(\epsilon>0\) and each \(t \in T\). For a subset \(A\) of \(T\) and \(\epsilon>0\) let \(A_{\epsilon}=\{t \in T ; d(t, A) \leq \epsilon\}\), where \(d(t, A)=\inf _{s \in A} d(t, s)\). Prove that
\[
\frac{\mu(A)}{\varphi(2 \epsilon)} \leq N(A, d, 2 \epsilon) \leq \frac{\mu\left(A_{\epsilon}\right)}{\varphi(\epsilon)}
\]

There are many simple situations where Dudley's bound is not of the correct order. We gave a first example on page 35. We give such another example in Exercise 2.5.11 below. There the set \(T\) is particularly appealing: it is a simplex in \(\mathbb{R}^{m}\). Yet other examples based on fundamental geometry (ellipsoids in \(\mathbb{R}^{k}\) ) are explained in Section 2.13.

The result of the following exercise is very useful in all kinds of examples.
Exercise 2.5.10. Consider two integers \(k, m\) with \(k \leq m / 4\). Assume for simplicity that \(k\) is even.
(a) Prove that
\[
\sum_{0 \leq \ell \leq k / 2}\binom{m}{\ell} \leq 2\left(\frac{2 k}{m}\right)^{k / 2}\binom{m}{k}
\]
(b) Denote by \(\mathcal{I}\) the class of subsets of \(\{1, \ldots, m\}\) of cardinality \(k\). Prove that you can find in \(\mathcal{I}\) a family \(\mathcal{F}\) such for \(I, J \in \mathcal{F}\) one has \(\operatorname{card}(I \backslash J) \cup(J \backslash I) \geq k / 2\) and \(\operatorname{card} \mathcal{F} \geq(m /(2 k))^{k / 2} / 2\). Hint: Use (a) and part (f) of Exercise 2.5.9 for \(\mu\) the counting measure on \(\mathcal{I}\). Warning: this is not so easy.

Exercise 2.5.11. Consider an integer \(m\) and an i.i.d. standard Gaussian sequence \(\left(g_{i}\right)_{i \leq m}\). For \(t=\left(t_{i}\right)_{i \leq m} \in \mathbb{R}^{m}\), let \(X_{t}=\sum_{i \leq m} t_{i} g_{i}\). This is called the
canonical Gaussian process on \(\mathbb{R}^{m}\). Its associated distance is the Euclidean distance on \(\mathbb{R}^{m}\). It will be much used later. Consider the set
\[
T=\left\{\left(t_{i}\right)_{i \leq m} \in \mathbb{R}^{m} ; t_{i} \geq 0, \sum_{i \leq m} t_{i}=1\right\}
\]
the convex hull of the canonical basis. By (2.16) we have \(\mathrm{Esup}_{t \in T} X_{t}=\) \(\mathrm{Esup}_{i \leq m} g_{i} \leq L \sqrt{\log m}\). Prove, however, that the right-hand side of (2.41) is \(\geq(\log m)^{3 / 2} / L\). (Hint: For an integer \(k \leq m\) consider the subset \(T_{k}\) of \(T\) consisting of sequences \(t=\left(t_{i}\right)_{i \leq m} \in T\) for which \(t_{i} \in\{0,1 / k\}\), so that \(t \in T_{k}\) is determined by the set \(I=\left\{i \leq m ; t_{i}=1 / k\right\}\) and card \(I=k\). Using Exercise 2.5.10 prove that \(\log N\left(T_{k}, d, 1 /(L \sqrt{k})\right) \geq k \log (e m / k) / L\) and conclude. \({ }^{11}\) ) Thus in this case Dudley's bound is off by a multiplicative factor of about \(\log m\). Exercise 2.7.9 below will show that in \(\mathbb{R}^{m}\) the situation cannot be worse than this.

\subsection*{2.6 Rolling Up our Sleeves: Chaining in the Simplex}

The bound (2.34) seems to be genuinely better than the bound (2.38) because when going from (2.34) to (2.38) we have used the somewhat brutal inequality
\[
\sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right) \leq \sum_{n \geq 0} 2^{n / 2} \sup _{t \in T} d\left(t, T_{n}\right) .
\]

The method leading to the bound (2.34) is probably the most important idea of this work. The fact that it appears now so naturally does not reflect the history of the subject, but rather that the proper approach is being used. When using this bound, we will choose the sets \(T_{n}\) in order to minimize the right-hand side of (2.34) instead of choosing them as in (2.36). As we will demonstrate later, this provides essentially the best possible bound for \(\mathrm{E}_{\sup _{t \in T}} X_{t}\). It is remarkable that despite the fact that this result holds in complete generality, it is a non-trivial task to find sets \(T_{n}\) witnessing this, even in very simple situations. In the present situation we perform this task by an explicit construction for the set \(T\) of (2.49).

Proposition 2.6.1. There exists sets \(T_{n} \subset \mathbb{R}^{m}\) with \(\operatorname{card} T_{n} \leq N_{n}\) such that
\[
\sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right) \leq L \sqrt{\log m}\left(=L \mathrm{E} \sup _{t \in T} X_{t}\right)
\]

Of course here \(d\) is the Euclidean distance in \(\mathbb{R}^{m}\). The reader may try to find these sets herself before reading the rest of this section, as there seems to be

\footnotetext{
\({ }^{11}\) In case you wonder why \(e\) occurs in this formula, it is just to take care of the case where \(k\) is nearly \(m\). This term is not needed here, but is important in upper bounds of the same nature that we will use below.
}
no better way to get convinced of the depth of the present theory. The sets \(T_{n}\) are not subsets of \(T\). Please figure out by yourself how to correct this. \({ }^{12}\)

Lemma 2.6.2. For each \(t \in T\) we can find a sequence \((p(n, t))_{n \geq 0}\) of integers \(0 \leq p(n, t) \leq 2 n\) with the following properties:
\[
\begin{gathered}
\sum_{n \geq 0} 2^{n-p(n, t)} \leq L \\
\forall n \geq 0, p(n+1, t) \leq p(n, t)+2 \\
\operatorname{card}\left\{i \leq m ; t_{i} \geq 2^{-p(n, t)}\right\}<2^{n}
\end{gathered}
\]

Proof. There is no loss of generality to assume that the sequence \(\left(t_{i}\right)_{i \leq m}\) is non-increasing. We set \(t_{i}=0\) for \(i>m\). Then for any \(n \geq 1\) and \(2^{n-1}<\) \(i \leq 2^{n}\) we have \(t_{i} \geq t_{2^{n}}\), so that \(2^{n-1} t_{2^{n}} \leq \sum_{2^{n-1}<i<2^{n}} t_{i}\). By summation over \(n \geq 1\) we obtain \(\sum_{n \geq 1} 2^{n} t_{2^{n}} \leq 2\), and thus \(\sum_{n \geq 0} 2^{n} t_{2^{n}} \leq 3\). For \(n \geq\) 0 consider the largest integer \(q(n, t) \leq 2 n\) such that \(2^{-q(n, t)}>t_{2^{n}}\). Thus \(2^{-q(n, t)-1} \leq t_{2^{n}}\) when \(q<2 n\). In any case \(2^{-q(n, t)} \leq 2 t_{2^{n}}+2^{-2 n}\) and thus \(\sum_{n \geq 0} 2^{n-q(n, t)} \leq L\). Also if \(t_{i}>2^{-q(n, t)}>t_{2^{n}}\) then \(i<2^{n}\). In particular \(\operatorname{card}\left\{i \leq m ; t_{i}>2^{-q(t, n)}\right\}<2^{n}\). Finally we define
\[
p(n, t)=\min \{q(k, t)+2(n-k) ; 0 \leq k \leq n\}
\]

Taking \(k=n\) shows that \(p(n, t) \leq q(n, t) \leq 2 n\), implying (2.52). If \(k \leq n\) is such that \(p(n, t)=q(k, t)+2(n-k)\) then \(p(n+1, t) \leq q(k, t)+2(n+1-k)=\) \(p(n, t)+2\), proving (2.51). Also, since \(2^{n-p(n, t)} \leq \sum_{k \leq n} 2^{n-2(n-k)-q(k, t)}=\) \(\sum_{k \leq n} 2^{k-n+(k-q(k, t))}\) we have
\[
\sum_{n \geq 0} 2^{n-p(n, t)} \leq \sum_{k \geq 0} 2^{k-q(k, t)} \sum_{n \geq k} 2^{k-n} \leq L
\]

Given a set \(I \subset\{1, \ldots, m\}\) and a integer \(p\) we denote by \(V_{I, p}\) the set of elements \(u=\left(u_{i}\right)_{i \leq m} \in \mathbb{R}^{m}\) such that \(u_{i}=0\) if \(i \notin I\) and \(u_{i}=r_{i} 2^{-p}\) if \(i \in I\), where \(r_{i}\) is an integer \(0 \leq r_{i} \leq 3\). Then card \(V_{I, p} \leq 4^{\text {card } I}\). For \(n \geq 1\) we denote by \(V_{n}\) the union of all the sets \(V_{I, p}\) for card \(I \leq 2^{n}\) and \(0 \leq p \leq 2 n\). Crudely we have card \(V_{n} \leq m^{L 2^{n}}\). We set \(V_{0}=\{0\}\) and for \(n \geq 1\) we denote by \(U_{n}\) the set of all sums \(\sum_{0 \leq k \leq n} x_{k}\) where \(x_{k} \in V_{k}\). Then card \(U_{n} \leq m^{L 2^{n}}\).

Lemma 2.6.3. Consider \(t \in T\) and the sequence \((p(n, t))_{n \geq 0}\) constructed in Lemma 2.6.2. Then for each \(n\) we can write \(t=u(n)+v(n)\) where \(u(n) \in U_{n}\) and where \(v(n)=\left(v(n)_{i}\right)_{i \leq m}\) satisfies \(0 \leq v(n)_{i} \leq \min \left(t_{i}, 2^{-p(n, t)}\right)\).

Proof. The proof is by induction over \(n\). For \(n=0\) we set \(u(0)=0, v(0)=t\). For the induction from \(n\) to \(n+1\) consider the set \(I=\left\{i \leq m ; v(n)_{i}>\right.\)

\footnotetext{
\({ }^{12}\) The argument can be found in Section 2.14.
}
\(\left.2^{-p(n+1, t)}\right\}\). Since \(v(n)_{i} \leq t_{i}\) it follows from (2.52) that card \(I<2^{n+1}\). For each \(i \in I\) let \(r_{i}\) be the largest integer with \(r_{i} 2^{-p(n+1, t)}<v(n)_{i}\) so that \(v(n)_{i}-r_{i} 2^{-p(n+1, t)} \leq 2^{-p(n+1, t)}\). Since \(v(n)_{i} \leq 2^{-p(n, t)}\) by induction and since \(p(n+1, t) \leq p(n, t)+2\) by (2.51) we have \(r_{i} \leq 3\). Define \(u=\left(u_{i}\right)_{i \leq m} \in \mathbb{R}^{m}\) by \(u_{i}=r_{i} 2^{-p(n+1, t)}\) if \(i \in I\) and \(u_{i}=0\) otherwise. Then \(u \in V_{I, p(n+1, t)} \subset V_{n}\). Thus \(x=u(n+1)+v(n+1)\) where \(u(n+1):=u(n)+u \in U_{n+1}\) and \(v(n+1):=v(n)-u\) satisfies \(v(n+1)_{i} \leq \min \left(t_{i}, 2^{-p(n+1, t)}\right)\).

Lemma 2.6.4. For each \(t \in T\) we have \(\sum_{n \geq 0} 2^{n / 2} d\left(t, U_{n}\right) \leq L\).
Proof. Consider the sequence \((v(n))_{n \geq 0}\) constructed in Lemma 2.6.3, so that \(d\left(t, U_{n}\right) \leq\|v(n)\|_{2}\) since \(t=u(n)+\overline{v(n)}\). Let \(I_{n}=\left\{i \leq m ; t_{i}>2^{-p(n, t)}\right\}\) so that by (2.52) we have card \(I_{n}<2^{n}\). For \(n \geq 1\) set \(J_{n}=I_{n} \backslash I_{n-1}\) so that for \(i \in I_{n}\) we have \(t_{i}<2^{-p(n-1, t)}\). Then \(\|v(n)\|_{2}^{2}=\sum_{i \leq m} v(n)_{i}^{2}=\sum_{i \in I_{n}} v(n)_{i}^{2}+\) \(\sum_{k>n} \sum_{i \in J_{k}} v(n)_{i}^{2}\). Since \(v(n)_{i} \leq 2^{-p(n, t)}\) and card \(I_{n} \leq 2^{n}\) the first sum is \(\leq 2^{n / 2-p(n, t)}\). Since \(v(n)_{i} \leq t_{i} \leq 2^{-p(k-1, t)}\) for \(i \in J_{k}\) and card \(J_{k} \leq 2^{k}\) we have \(\sum_{i \in J_{k}} v(n)_{i}^{2} \leq 2^{k / 2-p(k-1, t)}\). Thus \(\|v(n)\|_{2} \leq \sum_{k \geq n} 2^{k / 2-p(k-1, t)}\) and
\[
\begin{aligned}
& \sum_{n \geq 1} 2^{n / 2}\|v(n)\|_{2} \leq \sum_{n \geq 1} \sum_{k \geq n} 2^{n / 2+k / 2-p(k-1, t)}=\sum_{k \geq 1} 2^{k / 2-p(k-1, t)} \sum_{n \leq k} 2^{n / 2} \\
& \leq L \sum_{k \geq 1} 2^{k-p(k-1, t)} \leq L,
\end{aligned}
\]
where we have used (2.50) in the last inequality.
Proof of Proposition 2.6.1. Consider the smallest integer \(k_{0}\) with \(m \leq N_{k_{0}}\) so that \(2^{k_{0} / 2} \leq L \sqrt{\log m}\). Observe also that \(m^{2^{n}} \leq\left(2^{2^{k_{0}}}\right)^{2^{n}}=2^{2^{k_{0}+n}}=N_{k_{0}+n}\). Thus card \(U_{n} \leq m^{L 2^{n}} \leq N_{k_{0}+n+k_{1}}\) where \(k_{1}\) is a universal constant. For \(n \geq k_{0}+k_{1}+1\) we set \(T_{n}=U_{n-n_{0}-k_{1}}\), so that \(\operatorname{card} T_{n} \leq N_{n}\). For \(n \leq k_{0}+k_{1}\) we set \(T_{n}=\{0\}\). Finally, given \(t \in T\) (and keeping in mind that \(k_{1}\) is a universal constant) we have
\[
\sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right) \leq L 2^{k_{0} / 2}+\sum_{n \geq k_{0}+k_{1}+1} 2^{n / 2} d\left(t, T_{n}\right)
\]
and, using Lemma 2.6.4 in the last inequality,
\[
\begin{aligned}
\sum_{n \geq k_{0}+k_{1}+1} 2^{n / 2} d\left(t, T_{n}\right)=\sum_{n \geq k_{0}+k_{1}+1} & 2^{n / 2} d\left(t, U_{n-k_{0}-k_{1}}\right) \\
& =\sum_{n \geq 1} 2^{\left(n+k_{0}+k_{1}\right) / 2} d\left(t, U_{n}\right) \leq L 2^{k_{0} / 2}
\end{aligned}
\]

\subsection*{2.7 Admissible Sequences of Partitions}

The idea behind the bound (2.34) admits a technically more convenient formulation. \({ }^{13}\)

Definition 2.7.1. Given a set \(T\) an admissible sequence is an increasing sequence \(\left(\mathcal{A}_{n}\right)_{n \geq 0}\) of partitions of \(T\) such that \(\operatorname{card} \mathcal{A}_{n} \leq N_{n}\), i.e. \(\operatorname{card} \mathcal{A}_{0}=1\) and \(\operatorname{card} \mathcal{A}_{n} \leq 2^{2^{n}}\) for \(n \geq 1\).

By an increasing sequence of partitions we mean that every set of \(\mathcal{A}_{n+1}\) is contained in a set of \(\mathcal{A}_{n}\). Admissible sequences of partitions will be constructed recursively, by breaking each element \(C\) of \(\mathcal{A}_{n}\) into at most \(N_{n}\) pieces, obtaining then a partition \(\mathcal{A}_{n+1}\) of \(T\) consisting of at most \(N_{n}^{2} \leq N_{n+1}\) pieces.

Throughout the book we denote by \(A_{n}(t)\) the unique element of \(\mathcal{A}_{n}\) which contains \(t\). The double exponential in the definition of \(N_{n}\) (see (2.29)) occurs simply since for our purposes the proper measure of the "size" of a partition \(\mathcal{A}\) is \(\log \operatorname{card} \mathcal{A}\). This double exponential ensures that "the size of the partition \(\mathcal{A}_{n}\) doubles at every step". This offers a number of technical advantages which will become clear gradually.

Theorem 2.7.2. (The generic chaining bound). Under the increment condition (2.4) (and if \(\mathrm{E} X_{t}=0\) for each \(t\) ), then for each admissible sequence ( \(\mathcal{A}_{n}\) ) we have
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right)
\]

Here as always, \(\Delta\left(A_{n}(t)\right)\) denotes the diameter of \(A_{n}(t)\) for \(d\). One could think that (2.54) could be much worse than (2.34), but it will turn out that this is not the case when the sequence ( \(\mathcal{A}_{n}\) ) is appropriately chosen.
Proof. We may assume \(T\) to be finite. We construct a subset \(T_{n}\) of \(T\) by taking exactly one point in each set \(A\) of \(\mathcal{A}_{n}\). Then for \(t \in T\) and \(n \geq 0\), we have \(d\left(t, T_{n}\right) \leq \Delta\left(A_{n}(t)\right)\) and the result follows from (2.34).

Definition 2.7.3. Given \(\alpha>0\), and a metric space ( \(T, d\) ) (that need not be finite) we define
\[
\gamma_{\alpha}(T, d)=\inf \sup _{t \in T} \sum_{n \geq 0} 2^{n / \alpha} \Delta\left(A_{n}(t)\right)
\]
where the infimum is taken over all admissible sequences.
It is useful to observe that since \(A_{0}(t)=T\) we have \(\gamma_{\alpha}(T, d) \geq \Delta(T)\). The most important cases by far are \(\alpha=2\) and \(\alpha=1\). For the time being we need only the case \(\alpha=2\). The case \(\alpha=1\) is first met in Theorem 4.5.13, although more general functionals occur first in Definition 4.5.

\footnotetext{
\({ }^{13}\) We will demonstrate why this is the case only later, in Theorem 4.5.13.
}

Exercise 2.7.4. Prove that if \(d \leq B d^{\prime}\) then \(\gamma_{2}(T, d) \leq B \gamma_{2}\left(T, d^{\prime}\right)\).
Exercise 2.7.5. Prove that \(\gamma_{\alpha}(T, d) \leq K(\alpha) \Delta(T)(\log \operatorname{card} T)^{1 / \alpha}\) when \(T\) is finite. Hint: Ensure that \(\Delta\left(A_{n}(t)\right)=0\) if \(N_{n} \geq \operatorname{card} T\).

A large part of our arguments will take place in abstract metric spaces, and this may represent an obstacle to the reader who has never thought about this. Therefore, we cannot recommend too highly the following exercise.

Exercise 2.7.6. (a) Consider a metric space ( \(T, d\) ) and assume that for each \(n \geq 0\) you are given a covering \(\mathcal{B}_{n}\) of \(T\) with \(\operatorname{card} \mathcal{B}_{n} \leq N_{n}\). Prove that you can construct an admissible sequence ( \(\mathcal{A}_{n}\) ) of partitions of \(T\) with the following property:
\[
\forall n \geq 1, \forall A \in \mathcal{A}_{n}, \exists B \in \mathcal{B}_{n-1}, A \subset B
\]
(b) Prove that for any metric space ( \(T, d\) ) we have
\[
\gamma_{2}(T, d) \leq L \sum_{n \geq 0} 2^{n / 2} e_{n}(T)
\]

The following exercise explains one of the reasons admissible sequences of sets are so convenient: given two such sequences we can construct a third sequence which merges the good properties of the two sequences.

Exercise 2.7.7. Consider a set \(T\) and two admissible sequences ( \(\mathcal{B}_{n}\) ) and \(\left(\mathcal{C}_{n}\right)\). Prove that there is an admissible sequence \(\left(\mathcal{A}_{n}\right)\) such that
\[
\forall n \geq 1, \forall A \in \mathcal{A}_{n}, \exists B \in \mathcal{B}_{n-1}, A \subset B, \exists C \in \mathcal{C}_{n-1}, A \subset C
\]

The following simple property should be clear in the reader's mind.
Exercise 2.7.8. (a) Prove that for \(n \geq 0\) we have
\[
2^{n / 2} e_{n}(T) \leq L \gamma_{2}(T, d)
\]

Hint: observe that \(2^{n / 2} \max \left\{\Delta(A) ; A \in \mathcal{A}_{n}\right\} \leq \sup _{t \in T} \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right)\). (b) Prove that, equivalently, for \(\epsilon>0\) we have
\[
\epsilon \sqrt{\log N(T, d, \epsilon)} \leq L \gamma_{2}(T, d)
\]

The reader should compare (2.57) with (2.56).
Exercise 2.7.9. Use (2.57) and Exercise 2.5.9 (e) to prove that if \(T \subset \mathbb{R}^{m}\) then
\[
\sum_{n \geq 0} 2^{n / 2} e_{n}(T) \leq L \log (m+1) \gamma_{2}(T, d)
\]

In words, Dudley's bound is never off by more than a factor of about \(\log (m+1)\) in \(\mathbb{R}^{m} .^{14}\)
\({ }^{14}\) And we have shown in Exercise 2.5.6 that it is never off by a factor more that about \(\log \log\) card \(T\) either.

Exercise 2.7.10. Prove that the estimate (2.58) is essentially optimal. Warning: this requires some skill.

Combining Theorem 2.7.2 with Definition 2.7.3 yields the following very important result.

Theorem 2.7.11. Under (2.4) and (2.1) we have
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \gamma_{2}(T, d)
\]

To make (2.59) of interest we must be able to control \(\gamma_{2}(T, d)\), i.e. we must learn how to construct admissible sequences, a topic we shall first address in Section 2.9.

Exercise 2.7.12. When the process \(\left(X_{t}\right)_{t \in T}\) satisfies (2.4) but is no longer assumed to be centered prove that
\[
\mathrm{E} \sup _{s, t \in T}\left|X_{s}-X_{t}\right| \leq L \gamma_{2}(T, d)
\]

We now turn to the control of the tails of the process, which will follow by a small variation of the same argument.
Theorem 2.7.13. Under (2.4) and (2.1) we have
\[
\mathrm{P}\left(\sup _{s, t \in T}\left|X_{s}-X_{t}\right| \geq L \gamma_{2}(T, d)+L u \Delta(T)\right) \leq L \exp \left(-u^{2}\right)
\]

Proof. We use the notation of the proof of (2.34). We may assume \(u \geq 1\). Let us consider the smallest integer \(k \geq 0\) such that \(u^{2} \leq 2^{k}\) so that \(2^{k} \leq 2 u^{2}\). Consider the event \(\Omega_{1}\) defined by
\[
\forall t \in T_{k},\left|X_{t}-X_{t_{0}}\right| \leq 4 u \Delta(T)
\]
so that by the union bound,
\[
P\left(\Omega_{1}^{c}\right) \leq 2^{2^{k}} \cdot 2 \exp \left(-8 u^{2}\right) \leq 2 \exp \left(2 u^{2}-8 u^{2}\right) \leq \exp \left(-u^{2}\right)
\]

Consider the event \(\Omega_{2}\) given by
\[
\forall n \geq k, \forall t \in T,\left|X_{\pi_{n}(t)}-X_{\pi_{n+1}(t)}\right| \leq 2^{n / 2+2} \Delta\left(A_{n}(t)\right)
\]
so that by the union bound again,
\[
\mathrm{P}\left(\Omega_{2}^{c}\right) \leq \sum_{n \geq k} 2^{2^{n+1}} \cdot 2 \exp \left(-2^{n+3}\right) \leq \sum_{n \geq k} 2 \exp \left(-2^{n+2}\right) \leq 4 \exp \left(-u^{2}\right)
\]
using e.g. in the last inequality that \(2^{n+2} \geq 2^{k+2}+n-k \geq 4 u^{2}+n-k\). Consequently, \(\mathrm{P}\left(\Omega_{1} \cup \Omega_{2}\right) \geq 1-5 \exp \left(-u^{2}\right)\), and on this event we have
\[
\left|X_{t}-X_{\pi_{k}(t)}\right| \leq \sum_{n \geq k} 2^{n / 2+2} \Delta\left(A_{n}(t)\right) \leq L \gamma_{2}(T, d)
\]
so that \(\left|X_{t}-X_{t_{0}}\right| \leq\left|X_{t}-X_{\pi_{k}(t)}\right|+\left|X_{\pi_{k}(t)}-X_{t_{0}}\right| \leq L \gamma_{2}(T, d)+L u \Delta(T)\).

Let us note in particular that, using (2.24),
\[
\left(\mathrm{E} \sup _{s, t \in T}\left|X_{s}-X_{t}\right|^{p}\right)^{1 / p} \leq L \sqrt{p} \gamma_{2}(T, d)
\]

Needless to say that we will look for extensions of Theorem 2.7.13. We can prove right away a particularly elegant result (due independently to R. Latała and S . Mendelson). Let us consider a process \(\left(X_{t}\right)_{t \in T}\), which is assumed to be centered but need not to be symmetric. For \(n \geq 1\) consider the distance \(\delta_{n}\) on \(T\) given by \(\delta_{n}(s, t)=\left\|X_{s}-X_{t}\right\|_{2^{n}}\). Denote by \(\Delta_{n}(A)\) the diameter of a subset \(A\) of \(T\) for the distance \(\delta_{n}\).

Theorem 2.7.14. Consider an admissible sequence \(\left(\mathcal{A}_{n}\right)_{n \geq 0}\) of partitions of \(T\). Then
\[
\mathrm{E} \sup _{s, t \in T}\left|X_{s}-X_{t}\right| \leq L \sup _{t \in T} \sum_{n \geq 0} \Delta_{n}\left(A_{n}(t)\right) .
\]

Moreover, given \(u>0\) and the largest integer \(k\) with \(2^{k} \leq u^{2}\) we have
\[
\mathrm{P}\left(\sup _{s, t \in T}\left|X_{s}-X_{t}\right| \geq L \Delta_{k}(T)+\sup _{t \in T} \sum_{n \geq 0} \Delta_{n}\left(A_{n}(t)\right)\right) \leq L \exp \left(-u^{2}\right) .
\]

Proof. The increment condition (2.4) will be replaced by the following. For a r.v. \(Y\) and \(p \geq 1\) we have
\[
\mathrm{P}(|Y| \geq u) \leq \mathrm{P}\left(|Y|^{p} \geq u^{p}\right) \leq\left(\frac{\|Y\|_{p}}{u}\right)^{p}
\]

Let us then consider the points \(\pi_{n}(t)\) as usual. For \(u \geq 1\) let us consider the event \(\Omega_{u}\) defined by \({ }^{15}\)
\[
\forall n \geq 1,\left|X_{\pi_{n}(t)}-X_{\pi_{n+1}(t)}\right| \leq u \Delta_{n}\left(A_{n}(t)\right)
\]
so that by the union bound and (2.69) for \(u \geq 4\) we have
\[
\mathrm{P}\left(\Omega_{u}^{c}\right) \leq \sum_{n \geq 1}\left(\frac{2}{u}\right)^{2^{n}} \leq \sum_{k \geq 2}\left(\frac{2}{u}\right)^{k} \leq \frac{L}{u^{2}} .
\]

On \(\Omega_{u}\) summation of the inequalities (2.70) for \(n \geq 1\) yields \(\sup _{t \in T} \mid X_{t}-\) \(X_{\pi_{1}(t)} \leq L u \sum_{n \geq 1} \Delta_{n}\left(A_{n}(t)\right)\). Combining with (2.71) we obtain
\[
\mathrm{E} \sup _{t \in T}\left|X_{t}-X_{\pi_{1}(t)}\right| \leq L \sum_{n \geq 1} \Delta_{n}\left(A_{n}(t)\right) .
\]

Since \(E \sup _{t \in T}\left|X_{\pi_{1}(t)}-X_{\pi_{0}(t)}\right| \leq L \Delta_{0}(T)\) we have \(E_{\sup _{t \in T}}\left|X_{t}-X_{\pi_{0}(t)}\right| \leq\) \(L \sum_{n \geq 0} \Delta_{n}\left(A_{n}(t)\right)\) and (2.67) follows. The proof of (2.68) is nearly identical to the proof of (2.61) and is left to the reader.

\footnotetext{
\({ }^{15}\) We are following here the general method outlined at the end of Section 2.4.
}

\subsection*{2.8 Functionals}

Given a metric space ( \(T, d\) ) how do we calculate \(\gamma_{2}(T, d)\) ? Of course there is no free lunch. The quantity \(\gamma_{2}(T, d)\) reflects a highly non-trivial geometric characteristic of the metric space. This geometry must be understood in order to compute \(\gamma_{2}(T, d)\). There are unsolved problems in this book (such as Conjecture 17.1.4) which boil down to estimating \(\gamma_{2}(T, d)\) for a certain metric space.

In this section we introduce functionals, which are an efficient way to bring up the geometry of a metric space and to build competent admissible sequences, providing upper bounds for \(\gamma_{2}(T, d)\). We will say that a map \(F\) is a functional on a set \(T\) if, to each subset \(H\) of \(T\) it associates a number \(F(H) \geq 0\), and if it is increasing, i.e.
\[
H \subset H^{\prime} \subset T \Rightarrow F(H) \leq F\left(H^{\prime}\right)
\]

Intuitively a functional is a measure of "size" for the subsets of \(T\). It allows to identify which subsets of \(T\) are "large" for our purposes. A first example is given by \(F(H)=\Delta(H)\). In the same direction, a fundamental example of a functional is
\[
F(H)=\gamma_{2}(H, d)
\]

A second example, equally important, is the quantity
\[
F(H)=\mathrm{E} \sup _{t \in H} X_{t}
\]
where \(\left(X_{t}\right)_{t \in T}\) is a given process indexed by \(T\) and satisfying (2.4).
For our purposes the relevant property of functionals is by no means intuitively obvious yet (but we shall soon see that the functional (2.73) does enjoy this property). Let us first try to explain it in words: if a set is the union of many small pieces far enough from each other, then this set is significantly larger (as measured by the functional) than the smallest of its pieces. "Significantly larger" depends on the scale of the pieces, and on their number. This property will be called a "growth condition".

Let us address a secondary point before we give definitions. We denote by \(B(t, r)\) the ball centered at \(t\) of radius \(r\), and we note that
\[
\Delta(B(t, r)) \leq 2 r
\]

This factor 2 is a nuisance. It is qualitatively the same to say that a set is contained in a ball of small radius or has small diameter, but quantitatively we have to account for this factor 2 . In countless constructions we will produce sets \(A\) which are "small" because they are contained in a ball of small radius \(r\). Either we keep track of this property, which is cumbersome, or we control the size of \(A\) through its diameter and we deal with this inelegant factor 2 . We have chosen here the second method. \({ }^{16}\)
\({ }^{16}\) The opposite choice was made in [171].

What do we mean by "small pieces far from each other"? There is a scale \(a>0\) at which this happens, and a parameter \(r \geq 8\) which gives us some room. The pieces are small at that scale: they are contained in balls with radius \(2 a / r .^{17}\) The balls are far from each other: any two centers of such balls are at mutual distance \(\geq a\). The reason why we require \(r \geq 8\) is that we want the following: two points taken in different balls with radius \(2 a / r\) whose centers are at distance \(\geq a\) cannot be too close to each other. This would not be true for, say, \(r=4\), so we give ourselves some room, and take \(r \geq 8\). Here is the formal definition.

Definition 2.8.1. Given \(a>0\) and an integer \(r \geq 8\) we say that subsets \(H_{1}, \ldots, H_{m}\) of \(T\) are ( \(a, r\) )-separated if
\[
\forall \ell \leq m, H_{\ell} \subset B\left(t_{\ell}, 2 a / r\right)
\]
where the points \(t_{1}, t_{2}, \ldots, t_{m}\) in \(T\) satisfy
\[
\forall \ell, \ell^{\prime} \leq m, \ell \neq \ell^{\prime} \Rightarrow a \leq d\left(t_{\ell}, t_{\ell^{\prime}}\right) \leq 2 a r
\]

A secondary feature of this definition is that the small pieces \(H_{\ell}\) are not only well separated (on a scale \(a\) ), but they are in the "same region of \(T\) " (on the larger scale ra). This is the content of the last inequality in condition (2.76).

Exercise 2.8.2. Find interesting examples of metric spaces for which there are no points \(t_{1}, \ldots, t_{m}\) as in (2.76), for all large enough values of \(m\).

Now, what does "the union of the pieces is significantly larger than the smallest of these pieces" mean? This is an "additive property", not a multiplicative one. In this first version of the growth condition, it means that the size of this union is larger than the size of the smallest piece by a quantity \(a \sqrt{\log N}\) where \(N\) is the number of pieces. \({ }^{18}\) Well, sometimes it will only be larger by a quantity of say \(a \sqrt{\log N} / 100\). This is how the parameter \(c^{*}\) below comes into the picture. One could also multiply the functionals by a suitable constant (i.e. \(1 / c^{*}\) ) to always reduce to the case \(c^{*}=1\) but this is a matter of taste.

Another feature is that we do not need to consider the case with \(N\) pieces for a general value of \(N\), but only for the case where \(N=N_{n}\) for some \(n\). This is because we care about the value of \(\log N\) only within, say, a factor of 2 , and this is precisely what motivated the definition of \(N_{n}\). In order to understand the definition below one should also recall that \(\sqrt{\log N_{n}}\) is about \(2^{n / 2}\).

Definition 2.8.3. We say that the functional \(F\) satisfies the growth condition with parameters \(r \geq 8\) and \(c^{*}>0\) if for any integer \(n \geq 1\) and any

\footnotetext{
17 This coefficient 2 is motivated by the considerations of the previous paragraph.
\({ }^{18}\) We remind the reader that the function \(\sqrt{\log y}\) arises from the fact that it is the inverse of the function \(\exp \left(x^{2}\right)\).
}
\(a>0\) the following holds true, where \(m=N_{n}\). For each collection of subsets \(H_{1}, \ldots, H_{m}\) of \(T\) that are ( \(a, r\) )-separated we have
\[
F\left(\bigcup_{\ell \leq m} H_{\ell}\right) \geq c^{*} a 2^{n / 2}+\min _{\ell \leq m} F\left(H_{\ell}\right)
\]

This definition is motivated by the fundamental fact that when \(\left(X_{t}\right)_{t \in T}\) is a Gaussian process, the functional (2.74) satisfies a form of the growth condition, see Proposition 2.10.8 below.

The following illustrates how we might use the first part of (2.76).
Exercise 2.8.4. Let \((T, d)\) be isometric to a subset of \(\mathbb{R}^{k}\) provided with the distance induced by a norm. Prove that in order to check that a functional satisfies the growth condition of Definition 2.8.3, it suffices to consider the values of \(n\) for which \(N_{n+1} \leq(1+2 r)^{k}\). Hint: it follows from (2.47) that for larger values of \(n\) and \(m=N_{n}\) there are no points \(t_{1}, \ldots, t_{m}\) as in (2.76).

You may find it hard to give simple examples of functionals which satisfy the growth condition (2.77). It will become gradually apparent that this condition imposes strong restrictions on the metric space ( \(T, d\) ) and in particular a control from above of the quantity \(\gamma_{2}(T, d)\). It bears repeating that \(\gamma_{2}(T, d)\) reflects the geometry of the space ( \(T, d\) ). Once this geometry is understood, it is usually possible to guess a good choice for the functional \(F\). Many examples will be given in subsequent chapters.

As we show now, we really have no choice. Functionals with the growth property are intimately connected with the quantity \(\gamma_{2}(T, d)\).

Proposition 2.8.5. Assume \(r \geq 16\). Then the functional \(F(H)=\gamma_{2}(H, d)\) satisfies the growth condition with parameters \(r\) and \(c^{*}=1 / 8\).

Proof. Let \(m=N_{n}\) and consider points \(\left(t_{\ell}\right)_{\ell \leq m}\) of \(T\) with \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq a\) if \(\ell \neq \ell^{\prime}\). Consider sets \(H_{\ell} \subset B\left(t_{\ell}, a / 8\right)\), and the set \(H=\bigcup_{\ell \leq m} H_{\ell}\). We have to prove that
\[
\gamma_{2}(H, d) \geq \frac{1}{8} a 2^{n / 2}+\min _{\ell \leq m} \gamma_{2}\left(H_{\ell}, d\right)
\]

Consider an admissible sequence of partitions ( \(\mathcal{A}_{n}\) ) of \(H\), and consider the set
\[
I_{n}=\left\{\ell \leq m ; \exists A \in \mathcal{A}_{n-1} ; A \subset H_{\ell}\right\}
\]

Picking for \(\ell \in I_{n}\) an arbitrary element \(A \in \mathcal{A}_{n-1}\) with \(A \subset H_{\ell}\) defines a one-to-one map from \(I_{n}\) to \(\mathcal{A}_{n-1}\). Thus card \(I_{n} \leq \operatorname{card} \mathcal{A}_{n-1} \leq N_{n-1}<m=N_{n}\). Hence there exists \(\ell_{0} \notin I_{n}\). Next, we prove that for \(t \in H_{\ell_{0}}\) we have
\[
\Delta\left(A_{n-1}(t)\right) \geq \Delta\left(A_{n-1}(t) \cap H_{\ell_{0}}\right)+\frac{1}{4} a
\]

Since \(\ell_{0} \notin I_{n}\), we have \(A_{n-1}(t) \not \subset H_{\ell_{0}}\), so that since \(A_{n-1}(t) \subset H\), the set \(A_{n-1}(t)\) must intersect a set \(H_{\ell} \neq H_{\ell_{0}}\), and consequently it intersects the ball
\(B\left(t_{\ell}, a / 8\right)\). Since \(t \in H_{\ell_{0}}\) we have \(d\left(t, B\left(t_{\ell}, a / 8\right)\right) \geq a / 2\). Since \(t \in A_{n-1}(t)\) this implies that \(\Delta\left(A_{n-1}(t)\right) \geq a / 2\). This proves (2.79) since \(\Delta\left(A_{n-1}(t) \cap\right.\) \(\left.H_{\ell_{0}}\right) \leq \Delta\left(H_{\ell_{0}}\right) \leq a / 4\).

Now, since for each \(k \geq 0\) we have \(\Delta\left(A_{k}(t)\right) \geq \Delta\left(A_{k}(t) \cap H_{\ell_{0}}\right)\), we have
\[
\begin{aligned}
\sum_{k \geq 0} 2^{k / 2}\left(\Delta\left(A_{k}(t)\right)\right. & \left.\left.-\Delta\left(A_{k}(t) \cap H_{\ell_{0}}\right)\right)\right) \\
& \geq 2^{(n-1) / 2}\left(\Delta\left(A_{n-1}(t)-\Delta\left(A_{n-1}(t) \cap H_{\ell_{0}}\right)\right)\right. \\
& \geq \frac{1}{4} a 2^{(n-1) / 2}
\end{aligned}
\]
where we have used (2.79) in the last inequality, and, consequently,
\[
\sum_{k \geq 0} 2^{k / 2} \Delta\left(A_{k}(t)\right) \geq \frac{1}{4} a 2^{(n-1) / 2}+\sum_{k \geq 0} 2^{k / 2} \Delta\left(A_{k}(t) \cap H_{\ell_{0}}\right)
\]

Next, consider the admissible sequence \(\left(\mathcal{A}_{n}^{\prime}\right)\) of \(H_{\ell_{0}}\) given by \(\mathcal{A}_{n}^{\prime}=\{A \cap\) \(\left.H_{\ell_{0}} ; A \in \mathcal{A}_{n}\right\}\). We have by definition
\[
\sup _{t \in H_{\ell_{0}}} \sum_{k \geq 0} 2^{k / 2} \Delta\left(A_{k}(t) \cap H_{\ell_{0}}\right) \geq \gamma_{2}\left(H_{\ell_{0}}, d\right)
\]

Hence, taking the supremum over \(t\) in \(H_{\ell_{0}}\) in (2.80) we get
\(\sup _{t \in H_{\ell_{0}}} \sum_{k \geq 0} 2^{k / 2} \Delta\left(A_{k}(t)\right) \geq \frac{1}{4} a 2^{(n-1) / 2}+\gamma_{2}\left(H_{\ell_{0}}, d\right) \geq \frac{1}{8} a 2^{n / 2}+\min _{\ell \leq m} \gamma_{2}\left(H_{\ell}, d\right)\).
Since the admissible sequence ( \(\mathcal{A}_{n}\) ) is arbitrary, we have proved (2.78).

\subsection*{2.9 Partitioning Schemes}

In this section we use functionals satisfying the growth condition to construct admissible sequences of partitions. The basic result is as follows.

Theorem 2.9.1. Assume that there exists on \(T\) a functional \(F\) which satisfies the growth condition of Definition 2.8.3 with parameters \(r\) and \(c^{*}\). Then \({ }^{19}\)
\[
\gamma_{2}(T, d) \leq \frac{L r}{c^{*}} F(T)+L r \Delta(T)
\]

This theorem and its generalizations form the backbone of this book. The essence of this theorem is that it produces (by actually constructing them) a sequence of partitions that witnesses the inequality (2.81). For this reason, it could be called "the fundamental partitioning theorem."
\({ }^{19}\) It is certain that as \(r\) grows, we must obtain a weaker result. The dependence of the right-hand side of (2.81) on \(r\) is not optimal. It may be improved with further work.

Exercise 2.9.2. Consider a metric space \(T\) consisting of exactly two points. Prove that the functional given by \(F(H)=0\) for each \(H \subset T\) satisfies the growth condition of Definition 2.8.3 for \(r=8\) and any \(c^{*}>0\). Explain why we cannot replace (2.81) by the inequality \(\gamma_{2}(T, d) \leq \operatorname{Lr} F(T) / c^{*}\).

Let us first stress the following trivial fact (connected to Exercise 2.5.9 (a)). It will be used many times. The last statement of (a) is particularly useful.

Lemma 2.9.3. (a) Consider an integer \(N\). If we cannot cover \(T\) by at most \(N-1\) balls of radius a then there exist points \(\left(t_{\ell}\right)_{\ell \leq N}\) with \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq a\) for \(\ell \neq \ell^{\prime}\). In particular if \(e_{n}(T)>\) a we can find points \(\left(t_{\ell}\right)_{\ell \leq N_{n}}\) with \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq\) a for \(\ell \neq \ell^{\prime}\).
(b) Assume that any sequence \(\left(t_{\ell}\right)_{\ell \leq m}\) with \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq\) a for \(\ell \neq \ell^{\prime}\) satisfies \(m \leq N\). Then \(T\) can be covered by \(N\) balls of radius \(a\).
(c) Consider points \(\left(t_{\ell}\right)_{\ell \leq N_{n}+1}\) such that \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq a\) for \(\ell \neq \ell^{\prime}\). Then \(e_{n}(T) \geq a / 2\).

Proof. (a) We pick the points \(t_{\ell}\) recursively with \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq a\) for \(\ell^{\prime}<\ell\). By hypothesis the balls of radius \(a\) centered on the previously constructed points do not cover the space if there are \(<N\) of them so that the construction continues until we have constructed \(N\) points.
(b) You can view this either as a reformulation of (a) or argue directly that when \(m\) is taken as large as possible the balls \(B\left(t_{\ell}, a\right)\) cover \(T\).
(c) If \(T\) is covered by sets \(\left(B_{\ell^{\prime}}\right)_{\ell^{\prime} \leq N_{n}}\), by the pigeon hole principle at least two of the points \(t_{\ell}\) must fall into one of these sets, which therefore cannot be a ball of radius \(<a / 2\).

The admissible sequence of partitions witnessing (2.81) will be constructed by recursive application of the following basic principle.

Lemma 2.9.4. Under the conditions of Theorem 2.9.1 consider \(B \subset T\) with \(\Delta(B) \leq 2 r^{-j}\) for a certain \(j \in \mathbb{Z}\) and consider any \(n \geq 0\). Let \(m=N_{n}\). Then we can find a partition \(\left(A_{\ell}\right)_{\ell \leq m}\) of \(B\) into sets which have either of the following properties:
\[
\Delta\left(A_{\ell}\right) \leq 2 r^{-j-1}
\]
or else
\[
t \in A_{\ell} \Rightarrow F\left(B \cap B\left(t, 2 r^{-j-2}\right)\right) \leq F(B)-c^{*} 2^{n / 2} r^{-j-1}
\]

In words, the piece of the partitions have two further properties. Either (case (2.82)) we have reduced the bound on their diameter from \(2 r^{-j}\) for \(B\) to \(2 r^{-j-1}\), or else we have no new information on the diameter, but we have gathered the information (2.83).

Proof. Consider the set
\[
C=\left\{t \in B ; F\left(B \cap B\left(t, 2 r^{-j-2}\right)\right)>F(B)-c^{*} 2^{n / 2} r^{-j-1}\right\}
\]

Consider points \(\left(t_{\ell}\right)_{\ell \leq m^{\prime}}\) in \(C\) such that \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq r^{-j-1}\) for \(\ell \neq \ell^{\prime}\). We prove that \(m^{\prime}<m\). For otherwise, using (2.77) for \(a=r^{-j-1}\) and for the sets \(H_{\ell}:=B \cap B\left(t_{\ell}, 2 r^{-j-2}\right)\) shows that
\[
F(B) \geq F\left(\bigcup_{\ell \leq m} H_{\ell}\right) \geq c^{*} r^{-j-1} 2^{n / 2}+\min _{\ell \leq m} F\left(H_{\ell}\right)>F(B)
\]

This contradiction proves that \(m^{\prime}<m\). Consequently, using Lemma 2.9.3 (b) for \(N=m-1\) we may cover \(C\) by \(m^{\prime}<m\) balls \(\left(B_{\ell}\right)_{\ell \leq m^{\prime}}\) of radius \(\leq r^{-j-1}\). We then set \(A_{\ell}=C \cap\left(B_{\ell} \backslash \cup_{\ell^{\prime}<\ell} B_{\ell^{\prime}}\right)\) for \(\ell \leq m^{\prime}, A_{\ell}=\emptyset\) for \(m^{\prime}<\ell<m\) and \(A_{m}=B \backslash C\).

So, in picturesque terms, Lemma 2.9.4 produces many small pieces and (possibly) a large one (on which one has further information).

Before we start the proof of Theorem 2.9.1 we need the following technical fact which will be used many times: the sum of a geometric series is basically of the size of either its first or its last term.

Lemma 2.9.5. Consider numbers \(\left(a_{n}\right)_{n \geq 0}, a_{n}>0\), and assume \(\sup _{n} a_{n}<\) \(\infty\). Consider \(\alpha>1\) and define
\[
I=\left\{n \geq 0 ; \forall k \geq 0, k \neq n, a_{k}<a_{n} \alpha^{|n-k|}\right\}
\]

Then \(I \neq \emptyset\) and we have
\[
\sum_{k \geq 0} a_{k} \leq \frac{2 \alpha}{\alpha-1} \sum_{n \in I} a_{n}
\]

Proof. Let us write \(k \prec n\) when \(a_{k} \leq a_{n} \alpha^{-|n-k|}\). This relation is a partial order: if \(k \prec n\) and \(n \prec p\) then \(a_{k} \leq a_{p} \alpha^{-|n-k|-|n-p|} \leq a_{p} \alpha^{-|k-p|}\), so that \(k \prec p\). We can then restate the definition of \(I\) :
\[
I=\{n \geq 0 ; \forall k \geq 0, n \prec k \Rightarrow n=k\}
\]

In words, \(I\) is the set of elements \(n\) of \(\mathbb{N}\) that are maximal for the partial order \(\prec\).

Next, we prove that for each \(k\) in \(\mathbb{N}\) there exists \(n \in I\) with \(k \prec n\). Indeed otherwise we can recursively construct an infinite sequence \(n_{1}=n \prec n_{2} \prec \cdots\) and this is absurd because \(a_{n_{\ell+1}} \geq \alpha a_{n_{\ell}}\) and we assume that the sequence \(\left(a_{n}\right)\) is bounded.

Thus for each \(k\) in \(\mathbb{N}\) there exists \(n \in I\) with \(k \prec n\). Then \(a_{k} \leq a_{n} \alpha^{-|n-k|}\), and therefore
\[
\sum_{k \geq 0} a_{k} \leq \sum_{n \in I} \sum_{k \geq 0} a_{n} \alpha^{-|k-n|} \leq \frac{2}{1-\alpha^{-1}} \sum_{n \in I} a_{n}
\]

Proof of Theorem 2.9.1. There is no question that this proof is the most demanding up to this point. The result is however absolutely central, on its own and also because several of our main results will follow the same overall scheme of proof.

We have to construct an admissible sequence of partitions which witnesses the inequality (2.81). The construction of this sequence is as simple as it could be: we recursively use Lemma 2.9.4. More precisely, we construct an admissible sequence of partitions \(\mathcal{A}_{n}\) and for \(A \in \mathcal{A}_{n}\) we construct an integer \(j_{n}(A) \in \mathbb{Z}\) with
\[
\Delta(A) \leq 2 r^{-j_{n}(A)}
\]

We start with \(\mathcal{A}_{0}=\{T\}\) and \(j_{0}(T)\) the largest integer \(j_{0} \in \mathbb{Z}\) with \(\Delta(T) \leq\) \(2 r^{-j_{0}}\), so that \(2 r^{-j_{0}} \leq r \Delta(T)\). Having constructed \(\mathcal{A}_{n}\) we construct \(\mathcal{A}_{n+1}\) as follows. For each \(B \in \mathcal{A}_{n}\), we use Lemma 2.9.4 with \(j=j_{n}(B)\) to split \(B\) into sets \(\left(A_{\ell}\right)_{\ell \leq N_{n}}\). If \(A_{\ell}\) satisfies (2.82) we set \(j_{n+1}\left(A_{\ell}\right)=j_{n}(B)+1\) and otherwise (since we have no new information on the diameter) we set \(j_{n+1}\left(A_{\ell}\right)=j_{n}(B)\). Thus, in words, \(j_{n+1}\left(A_{\ell}\right)=j_{n}(B)+1\) if \(A_{\ell}\) is a small piece of \(B\) and \(j_{n+1}\left(A_{\ell}\right)=j_{n}(B)\) if \(A_{\ell}\) is the large piece of \(B\).

The sequence thus constructed is admissible, since each set \(B\) in \(\mathcal{A}_{n}\) is split into at most \(N_{n}\) sets and since \(N_{n}^{2} \leq N_{n+1}\). We note also by construction that if \(B \in \mathcal{A}_{n}\) and \(A \subset B, A \in \mathcal{A}_{n+1}\) then
- either \(j_{n+1}(A)=j_{n}(B)+1\)
- or else \(j_{n+1}(A)=j_{n}(B)\) and
\[
t \in A \Rightarrow F\left(B \cap B\left(t, 2 r^{-j_{n+1}(A)-2}\right)\right) \leq F(B)-c^{*} 2^{n / 2} r^{-j_{n+1}(A)-1}
\]

Now we start the hard part of the proof, proving that the sequence of partitions we just constructed witnesses (2.81). For this we fix \(t \in T\). We want to prove that
\[
\sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq \frac{L r}{c^{*}} F(T)+L r \Delta(T)
\]

We set \(j(n)=j_{n}\left(A_{n}(t)\right)\), so that \(j(n) \leq j(n+1) \leq j(n)+1\). We set \(a(n)=\) \(2^{n / 2} r^{-j(n)}\). Since \(2^{n / 2} \Delta\left(A_{n}(t)\right) \leq 2 a(n)\), it suffices to show that
\[
\sum_{n \geq 0} a(n) \leq \frac{L r}{c^{*}} F(T)+L r \Delta(T)
\]

First, we prove a side result, that for \(n \geq 0\) we have
\[
a(n) \leq \frac{L r}{c^{*}} F(T)+L \Delta(T)
\]

If \(n \geq 1\) and \(j(n-1)=j(n)\) then using (2.87) for \(n-1\) rather than \(n\) yields (2.89). Next, if \(n \geq 1\) and \(j(n-1)=j(n)-1\) then \(a(n)=\sqrt{2} r^{-1} a(n-1) \leq\) \(a(n-1)\) since \(r \geq 8\), and iterating this relation until we reach an integer \(n^{\prime}\) with either \(j\left(n^{\prime}-1\right)=j\left(n^{\prime}\right)\) or \(n^{\prime}=0\) proves (2.89) since \(a(0) \leq L \Delta(T)\).

In particular the sequence \((a(n))\) is bounded. Consider then the set \(I\) as provided by Lemma 2.9.5 for \(\alpha=\sqrt{2}\) and \(a_{n}=a(n)\), that is
\[
I=\left\{n \geq 0 ; \forall k \geq 0, n \neq k, a(k)<a(n) 2^{|k-n| / 2}\right\}
\]

Recalling that \(a(0)=r^{-j_{0}} \leq r \Delta(T) / 2\), it suffices to prove that
\[
\sum_{n \in I \backslash\{0\}} a(n) \leq \frac{L r}{c^{*}} F(T)
\]

For \(n \in I, n \geq 1\) we have \(a(n+1)<\sqrt{2} a(n)\) and \(a(n-1)<\sqrt{2} a(n)\). Since \(a(n+1)=\sqrt{2} r^{j(n)-j(n+1)} a(n)\) this implies
\[
j(n+1)=j(n)+1 ; j(n-1)=j(n)
\]

Proving (2.90) is the difficult part. Assuming first that \(I\) is infinite (the important case), let us enumerate the elements of \(I \backslash\{0\}\) as \(n_{1}<n_{2}<\ldots\). so that (2.91) implies
\[
j\left(n_{k}+1\right)=j\left(n_{k}\right)+1 ; j\left(n_{k}-1\right)=j\left(n_{k}\right) .
\]

In words, \(n_{k}\) is at the end of a sequence of partitions steps in which \(A_{\ell+1}(t)\) was the large piece of \(A_{\ell}(t)\), and \(A_{n_{k}+1}(t)\) is a small piece of \(A_{n_{k}}(t)\). Let us note that as a consequence of (2.92) we have
\[
j_{n_{k+1}} \geq j_{n_{k}+1} \geq j_{n_{k}}+1
\]

The key to the proof is to show that for \(k \geq 1\) we have
\[
a\left(n_{k}\right) \leq \frac{L r}{c^{*}}\left(f\left(n_{k}-1\right)-f\left(n_{k+2}\right)\right)
\]
where \(f(n)=F\left(A_{n}(t)\right)\). Now the sequence \((f(n))\) is decreasing because \(A_{n}(t) \subset A_{n-1}(t)\), and \(f(0)=F(T)\). When \(k \geq 2\) then \(f\left(n_{k}-1\right) \leq f\left(n_{k-1}\right)\), so that (2.93) implies
\[
a\left(n_{k}\right) \leq \frac{L r}{c^{*}}\left(f\left(n_{k-1}\right)-f\left(n_{k+2}\right)\right)
\]

Summation of the inequalities (2.94) for \(k \geq 2\) then yields
\[
\sum_{k \geq 2} a\left(n_{k}\right) \leq \frac{L r}{c^{*}} F(T)
\]
and combining with (2.93) for \(k=1\) proves (2.90) and concludes the proof of the theorem when \(I\) is infinite.

We now prove (2.93). Since \(n_{k} \geq 1\) we may define \(n^{*}:=n_{k}-1\). By (2.92) we have \(j\left(n_{k}-1\right)=j\left(n_{k}\right)\), i.e. \(j\left(n^{*}\right)=j\left(n^{*}+1\right)\). We may then use (2.87) for \(B=A_{n^{*}}(t), A=A_{n_{k}}(t)=A_{n^{*}+1}(t)\) to obtain that
\[
F\left(B \cap B\left(t, 2 r^{-j_{n^{*}+1}(A)-2}\right)\right) \leq F(B)-c^{*} 2^{n^{*} / 2} r^{-j_{n^{*}+1}(A)-1}
\]

Recalling that \(n^{*}=n_{k}-1\) this means
\[
F\left(B \cap B\left(t, 2 r^{-j_{n_{k}}(A)-2}\right)\right) \leq F(B)-c^{*} 2^{\left(n_{k}-1\right) / 2} r^{-j_{n_{k}}(A)-1},
\]
so that
\[
a\left(n_{k}\right) \leq \frac{L r}{c^{*}}\left(F(B)-F\left(B \cap B\left(t, 2 r^{-j_{n_{k}}(A)-2}\right)\right)\right)
\]

Furthermore, by (2.92),
\[
j\left(n_{k+2}\right) \geq j\left(n_{k+1}\right)+1 \geq j\left(n_{k}\right)+2
\]

Since \(j\left(n_{k+2}\right)=j_{n_{k+2}}\left(A_{n_{k+2}}(t)\right)\), (2.86) implies \(\Delta\left(A_{n_{k+2}}(t)\right) \leq 2 r^{-j\left(n_{k+2}\right)} \leq\) \(2 r^{-j\left(n_{k}\right)-2}\) so that
\[
A_{n_{k+2}}(t) \subset B \cap B\left(t, 2 r^{-j\left(n_{k}\right)-2}\right),
\]
and thus \(f\left(n_{k+2}\right)=F\left(A_{n_{k+2}}(t)\right) \leq F\left(B \cap B\left(t, 2 r^{-j_{n_{k}}}(A)-2\right)\right)\). Combining with (2.97) and since \(F(B)=f\left(n_{k}-1\right)\) we have proved (2.93).

Assuming now that \(I\) is finite, it has a largest element \(n_{\bar{k}}\). We use the previous argument to control \(a\left(n_{k}\right)\) when \(k+2 \leq \bar{k}\), and for \(k=\bar{k}-1\) and \(k=\bar{k}\) we simply use (2.89).

It is important for the sequel that you fully master the previous argument.
Exercise 2.9.6. We say that a sequence \(\left(F_{n}\right)_{n \geq 0}\) of functionals on \((T, d)\) satisfies the growth condition with parameters \(r \geq 8\) and \(c^{*}>0\) if
\[
\forall n \geq 0, F_{n+1} \leq F_{n}
\]
and if for any integer \(n \geq 0\) and any \(a>0\) the following holds true, where \(m=N_{n}\). For each collection of subsets \(H_{1}, \ldots, H_{m}\) of \(T\) that are ( \(a, r\) )separated we have
\[
F_{n}\left(\bigcup_{\ell \leq m} H_{\ell}\right) \geq c^{*} a 2^{n / 2}+\min _{\ell \leq m} F_{n+1}\left(H_{\ell}\right)
\]

Prove that then
\[
\gamma_{2}(T, d) \leq \frac{L r}{c^{*}} F_{0}(T)+L r \Delta(T)
\]

Hint: copy the previous arguments by replacing everywhere \(F(A)\) by \(F_{n}(A)\) when \(A \in \mathcal{A}_{n}\).

Proposition 2.9.7. Consider a metric space ( \(T, d\) ), and for \(n \geq 0\), consider subsets \(T_{n}\) of \(T\) with card \(T_{0}=1\) and card \(T_{n} \leq N_{n}\) for \(n \geq 1\). Consider a number \(S\) and let
\[
U=\left\{t \in T ; \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right) \leq S\right\}
\]

Then \(\gamma_{2}(U, d) \leq L S\).

Proof. For \(H \subset U\) we define \(F(H)=\inf \sup _{t \in H} \sum_{n \geq 0} 2^{n / 2} d\left(t, V_{n}\right)\) where the infimum is taken over all choices of \(V_{n} \subset T\) with \(\operatorname{card} V_{n} \leq N_{n}\). It is important here not to assume that \(V_{n} \subset H\) to ensure that \(F\) is increasing. We then prove that \(F\) satisfies the growth condition by an argument very similar to that of Proposition 2.8.5. The proof follows from Theorem 2.9.1 since \(\Delta(U, d) \leq 2 S\), as each point of \(U\) is within distance \(S\) of the unique point of \(T_{0}\).

A slightly different partitioning scheme has recently been discovered by R. van Handel [59], and we describe a variant of it now. We consider a metric space \((T, d)\) and an integer \(r \geq 8\). We assume that for \(j \in \mathbb{Z}\) we are given a function \(s_{j}(t) \geq 0\) on \(T\).

Theorem 2.9.8. Assume that the following holds.
For each subset \(A\) of \(T\), for each \(j \in \mathbb{Z}\) with \(\Delta(A) \leq 2 r^{-j}\) and for each \(n \geq 1\), then either \(e_{n}(A) \leq r^{-j-1}\) or else there exists \(t \in A\)
with \(s_{j}(t) \geq 2^{n / 2} r^{-j-1}\).
Then
\[
\gamma_{2}(T, d) \leq \operatorname{Lr}\left(\Delta(T)+\sup _{t \in T} \sum_{j \in \mathbb{Z}} s_{j}(t)\right)
\]

We will show later how to construct functions \(s_{j}(t)\) satisfying (2.102) using a functional which satisfies the growth condition. \({ }^{20}\)

The right-hand side of (2.103) is the supremum over \(t\) of a sum of terms. It need not always be the same terms which will contribute the most for different values of \(t\), and the bound is definitely better than if the supremum and the summation were exchanged.

Proof of Theorem 2.9.8. Consider the largest \(j_{0} \in \mathbb{Z}\) with \(\Delta(T) \leq 2 r^{-j_{0}}\), so that \(2 r^{-j_{0}} \leq r \Delta(T)\). We construct by induction an increasing sequence of partitions \(\mathcal{A}_{n}\) with \(\operatorname{card} \mathcal{A}_{n} \leq N_{n}\), and for \(A \in \mathcal{A}_{n}\) we construct an integer \(j_{n}(A) \in \mathbb{Z}\) with \(\Delta(A) \leq 2 r^{-j_{n}(A)}\). We start with \(\mathcal{A}_{0}=\mathcal{A}_{1}=\{T\}\) and \(j_{0}(T)=j_{1}(T)=j_{0}\).

Once \(\mathcal{A}_{n}\) has been constructed ( \(n \geq 1\) ), we further split every element \(B \in \mathcal{A}_{n}\). The idea is to first split \(B\) into sets which are basically level sets for the function \(s_{j}(t)\) in order to achieve the crucial relation (2.107) below, and then to further split each of these sets according to its metric entropy. More precisely, we may assume that \(S=\sup _{t \in T} \sum_{j \in \mathbb{Z}} s_{j}(t)<\infty\), for there is nothing to prove otherwise. Let us set \(j=j_{n}(B)\) and define the sets \(A_{k}\) for \(1 \leq k \leq n\) by setting for \(k<n\)
\[
A_{k}=\left\{t \in B ; 2^{-k} S<s_{j}(t) \leq 2^{-k+1} S\right\}
\]

\footnotetext{
\({ }^{20}\) See [59] for other constructions.
}
and
\[
A_{n}=\left\{t \in B ; s_{j}(t) \leq 2^{-n+1} S\right\} .
\]

The purpose of this construction is to ensure the following:
\[
k \leq n ; t, t^{\prime} \in A_{k} \Rightarrow s_{j}\left(t^{\prime}\right) \leq 2\left(s_{j}(t)+2^{-n} S\right) .
\]

This is obvious since \(s_{j}\left(t^{\prime}\right) \leq 2 s_{j}(t)\) for \(k<n\) and \(s_{j}\left(t^{\prime}\right) \leq 2^{-n+1} S\) if \(k=n\). For each set \(A_{k}, k \leq n\) we use the following procedure.
- If \(e_{n-1}\left(A_{k}\right) \leq r^{-j-1}\) we may cover \(A_{k}\) by at most \(N_{n-1}\) balls of radius \(2 r^{-j-1}\), so we may split \(A_{k}\) into \(N_{n-1}\) pieces of diameter \(\leq 4 r^{-j-1}\). We decide that each of these pieces \(A\) is an element of \(\mathcal{A}_{n+1}\), for which we set \(j_{n+1}(A)=j+1\). Thus \(\Delta(A) \leq 4 r^{-j-1}=4 r^{-j_{n+1}(A)}\).
- Otherwise we decide that \(A_{k} \in \mathcal{A}_{n+1}\) and we set \(j_{n+1}\left(A_{k}\right)=j\). Thus \(\Delta\left(A_{k}\right) \leq 2 r^{-j}=2 r^{-j_{n+1}(A)}\). From (2.102) there exists \(t^{\prime} \in A_{k}\) for which \(s_{j}\left(t^{\prime}\right) \geq 2^{(n-1) / 2} r^{-j-1}\). Then by (2.106) we have
\[
\forall t \in A_{k} ; 2^{(n-1) / 2} r^{-j-1} \leq 2\left(s_{j}(t)+2^{-n} S\right) .
\]

In summary, if \(B \in \mathcal{A}_{n}\) and \(A \in \mathcal{A}_{n+1}, A \subset B\) then
- either \(j_{n+1}(A)=j_{n}(B)+1\)
- or else \(j_{n+1}(A)=j_{n}(B)\) and, from (2.107)
\[
\forall t \in A ; 2^{(n-1) / 2} r^{-j_{n+1}(A)-1} \leq 2\left(s_{j_{n}(B)}(t)+2^{-n} S\right) .
\]

This completes the construction. Now for \(n \geq 1\) we have \(n \leq N_{n-1}\) so that \(\operatorname{card} \mathcal{A}_{n+1} \leq n N_{n-1} N_{n} \leq N_{n+1}\) and the sequence ( \(\mathcal{A}_{n}\) ) is admissible. Next, we fix \(t \in T\). We set \(j_{n}=j_{n}\left(A_{n}(t)\right)\), and we observe that by construction \(j_{n} \leq j_{n+1} \leq j_{n}+1\). Since \(\Delta\left(A_{n}(t)\right) \leq 4 r^{-j_{n}(t)}\) we have \(2^{n / 2} \Delta\left(A_{n}(t)\right) \leq 4 a(n)\) where \(a(n):=2^{n / 2} r^{-j_{n}(t)}\). To complete the argument we prove that
\[
\sum_{n \geq 0} a(n) \leq \operatorname{Lr}(S+\Delta(T)) .
\]

For this consider the set \(I\) provided by Lemma 2.9.5 for \(\alpha=\sqrt{2}\), so that since \(r^{-j_{0}} \leq 2 r \Delta(T)\) it suffices to prove that
\[
\sum_{n \in I \backslash\{0\}} a(n) \leq L r S .
\]

For \(n \in I \backslash\{0\}\), it holds that \(j_{n-1}=j_{n}<j_{n+1}\) (since otherwise this contradicts the definition of \(I\) ). In particular, the integers \(j_{n}\) for \(n \in I\) are all different so that \(\sum_{n \geq 0} s_{j_{n}}(t) \leq S\). Using (2.108) for \(n-1\) instead of \(n\) yields \(2^{(n-2) / 2} r^{-j_{n}-1} \leq 2\left(s_{j_{n-1}}(t)+2^{-n+1} S\right)\). Since \(j_{n-1}=j_{n}\) we get
\[
a(n) \leq L r\left(s_{j_{n}}(t)+2^{-n} S\right),
\]
and summing these relations we obtain the desired result.
The following connects Theorems 2.9.1 and 2.9.8.

Proposition 2.9.9. Assume that the functional F satisfies the growth condition with parameters \(r\) and \(c^{*}\). Then the functions
\[
s_{j}(t)=\frac{1}{c^{*}}\left(F\left(B\left(t, 2 r^{-j+1}\right)\right)-F\left(B\left(t, 2 r^{-j-2}\right)\right)\right)
\]
satisfy (2.102).
Proof. Consider a subset \(A\) of \(T, j \in \mathbb{Z}\) with \(\Delta(A) \leq 2 r^{-j}\) and \(n \geq 1\). Let \(m=N_{n}\). If \(e_{n}(A)>r^{-j-1}\) then by Lemma 2.9.3 we may find \(\left(t_{\ell}\right)_{\ell \leq m}\) in \(A\) with \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq r^{-j-1}\) for \(\ell \neq \ell^{\prime}\). Consider the set \(H_{\ell}=B\left(t_{\ell}, 2 r^{-j-2}\right)\) so that by (2.77) used for \(a=r^{-j-1}\) it holds that
\[
F\left(\bigcup_{\ell \leq m} H_{\ell}\right) \geq c^{*} r^{-j-1} 2^{n / 2}+\min _{\ell \leq m} F\left(H_{\ell}\right)
\]

Let us now consider \(\ell_{0} \leq m\) such that \(F\left(H_{\ell_{0}}\right)\) achieves the minimum in the right-hand side, so that \(\min _{\ell \leq m} F\left(H_{\ell}\right)=F\left(B\left(t_{\ell_{0}}, 2 r^{-j-2}\right)\right)\). The crude inequality \(2 r^{-j-2}+2 r^{-j} \leq 2 r^{-j+1}\) implies that \(H_{\ell} \subset B\left(t_{\ell_{0}}, 2 r^{-j+1}\right)\) for each \(\ell\), so that \(F\left(\bigcup_{\ell \leq m} H_{\ell}\right) \leq F\left(B\left(t_{\ell_{0}}, 2 r^{-j+1}\right)\right)\). Then (2.111) implies
\[
F\left(B\left(t_{\ell_{0}}, 2 r^{-j+1}\right)\right) \geq c^{*} r^{-j-1} 2^{n / 2}+F\left(B\left(t_{\ell_{0}}, 2 r^{-j-2}\right)\right)
\]
i.e. \(s_{j}\left(t_{\ell_{0}}\right) \geq 2^{n / 2} r^{-j-1}\).

Despite the fact that the proof of Theorem 2.9.8 is a few lines shorter than the proof of Theorem 2.9.1, in the various generalizations of this principle we will mostly follow the scheme of proof of Theorem 2.9.1. The reason for this choice is simple: it should help the reader that our various partition theorems follow a common pattern. The most difficult partition theorem we present is Theorem 6.2.8 (the Latala-Bednorz theorem), which is one of the highlights of this work, and it is not clear at this point whether the method of Theorem 2.9.8 can be adapted to the proof of this theorem.

The following simple observation allows us to construct a sequence which is admissible from one which is slightly too large. It will be used several times.

Lemma 2.9.10. Consider \(\alpha>0\), an integer \(\tau \geq 0\) and an increasing sequence of partitions \(\left(\mathcal{B}_{n}\right)_{n \geq 0}\) with \(\operatorname{card} \mathcal{B}_{n} \leq N_{n+\tau}\). Let
\[
S:=\sup _{t \in T} \sum_{n \geq 0} 2^{n / \alpha} \Delta\left(B_{n}(t)\right)
\]

Then we can find an admissible sequence of partitions \(\left(\mathcal{A}_{n}\right)_{n \geq 0}\) such that
\[
\sup _{t \in T} \sum_{n \geq 0} 2^{n / \alpha} \Delta\left(A_{n}(t)\right) \leq 2^{\tau / \alpha}(S+K(\alpha) \Delta(T))
\]

Of course (for the last time) here \(K(\alpha)\) denotes a number depending on \(\alpha\) only (that need not be the same at each occurrence).
Proof. We set \(\mathcal{A}_{n}=\{T\}\) if \(n<\tau\) and \(\mathcal{A}_{n}=\mathcal{B}_{n-\tau}\) if \(n \geq \tau\) so that \(\operatorname{card} \mathcal{A}_{n} \leq\) \(N_{n}\) and
\[
\sum_{n \geq \tau} 2^{n / \alpha} \Delta\left(A_{n}(t)\right)=2^{\tau / \alpha} \sum_{n \geq 0} 2^{n / \alpha} \Delta\left(B_{n}(t)\right)
\]

Using the bound \(\Delta\left(A_{n}(t)\right) \leq \Delta(T)\), we obtain
\[
\sum_{n<\tau} 2^{n / \alpha} \Delta\left(A_{n}(t)\right) \leq K(\alpha) 2^{\tau / \alpha} \Delta(T)
\]

Exercise 2.9.11. Prove that (2.112) might fail if one replaces the right-hand side by \(K(\alpha, \tau) S\). Hint: \(S\) does not control \(\Delta(T)\).

\subsection*{2.10 Gaussian Processes: The Majorizing Measure Theorem}

Consider a Gaussian process \(\left(X_{t}\right)_{t \in T}\), that is, a jointly Gaussian family of centered r.v.s indexed by \(T\). We provide \(T\) with the canonical distance
\[
d(s, t)=\left(\mathrm{E}\left(X_{s}-X_{t}\right)^{2}\right)^{1 / 2}
\]

Recall the functional \(\gamma_{2}\) of Definition 2.7.3.
Theorem 2.10.1. (The Majorizing Measure Theorem.) For a universal constant \(L\) it holds that
\[
\frac{1}{L} \gamma_{2}(T, d) \leq \mathrm{E} \sup _{t \in T} X_{t} \leq L \gamma_{2}(T, d)
\]

The reason for the name is explained in Section 3.1. We will meditate on this statement in Section 2.12. We will spend much time trying to generalize this theorem to other classes of processes. To link the statements of these generalizations with that of (2.114) it may be good to reformulate the lower bound \(\gamma_{2}(T, d) \leq L \mathrm{E} \sup _{t \in T} X_{t}\) in the following general terms:

The control from above of \(\mathrm{E} \sup _{t \in T} X_{t}\) implies the existence of a
"small" sequence of admissible partitions of \(T\).
The right-hand side inequality in (2.114) is Theorem 2.7.11. To prove the lower bound we will use Theorem 2.9.1 and the functional
\[
F(H)=\mathrm{E} \sup _{t \in H} X_{t}:=\sup _{H^{*} \subset H, H^{*} \text { finite }} \mathrm{E} \sup _{t \in H^{*}} X_{t}
\]

For this we need to prove that this functional satisfies the growth condition with \(c^{*}\) a universal constant and to bound \(\Delta(T)\). We strive to give a proof that relies on general principles, and lends itself to generalizations.

Lemma 2.10.2. (Sudakov minoration) Assume that
\[
\forall p, q \leq m, \quad p \neq q \Rightarrow d\left(t_{p}, t_{q}\right) \geq a
\]

Then we have
\[
\mathrm{E} \sup _{p \leq m} X_{t_{p}} \geq \frac{a}{L_{1}} \sqrt{\log m}
\]

Here and below \(L_{1}, L_{2}, \ldots\) are specific universal constants. Their values remain the same, at least within the same section.

The proof of the Sudakov minoration is given just after Lemma 15.2.7.
Exercise 2.10.3. Prove that Lemma 2.10 .2 is equivalent to the following statement. If \(\left(X_{t}\right)_{t \in T}\) is a Gaussian process, and \(d\) is the canonical distance, then
\[
e_{n}(T, d) \leq L 2^{-n / 2} \mathrm{E} \sup _{t \in T} X_{t}
\]

Compare with Exercise 2.7.8.
To understand the relevance of Sudakov minoration, let us consider the case where \(\mathrm{E} X_{t_{p}}^{2} \leq 100 a^{2}\) (say) for each \(p\). Then (2.116) means that the bound (2.15) is of the correct order in this situation.
Exercise 2.10.4. Prove (2.116) when the r.v.s \(X_{t_{p}}\) are independent. That is, assume that these variables are Gaussian independent centered. Hint: use the method of Exercise 2.3.7 (b).

Exercise 2.10.5. A natural approach ("the second moment method") to prove that \(\mathrm{P}\left(\sup _{p \leq m} X_{t_{p}} \geq u\right)\) is at least \(1 / L\) for a certain value of \(u\) is as follows. Consider the r.v. \(Y=\sum_{p} 1_{\left\{X_{t_{p}} \geq u\right\}}\), prove that \(\mathrm{E} Y^{2} \leq L(\mathrm{E} Y)^{2}\), and then use the Paley-Zygmund inequality (6.15) below to prove that \(\sup _{p \leq m} X_{t_{p}} \geq a \sqrt{\log m} / L_{1}\) with probability \(\geq 1 / L\). Prove that this approach works when the r.v.s \(X_{t_{\ell}}\) are independent, but find examples showing that this naive approach does not work in general to prove (2.116).

The following is a very important property of Gaussian processes, and one of the keys to Theorem 2.10.1. It is a facet of the theory of concentration of measure, a leading idea of modern probability theory. We refer the reader to [74] to learn about this.

Lemma 2.10.6. Consider a Gaussian process \(\left(X_{t}\right)_{t \in U}\), where \(U\) is finite and let \(\sigma=\sup _{t \in U}\left(\mathrm{E} X_{t}^{2}\right)^{1 / 2}\). Then for \(u \geq 0\) we have
\[
\mathrm{P}\left(\left|\sup _{t \in U} X_{t}-\mathrm{E} \sup _{t \in U} X_{t}\right| \geq u\right) \leq 2 \exp \left(-\frac{u^{2}}{2 \sigma^{2}}\right) .
\]

In words, the size of the fluctuations of \(\sup _{t \in U} X_{t}\) are governed by the size of the individual r.v.s \(X_{t}\), rather than by the (typically much larger) quantity \(\mathrm{Esup}_{t \in U} X_{t}\). It is essential that the cardinality of \(U\) does not appear in (2.118).

Exercise 2.10.7. Find an example of a Gaussian process for which
\[
\mathrm{E} \sup _{t \in T} X_{t} \gg \sigma=\sup _{t \in T}\left(\mathrm{E} X_{t}^{2}\right)^{1 / 2}
\]
whereas the fluctuations of \(\sup _{t \in T} X_{t}\) are of order \(\sigma\), e.g. the variance of \(\sup _{t} X_{t}\) is about \(\sigma^{2}\). Hint: \(T=\left\{\left(t_{i}\right)_{i \leq n} ; \sum_{i \leq n} t_{i}^{2} \leq 1\right\}\) and \(X_{t}=\sum_{i \leq n} t_{i} g_{i}\) where \(g_{i}\) are independent standard Gaussian r.v.s

Proposition 2.10.8. Consider points \(\left(t_{\ell}\right)_{\ell \leq m}\) in \(T\). Assume that \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq\) a if \(\ell \neq \ell^{\prime}\). Consider \(\sigma>0\), and for \(\ell \leq m\) a finite set \(H_{\ell} \subset B\left(t_{\ell}, \sigma\right)\). Then if \(H=\bigcup_{\ell \leq m} H_{\ell}\) we have
\[
\mathrm{E} \sup _{t \in H} X_{t} \geq \frac{a}{L_{1}} \sqrt{\log m}-L_{2} \sigma \sqrt{\log m}+\min _{\ell \leq m} \mathrm{E} \sup _{t \in H_{\ell}} X_{t}
\]

When \(\sigma \leq a /\left(2 L_{1} L_{2}\right)\), (2.119) implies
\[
\mathrm{E} \sup _{t \in H} X_{t} \geq \frac{a}{2 L_{1}} \sqrt{\log m}+\min _{\ell \leq m} \mathrm{E} \sup _{t \in H_{\ell}} X_{t}
\]
which can be seen as a generalization of Sudakov's minoration (2.116) by taking \(H_{\ell}=\left\{t_{\ell}\right\}\). When \(m=N_{n}\), (2.120) proves that the functional \(F(H)=\) \(\mathrm{E} \sup _{t \in T} X_{t}\) satisfies the growth condition (2.77).

Proof. We can and do assume \(m \geq 2\). For \(\ell \leq m\), we consider the r.v.
\[
Y_{\ell}=\left(\sup _{t \in H_{\ell}} X_{t}\right)-X_{t_{\ell}}=\sup _{t \in H_{\ell}}\left(X_{t}-X_{t_{\ell}}\right)
\]

For \(t \in H_{\ell}\) we set \(Z_{t}=X_{t}-X_{t_{\ell}}\). Since \(H_{\ell} \subset B\left(t_{\ell}, \sigma\right)\) we have \(\mathrm{E} Z_{t}^{2}=\) \(d\left(t, t_{\ell}\right)^{2} \leq \sigma^{2}\) and, for \(u \geq 0\) equation (2.118) used for the process \(\left(Z_{t}\right)_{t \in H_{\ell}}\) implies
\[
\mathrm{P}\left(\left|Y_{\ell}-\mathrm{E} Y_{\ell}\right| \geq u\right) \leq 2 \exp \left(-\frac{u^{2}}{2 \sigma^{2}}\right)
\]

Thus if \(V=\max _{\ell \leq m}\left|Y_{\ell}-\mathrm{E} Y_{\ell}\right|\) then combining (2.121) and the union bound, we get
\[
\mathrm{P}(V \geq u) \leq 2 m \exp \left(-\frac{u^{2}}{2 \sigma^{2}}\right)
\]
and (2.13) implies
\[
\mathrm{E} V \leq L_{2} \sigma \sqrt{\log m}
\]

Now, for each \(\ell \leq m\),
\[
Y_{\ell} \geq \mathrm{E} Y_{\ell}-V \geq \min _{\ell \leq m} \mathrm{E} Y_{\ell}-V
\]
and thus
\[
\sup _{t \in H_{\ell}} X_{t}=Y_{\ell}+X_{t_{\ell}} \geq X_{t_{\ell}}+\min _{\ell \leq m} \mathrm{E} Y_{\ell}-V
\]
so that
\[
\sup _{t \in H} X_{t} \geq \max _{\ell \leq m} X_{t_{\ell}}+\min _{\ell \leq m} \mathrm{E} Y_{\ell}-V
\]

Taking expectations we obtain
\[
\mathrm{E} \sup _{t \in H} X_{t} \geq \mathrm{E} \max _{\ell \leq m} X_{t_{\ell}}+\min _{\ell \leq m} \mathrm{E} Y_{\ell}-\mathrm{E} V
\]
and we use (2.116) and (2.123).
Exercise 2.10.9. Prove that (2.120) might fail if one allows \(\sigma=a\). Hint: the intersection of the balls \(B\left(t_{\ell}, a\right)\) might contain a ball with positive radius.

Exercise 2.10.10. Consider subsets \(\left(H_{\ell}\right)_{\ell \leq m}\) of \(B(0, a)\) and \(H=\cup_{\ell \leq m} H_{\ell}\). Prove that
\[
\mathrm{E} \sup _{t \in H} X_{t} \leq L a \sqrt{\log m}+\max _{\ell \leq m} \mathrm{E} \sup _{t \in H_{\ell}} X_{t}
\]

Try to find improvements on this bound. Hint: peek at (19.61) below.
Proof of Theorem 2.10.1. We fix \(r=\max \left(8,4 L_{1} L_{2}\right)\), so that \(2 a / r \leq a / 2 L_{1} L_{2}\). The growth condition for the functional \(F\) of (2.115) follows from (2.120), which implies that (2.77) holds for \(c^{*}=1 / L\). Theorem 2.9.1 implies
\[
\gamma_{2}(T, d) \leq L \mathrm{E} \sup _{t \in T} X_{t}+L \Delta(T)
\]

To control the term \(\Delta(T)\) we write that for \(t_{1}, t_{2} \in H\),
\[
\mathrm{E} \max \left(X_{t_{1}}, X_{t_{2}}\right)=\mathrm{E} \max \left(X_{t_{1}}-X_{t_{2}}, 0\right)=\frac{1}{\sqrt{2 \pi}} d\left(t_{1}, t_{2}\right)
\]
so that \(\Delta(T) \leq \sqrt{2 \pi} \mathrm{E} \sup _{t \in T} X_{t}\).
The proof of Theorem 2.10.1 displays an interesting feature. This theorem aims at understanding \(\mathrm{E} \sup _{t \in T} X_{t}\), and for this we use functionals that are based on precisely this quantity. This is not a circular argument. The content of Theorem 2.10.1 is that there is simply no other way to bound a Gaussian process than to control the quantity \(\gamma_{2}(T, d)\). The miracle of this theorem is that it relates in complete generality two quantities, namely \(\mathrm{E}_{\sup _{t \in T}} X_{t}\) and \(\gamma_{2}(T, d)\) which are both very hard to estimate. Still, in concrete situations, to estimate these quantities, we must in some way gain understanding of the underlying geometry.

The following is a noteworthy consequence of Theorem 2.10.1.

Theorem 2.10.11. Consider two processes \(\left(Y_{t}\right)_{t \in T}\) and \(\left(X_{t}\right)_{t \in T}\) indexed by the same set. Assume that the process \(\left(X_{t}\right)_{t \in T}\) is Gaussian and that the process \(\left(Y_{t}\right)_{t \in T}\) satisfies the increment condition
\[
\forall u>0, \forall s, t \in T, \mathrm{P}\left(\left|Y_{s}-Y_{t}\right| \geq u\right) \leq 2 \exp \left(-\frac{u^{2}}{d(s, t)^{2}}\right)
\]
where \(d\) is the distance (2.113) associated to the process \(X_{t}\). Then we have
\[
\mathrm{E} \sup _{s, t \in T}\left|Y_{s}-Y_{t}\right| \leq L \mathrm{E} \sup _{t \in T} X_{t}
\]

Processes satisfying the condition (2.125) are sometimes called sub-Gaussian. We will see many examples later, see (6.2) below.

Proof. We combine (2.60) with the left-hand side of (2.114).
Let us also note the following consequence of (2.126) and Lemma 2.2.1. \({ }^{21}\)
Corollary 2.10.12. Consider two Gaussian processes \(\left(X_{t}\right)_{t \in T}\) and \(\left(Y_{t}\right)_{t \in T}\). Assume that
\[
\forall s, t \in T, \mathrm{E}\left(Y_{s}-Y_{t}\right)^{2} \leq \mathrm{E}\left(X_{s}-X_{t}\right)^{2}
\]

Then
\[
\mathrm{E} \sup _{t \in T} Y_{t} \leq L \mathrm{E} \sup _{t \in T} X_{t}
\]

\subsection*{2.11 Gaussian Processes as Subsets of a Hilbert Space}

In this section we learn to think of a Gaussian process as a subset of a Hilbert space. This will reveal our lack of understanding of basic geometric questions.

First, consider a Gaussian process \(\left(Y_{t}\right)_{t \in T}\) and assume (the only case which is of interest to us) that there is a countable set \(T^{\prime} \subset T\) which is dense in \(T\). We view each \(Y_{t}\) as a point in the Hilbert space \(L^{2}(\Omega, \mathrm{P})\) where \((\Omega, \mathrm{P})\) is the basic probability space. The closed linear span of the r.v.s \(\left(Y_{t}\right)_{t \in T}\) in \(L^{2}(\Omega, \mathrm{P})\) is a separable Hilbert space, and the map \(t \mapsto Y_{t}\) is an isometry from ( \(T, d\) ) to its image (by the very definition of the distance \(d\) ). In this manner we associate a subset of a Hilbert space to each Gaussian process.

Conversely, consider a separable Hilbert space, which we may assume to be \(\ell^{2}=\ell^{2}(\mathbb{N})\). Consider an independent sequence \(\left(g_{i}\right)_{i \geq 1}\) of standard Gaussian r.v.s We can then define the Gaussian process \(\left(X_{t}\right)_{t \in \ell^{2}}\), where
\[
X_{t}=\sum_{i \geq 1} t_{i} g_{i}
\]
(the series converges in \(L^{2}(\Omega)\) ). Thus
\(\overline{{ }^{21} \text { Please do not miss the comments at the end of Section 2.16. }}\)
\[
\mathrm{E} X_{t}^{2}=\sum_{i \geq 1} t_{i}^{2}=\|t\|^{2}
\]

In this manner, for each subset \(T\) of \(\ell^{2}\) we can consider the Gaussian process \(\left(X_{t}\right)_{t \in T}\). The distance induced on \(T\) by the process coincides with the distance of \(\ell^{2}\) by (2.129).

A subset \(T\) of \(\ell^{2}\) will always be provided with the distance induced by \(\ell^{2}\), so we may also write \(\gamma_{2}(T)\) rather than \(\gamma_{2}(T, d)\). We denote by conv \(T\) the convex hull of \(T\).

Theorem 2.11.1. For a subset \(T\) of \(\ell^{2}\), we have
\[
\gamma_{2}(\operatorname{conv} T) \leq L \gamma_{2}(T)
\]

Of course we also have \(\gamma_{2}(T) \leq \gamma_{2}(\operatorname{conv} T)\) since \(T \subset \operatorname{conv} T\).
Proof. To prove (2.130) we observe that since \(X_{a_{1} t_{1}+a_{2} t_{2}}=a_{1} X_{t_{1}}+a_{2} X_{t_{2}}\) we have
\[
\sup _{t \in \operatorname{conv} T} X_{t}=\sup _{t \in T} X_{t}
\]

We then use (2.114) to write
\[
\frac{1}{L} \gamma_{2}(\operatorname{conv} T) \leq \mathrm{E} \sup _{t \in \operatorname{conv} T} X_{t}=\mathrm{E} \sup _{t \in T} X_{t} \leq L \gamma_{2}(T)
\]

A basic problem is that it is absolutely not obvious how to construct an admissible sequence of partitions on conv \(T\) witnessing (2.130).

Research problem 2.11.2. Give a geometrical proof of (2.130).
What we mean by geometrical proof is a proof that does not use Gaussian processes but only the geometry of Hilbert space. The difficulty of the problem is that the structure of an admissible sequence which witnesses that \(\gamma_{2}(\operatorname{conv} T) \leq L \gamma_{2}(T)\) must depend on the "geometry" of the set \(T\). A really satisfactory argument would give a proof that holds in Banach spaces more general than Hilbert space, for example by providing a positive answer to the following, where the concept of \(q\)-smooth Banach space is explained in [80].

Research problem 2.11.3. Consider a 2 -smooth Banach space, and the distance \(d\) induced by its norm. Is it true that for each subset \(T\) of its unit ball one has \(\gamma_{2}(\operatorname{conv} T, d) \leq K \sqrt{\log \operatorname{card} T}\) ? More generally, is it true that for each finite subset \(T\) one has \(\gamma_{2}(\operatorname{conv} T, d) \leq K \gamma_{2}(T, d)\) ? (Here \(K\) may depend on the Banach space, but not on \(T\).)

Research problem 2.11.4. Still more generally, is it true that for a finite subset \(T\) of a \(q\)-smooth Banach space, one has \(\gamma_{q}(\operatorname{conv} T) \leq K \gamma_{q}(T)\) ?

Even when the Banach space is \(\ell^{p}\), I do not know the answer to these problems (unless \(p=2!\) ). (The Banach space \(\ell^{p}\) is 2 -smooth for \(p \geq 2\) and \(q\)-smooth for \(p<2\), where \(1 / p+1 / q=1\).) One concrete case is when the set \(T\) consists of the first \(N\) vectors of the unit basis of \(\ell^{p}\). It is possible to show in this case that \(\gamma_{q}(\operatorname{conv} T) \leq K(p)(\log N)^{1 / q}\), where \(1 / p+1 / q=1\). We leave this as a challenge to the reader. The proof here is pretty much the same as for the case \(p=q=2\) which was covered in Section 2.6.

Exercise 2.11.5. Prove that if \(a \geq 2\) we have \(\sum_{k \geq 1}(k+1)^{-a} \leq L 2^{-a}\).
We recall the \(\ell^{2}\) norm \(\|\cdot\|\) of (2.129). Here is a simple fact.
Proposition 2.11.6. Consider a sequence \(\left(t_{k}\right)_{k \geq 1}\) such that.
\[
\forall k \geq 1,\left\|t_{k}\right\| \leq 1 / \sqrt{\log (k+1)}
\]

Let \(T=\left\{ \pm t_{k}, k \geq 1\right\}\). Then \(\mathrm{E}_{\sup _{t \in T}} X_{t} \leq L\) and thus \(\mathrm{E}_{\sup _{t \in \operatorname{conv} T}} X_{t} \leq L\) by (2.131).
Proof. We have
\[
\mathrm{P}\left(\sup _{k \geq 1}\left|X_{t_{k}}\right| \geq u\right) \leq \sum_{k \geq 1} \mathrm{P}\left(\left|X_{t_{k}}\right| \geq u\right) \leq \sum_{k \geq 1} 2 \exp \left(-\frac{u^{2}}{2} \log (k+1)\right)
\]
since \(X_{t_{k}}\) is Gaussian with \(\mathrm{E} X_{t_{k}}^{2} \leq 1 / \log (k+1)\). For \(u \geq 2\), the right-hand side of (2.132) is at most \(L \exp \left(-u^{2} / L\right)\) by the result of Exercise 2.11.5, and as usual the conclusion follows from (2.6).
Exercise 2.11.7. Deduce Proposition 2.11.6 from (2.34). Hint: Use Exercise 2.4.1.

It is particularly frustrating not to be able to solve the following special instance of Problem 2.11.2.
Research problem 2.11.8. In the setting of Proposition 2.11.6 find a geometrical proof that \(\gamma_{2}(\operatorname{conv} T) \leq L\).

The following shows that the situation of Proposition 2.11.6 is in a sense generic.
Theorem 2.11.9. Consider a countable set \(T \subset \ell^{2}\), with \(0 \in T\). Then we can find a sequence \(\left(t_{k}\right)\) with
\[
\forall k \geq 1,\left\|t_{k}\right\| \sqrt{\log (k+1)} \leq L \mathrm{E} \sup _{t \in T} X_{t}
\]
and
\[
T \subset \operatorname{conv}\left(\left\{t_{k} ; k \geq 1\right\}\right)
\]

Furthermore we may assume that each \(t_{k}\) is a multiple of the difference of two elements of T. \({ }^{22}\)
\({ }^{22}\) This information is of secondary importance and will be used only much later.

Proof. By Theorem 2.10.1 we can find an admissible sequence ( \(\mathcal{A}_{n}\) ) of \(T\) with
\[
\forall t \in T, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq L \mathrm{E} \sup _{t \in T} X_{t}:=S
\]

We construct sets \(T_{n} \subset T\), such that each \(A \in \mathcal{A}_{n}\) contains exactly one element of \(T_{n}\). We ensure in the construction that \(T=\bigcup_{n \geq 0} T_{n}\) and that \(T_{0}=\{0\}\). (To do this, we simply enumerate the elements of \(T\) as \(\left(v_{n}\right)_{n \geq 1}\) with \(v_{0}=0\) and we ensure that \(v_{n}\) is in \(T_{n}\).) For \(n \geq 1\) consider the set \(U_{n}\) that consists of all the points
\[
2^{-n / 2} \frac{t-v}{\|t-v\|}
\]
where \(t \in T_{n}, v \in T_{n-1}\), and \(t \neq v\). Thus each element of \(U_{n}\) has norm \(2^{-n / 2}\), and \(U_{n}\) has at most \(N_{n} N_{n-1} \leq N_{n+1}\) elements. Let \(U=\bigcup_{n \geq 1} U_{n}\). Then since \(\sum_{\ell \leq n} N_{\ell+1} \leq N_{n+2}, U\) contains at most \(N_{n+2}\) elements of norm \(\geq 2^{-n / 2}\). We enumerate \(U\) as \(\left\{z_{k} ; k=1, \ldots\right\}\) where the sequence ( \(\left\|z_{k}\right\|\) ) is non-increasing, so that \(\left\|z_{k}\right\|<2^{-n / 2}\) for \(k>N_{n+2}\). Let us now prove that \(\left\|z_{k}\right\| \leq L / \sqrt{\log (k+1)}\). If \(k \leq N_{2}\) this holds because \(\left\|z_{k}\right\| \leq 1\). Assume then that \(k>N_{2}\) and let \(n \geq 0\) be the largest integer with \(k>N_{n+2}\). Then by definition of \(n\) we have \(k \leq N_{n+3}\) and thus \(2^{-n / 2} \leq L / \sqrt{\log k}\). But then \(\left\|z_{k}\right\| \leq 2^{-n / 2} \leq L / \sqrt{\log k}\), proving the required inequality.

Consider \(t \in T\), so that \(t \in T_{m}\) for some \(m \geq 0\). Writing \(\pi_{n}(t)\) for the unique element of \(T_{n} \cap A_{n}(t)\), since \(\pi_{0}(t)=0\) we have
\[
t=\sum_{1 \leq n \leq m} \pi_{n}(t)-\pi_{n-1}(t)=\sum_{1 \leq n \leq m} a_{n}(t) u_{n}(t)
\]
with \(a_{n}(t)=2^{n / 2}\left\|\pi_{n}(t)-\pi_{n-1}(t)\right\|\) and
\[
u_{n}(t)=2^{-n / 2} \frac{\pi_{n}(t)-\pi_{n-1}(t)}{\left\|\pi_{n}(t)-\pi_{n-1}(t)\right\|} \in U
\]

Since
\[
\sum_{1 \leq n \leq m} a_{n}(t) \leq \sum_{n \geq 1} 2^{n / 2} \Delta\left(A_{n-1}(t)\right) \leq 2 S
\]
and since \(u_{n}(t) \in U_{n} \subset U\) we see from (2.134) that
\[
t=\sum_{1 \leq n \leq m} a_{n}(t) u_{n}(t)+\left(2 S-\sum_{1 \leq n \leq m} a_{n}(t)\right) \times 0 \in 2 S \operatorname{conv}(U \cup\{0\})
\]

Thus \(T \subset 2 S \operatorname{conv}(U \cup\{0\})=\operatorname{conv}(2 S U \cup\{0\})\), and it suffices to take \(t_{k}=2 S z_{k}\).

Exercise 2.11.10. What is the purpose of the condition \(0 \in T\) ?

Exercise 2.11.11. Prove that if \(T \subset \ell^{2}\) and \(0 \in T\), then (even when \(T\) is not countable) we can find a sequence ( \(t_{k}\) ) in \(\ell^{2}\), with \(\left\|t_{k}\right\| \sqrt{\log (k+1)} \leq\) \(L E \sup _{t \in T} X_{t}\) for all \(k\) and
\[
T \subset \overline{\operatorname{conv}}\left\{t_{k} ; k \geq 1\right\}
\]
where \(\overline{\text { conv }}\) denotes the closed convex hull. (Hint: do the obvious thing, apply Theorem 2.11.9 to a dense countable subset of \(T\).) Denoting now by \(\operatorname{conv}^{*}(A)\) the set of infinite sums \(\sum_{i} \alpha_{i} a_{i}\) where \(\sum_{i}\left|\alpha_{i}\right|=1\) and \(a_{i} \in A\), prove that one can also achieve
\[
T \subset \operatorname{conv}^{*}\left\{t_{k} ; k \geq 1\right\}
\]

Exercise 2.11.12. Consider a set \(T \subset \ell^{2}\) with \(0 \in T \subset B(0, \delta)\). Prove that we can find a sequence ( \(t_{k}\) ) in \(\ell^{2}\), with the following properties:
\[
\begin{gathered}
\forall k \geq 1,\left\|t_{k}\right\| \sqrt{\log (k+1)} \leq L \mathrm{E} \sup _{t \in T} X_{l} \\
\left\|t_{k}\right\| \leq L \delta \\
T \subset \overline{\operatorname{conv}}\left\{t_{k} ; k \geq 1\right\}
\end{gathered}
\]
where \(\overline{\text { conv }}\) denotes the closed convex hull. Hint: copy the proof of Theorem 2.11.9, observing that since \(T \subset B(0, \delta)\) one may chose \(\mathcal{A}_{n}=\{T\}\) and \(T_{n}=\{0\}\) for \(n \leq n_{0}\), where \(n_{0}\) is the smallest integer for which \(2^{n_{0} / 2} \geq \delta^{-1} \mathrm{E} \sup _{t \in T} X_{t}\), and thus \(U_{n}=\emptyset\) for \(n \leq n_{0}\).

The following problems are closely related to Problem (2.11.2).
Research problem 2.11.13. Give a geometric proof of the following fact. Given subsets \(\left(T_{k}\right)_{k \leq N}\) of a Hilbert space, and \(T=\sum_{k \leq N} T_{k}=\left\{x_{1}+\ldots+\right.\) \(\left.x_{N} ; \forall k \leq N, x_{k} \in T_{k}\right\}\), prove that \(\gamma_{2}(T) \leq L \sum_{k \leq N} \gamma_{2}\left(\overline{T_{k}}\right)\).
We do not even know how to solve the following special case.
Research problem 2.11.14. Consider sets \(\left(T_{k}\right)_{k \leq N}\) in a Hilbert space, and assume that each \(T_{k}\) consists of \(M\) vectors of length 1 . Let \(T=\sum_{k \leq N} T_{k}\). Give a geometrical proof of the fact that \(\gamma_{2}(T) \leq L N \sqrt{\log M}\).

The next exercise is inspired by the paper [9] of S Artstein. It is more elaborate, and may be omitted on first reading. A Bernoulli r.v. \(\varepsilon\) is such that \(\mathrm{P}(\varepsilon= \pm 1)=1 / 2 .^{23}\)

Exercise 2.11.15. Consider a subset \(T \subset \mathbb{R}^{n}\), where \(\mathbb{R}^{n}\) is provided with the Euclidean distance. We assume that for some \(\delta>0\), we have
\[
0 \in T \subset B(0, \delta)
\]

\footnotetext{
\({ }^{23}\) One must distinguish Bernoulli r.v.s \(\varepsilon_{i}\) from positive numbers \(\epsilon_{k}\) !
}

Consider independent Bernoulli r.v.s \(\left(\varepsilon_{i, p}\right)_{i, p \geq 1}\). Given a number \(q \leq n\) consider the operator \(U_{q}: \mathbb{R}^{n} \rightarrow \mathbb{R}^{q}\) given by
\[
U_{q}(x)=\left(\sum_{i \leq n} \varepsilon_{i, p} x_{i}\right)_{p \leq q}
\]
(a) Prove that \(\left\|U_{q}\right\| \geq \sqrt{n}\).

We want to prove that despite (a) there exists a number \(L\) such that if \(\mathrm{E}_{\sup _{t \in T}} \sum_{i \leq n} g_{i} t_{i} \leq \delta \sqrt{q}\), then with high probability
\[
U_{q}(T) \subset B(0, L \delta \sqrt{q})
\]
whereas from (2.138) we would not expect better than \(U_{q}(T) \subset B(0, \delta \sqrt{n})\). (b) Use the subgaussian inequality (6.1.1) to prove that if \(\|x\|=1\), then
\[
\mathrm{E} \exp \left(\frac{1}{4}\left(\sum_{i \leq n} \varepsilon_{i, p} x_{i}\right)^{2}\right) \leq L
\]
(c) Use (2.140) and independence to prove that for \(x \in \mathbb{R}^{n}\) and \(v \geq 1\),
\[
\mathrm{P}\left(\left\|U_{q}(x)\right\| \geq L v \sqrt{q}\|x\|\right) \leq \exp \left(-v^{2} q\right)
\]
(d) Use (2.141) to prove that with probability close to 1 , for each of the vectors \(t_{k}\) of Exercise 2.11.12 one has \(\left\|U_{q}\left(t_{k}\right)\right\| \leq L \delta \sqrt{q}\) and conclude.

We end this section with a discussion of a question which shares some features with Problem 2.11.2, in a sense that it is a property which is obvious on one hand, but difficult to prove without using the magic of linearity. \({ }^{24}\) For \(k \leq N\) let us consider Gaussian processes \(\left(X_{k, t}\right)_{t \in T_{k}}\) with associated distances \(d_{k}\). On the space \(T=\prod_{k \leq N} T_{k}\) let us consider the distance \(d\) given by
\[
d\left(\left(t_{k}\right)_{k \leq N},\left(t_{k}^{\prime}\right)_{k \leq N}\right)=\left(\sum_{k \leq N} d_{k}\left(t_{k}, t_{k}^{\prime}\right)^{2}\right)^{1 / 2}
\]

Proposition 2.11.16. We have
\[
\gamma_{2}(T, d) \leq L \sum_{k \leq N} \gamma_{2}\left(T_{k}, d_{k}\right)
\]

Proof. Assuming without loss of generality that the processes \(\left(X_{k}\right)_{k \leq N}\) are independent we can consider the Gaussian process \(\left(X_{t}\right)_{t \in T}\) given for \(t=\) \(\left(t_{k}\right)_{k \leq N}\) by \(X_{t}=\sum_{k \leq N} X_{k, t_{k}}\). It is obvious that the distance \(d\) of (2.143) is associated to this process. It is also obvious that
\[
\sup _{t \in T} X_{t}=\sum_{k \leq N} \sup _{t \in T_{k}} X_{t_{k}}
\]

Taking expectation and combining with (2.114) concludes the proof.

\footnotetext{
\({ }^{24}\) There are several equally frustrating instances of this situation.
}

The question now is to prove (2.143) without using Gaussian processes, for example by proving it for any sequence \(\left(\left(T_{k}, d_{k}\right)\right)_{k \leq N}\) of metric spaces. The most interesting part of that project is that it is unexpectedly hard. Is it the sign that we are still missing an important ingredient? In the next exercise we show how to prove about the simplest possible case of (2.143), which is already pretty challenging.

Exercise 2.11.17. Throughout this exercise each space \(T_{k}\) consists of \(M_{k}\) points, and the mutual distance of any two different points of \(T_{k}\) is \(\epsilon_{k}>0\). The goal is to prove the inequality
\[
\int \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon \leq L \sum_{k \leq N} \epsilon_{k} \sqrt{\log M_{k}}
\]

Throughout the exercise \(I\) denotes a subset of \(\{1, \ldots, N\}\) and \(I^{c}\) denotes its complement.
(a) Prove that if \(\sum_{k \in I} \epsilon_{k}^{2} \leq \epsilon^{2}\) then \(N(T, d, \epsilon) \leq \prod_{k \in I^{c}} M_{k}\).
(b) Show that to prove (2.144) it suffices to prove the following. Consider two sequences \(\left(\epsilon_{k}\right)_{k \leq N}\) and \(\left(\eta_{k}\right)_{k \leq N}\). For \(\epsilon>0\) define \(S(\epsilon)\) by
\[
S(\epsilon)^{2}=\inf \left\{\sum_{k \in I^{c}} \eta_{k}^{2} ; \sum_{k \in I} \epsilon_{k}^{2} \leq \epsilon^{2}\right\}
\]
where the infimum is over all choices of \(I\). Then
\[
\int_{0}^{\infty} S(\epsilon) \mathrm{d} \epsilon \leq L \sum_{k \leq N} \epsilon_{k} \eta_{k}
\]
(c) To prove (2.145), show that it suffices to prove the following. Consider a function \(h>0\) on a probability space. For \(\epsilon>0\) define \(\tilde{S}(\epsilon)\) by
\[
\tilde{S}(\epsilon)^{2}=\inf \left\{\int_{A^{c}} \frac{1}{h} \mathrm{~d} \mu ; \int_{A} h \mathrm{~d} \mu \leq \epsilon^{2}\right\}
\]
where the infimum is over all choices of \(A\). Then \(\int_{0}^{\infty} \tilde{S}(\epsilon) \mathrm{d} \epsilon \leq L\). Hint: Reduce to the case where \(\sum_{k \leq N} \epsilon_{k} \eta_{k}=1\). Use the probability \(\mu\) on \(\{1, \ldots, N\}\) such that \(\mu(\{k\})=\epsilon_{k} \eta_{k}\) and the function \(h\) given by \(h(k)=\epsilon_{k} / \eta_{k}\).
(d) Show that it suffices to prove that \(\sum_{\ell \in \mathbb{Z}} 2^{-\ell} \tilde{S}\left(2^{-\ell}\right) \leq L\).
(e) Assuming for simplicity that \(\mu\) has no atoms \({ }^{25}\) prove the statement given in (d). Hint: For \(\ell \in \mathbb{Z}\) and \(2^{-2 \ell} \leq \int h \mathrm{~d} \mu\) consider the set \(A_{\ell}\) of the type \(A_{\ell}=\left\{h \leq t_{\ell}\right\}\) where \(t_{\ell}\) is such that \(\int_{A_{\ell}} h \mathrm{~d} \mu=2^{-2 \ell}\), so that \(\tilde{S}\left(2^{-\ell}\right)^{2} \leq\) \(\int_{A_{\ell}^{c}}(1 / h) \mathrm{d} \mu\). Warning: this is not easy.
\({ }^{25}\) I am sure that this is true without this hypothesis, but I did not find the energy to carry out the details.

Exercise 2.11.18. This exercise continues the previous one. The spaces \(\left(T_{k}, d_{k}\right)\) are now any metric spaces, and the goal is to prove that
\[
\int_{0}^{\infty} \sqrt{\log N(T, d, \epsilon)} \mathrm{d} \epsilon \leq L \sum_{k \leq N} \int_{0}^{\infty} \sqrt{\log N\left(T_{k}, d_{k}, \epsilon\right)} \mathrm{d} \epsilon
\]

Proving this requires passing the main difficulty of proving (2.143), but to prove (2.143) itself, it will be convenient to use different tools, and that proof is the object of Exercise 3.1.6.
(a) Show that to prove (2.146) it suffices to prove the following. Consider decreasing functions \(f_{k}: \mathbb{R}^{+} \rightarrow \mathbb{R}^{+}\)and for \(\epsilon>0\) define \(V(\epsilon)\) by
\[
V(\epsilon)^{2}=\inf \left\{\sum_{k \leq N} f_{k}\left(\epsilon_{k}\right)^{2} ; \sum_{k \leq N} \epsilon_{k}^{2} \leq \epsilon^{2}\right\}
\]
where the infimum is taken over all families \(\left(\epsilon_{k}\right)_{k \leq N}\). Then
\[
\int_{0}^{\infty} V(\epsilon) \mathrm{d} \epsilon \leq L \sum_{k \leq N} \int_{0}^{\infty} f_{k}(\epsilon) \mathrm{d} \epsilon
\]
(b) When each \(f_{k}\) is of the type \(f_{k}=\eta_{k} \mathbf{1}_{\left[0, \theta_{k}\right]}\), deduce (2.146) from (2.145).
(c) Convince yourself that by approximation it suffices to consider the case where each \(f_{k}\) is a finite sum \(\sum_{\ell} 2^{-\ell} \mathbf{1}_{\left[0, \theta_{k \ell}[ \right.}\).
(d) In the case (c) prove (2.147) by applying the special case (b) to the family \(f_{k, \ell}\) of functions given by \(f_{k, \ell}:=2^{-\ell} \mathbf{1}_{\left[0, \theta_{k, \ell}\right]}\) for all relevant values of \(k, \ell\). Hint: this is a bit harder.

\subsection*{2.12 Dreams}

We may reformulate the inequality (2.114)
\[
\frac{1}{L} \gamma_{2}(T, d) \leq \mathrm{E} \sup _{t \in T} X_{t} \leq L \gamma_{2}(T, d)
\]
of Theorem 2.10.1 by the statement
Chaining suffices to explain the size of a Gaussian process.
We simply mean that the "natural" chaining bound for the size of a Gaussian process (i.e. the right-hand side inequality in (2.114)) is of correct order, provided one uses the best possible chaining. This is what the left-hand side of (2.114) shows. We may dream of removing the word "Gaussian" in that statement. The desire to achieve this lofty goal in as many situations as possible motivates much of the rest of the book.

Besides the generic chaining, we have found in Theorem 2.11.9 another optimal way to bound Gaussian processes: to put them into the convex hull of a "small" process, that is to use the inequality
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \inf \left\{S ; T \subset \operatorname{conv}\left\{t_{k}, k \geq 1\right\},\left\|t_{k}\right\| \leq S / \sqrt{\log (k+1)}\right\} .
\]

Since we do not really understand the geometry of going from a set to its convex hull, it is better (for the time being) to consider this method as somewhat distinct from the generic chaining. Let us try to formulate it in a way which is suitable for generalizations. Given a countable set \(\mathcal{V}\) of r.v.s, let us define the (possibly infinite) quantity
\[
S(\mathcal{V})=\inf \left\{S>0 ; \int_{S}^{\infty} \sum_{V \in \mathcal{V}} \mathrm{P}(|V|>u) \mathrm{d} u \leq S\right\}
\]

Lemma 2.12.1. It holds that
\[
\mathrm{E} \sup _{V \in \operatorname{conv} \mathcal{V}}|V| \leq 2 S(\mathcal{V}) .
\]

Proof. We combine (2.6) with the fact that for \(S>S(\mathcal{V})\) we have
\[
\int_{0}^{\infty} \mathrm{P}\left(\sup _{V \in \text { conv } \mathcal{V}}|V| \geq u\right) \mathrm{d} u \leq S+\int_{S}^{\infty} \sum_{V \in \mathcal{V}} \mathrm{P}(|V|>u) \mathrm{d} u \leq 2 S
\]

Thus (2.150) provides a method to bound stochastic processes. This method may look childish, but for Gaussian processes, the following reformulation of Theorem 2.11.9 shows that it is in fact optimal.

Theorem 2.12.2. Consider a countable set \(T\). Consider a Gaussian process \(\left(X_{t}\right)_{t \in T}\) and assume that \(X_{t_{0}}=0\) for some \(t_{0} \in T\). Then there exists a countable set \(\mathcal{V}\) of Gaussian r.v.s, each of which is a multiple of the difference of two variables \(X_{t}\), with
\[
\begin{gathered}
\forall t \in T ; X_{t} \in \operatorname{conv} \mathcal{V} \\
S(\mathcal{V}) \leq L \mathrm{E} \sup _{t \in T} X_{t}
\end{gathered}
\]

To understand the need of the condition \(X_{t_{0}}=0\) for some \(t_{0}\) think of the case where \(T\) consists of one single point. The proof of Theorem 2.12.2 is nearly obvious by using (2.132) to bound \(S(\mathcal{V})\) for the set \(\mathcal{V}\) consisting of the variables \(X_{t_{k}}\) for the sequence ( \(t_{k}\) ) constructed in Theorem 2.11.9. We may dream of proving statements such as Theorem 2.12.2 for many classes of processes.

Also worthy of detailing is another remarkable geometric consequence of Theorem 2.11.9 in a somewhat different direction. Consider an integer \(N\).

Considering i.i.d. standard Gaussian r.v.s, we define as usual the process \(X_{t}=\sum_{i \leq N} g_{i} t_{i}\). We may view an element \(t\) of \(\ell_{N}^{2}\) as a function on \(\ell_{N}^{2}\) by the canonical duality, and therefore view \(t\) as a r.v. on the probability space \(\left(\ell_{N}^{2}, \mu\right)\), where \(\mu\) is the law of the sequence \(\left(g_{i}\right)_{i \leq N}\). The processes \(\left(X_{t}\right)\) and \((t)\) have the same law, hence they are really the same object viewed in two different ways. Consider a subset \(T\) of \(\ell_{N}^{2}\), and assume that \(T \subset \operatorname{conv}\left\{t_{k} ; k \geq\right.\) \(1\}\). Then for any \(v>0\) we have
\[
\left\{\sup _{t \in T} t>v\right\} \subset \bigcup_{k \geq 1}\left\{t_{k}>v\right\}
\]

The sets \(\left\{t_{k} \geq v\right\}\) on the right are very simple: they are half-spaces. Assume now that for \(k \geq 1\) and a certain \(S\) we have \(\left\|t_{k}\right\| \sqrt{\log (k+1)} \leq S\). Then for \(u \geq 2\)
\[
\sum_{k \geq 1} \mu\left(\left\{t_{k} \geq S u\right\}\right) \leq \sum_{k \geq 1} \exp \left(-\frac{u^{2}}{2} \log (k+1)\right) \leq L \exp \left(-u^{2} / L\right)
\]
the very same computation as in (2.132). Theorem 2.11.9 implies that one may find such \(t_{k}\) for \(S=L \mathrm{E} \sup _{t} X_{t}\). Therefore for \(v \geq L \mathrm{E} \sup _{t} X_{t}\), the fact that the set in the left-hand side of (2.153) is small (in the sense of probability) may be witnessed by the fact that this set can be covered by a union of simple sets (half-spaces) the sum of the probabilities of which is small.

We may dream that something similar occurs in many other settings. In Chapter 13, which can be read right now, we will meet a fundamental such setting, which inspired the author's lifetime favorite problem, see Section 13.3.

\subsection*{2.13 A First Look at Ellipsoids}

We have illustrated the gap between Dudley's bound (2.41) and the sharper bound (2.34), using the examples (2.49) and (2.42). These examples might look artificial, but here we demonstrate that the gap between Dudley's bound (2.41) and the generic chaining bound (2.34) already exists for ellipsoids in Hilbert space. Truly understanding ellipsoids will be fundamental in several subsequent questions, such as the matching theorems of Chapter 4. A further study of ellipsoids is proposed in Section 3.2.

Given a sequence \(\left(a_{i}\right)_{i \geq 1}, a_{i}>0\), we consider the ellipsoid
\[
\mathcal{E}=\left\{t \in \ell^{2} ; \sum_{i \geq 1} \frac{t_{i}^{2}}{a_{i}^{2}} \leq 1\right\}
\]

Proposition 2.13.1. When \(\sum_{i \geq 1} a_{i}^{2}<\infty\) we have
\[
\frac{1}{L}\left(\sum_{i \geq 1} a_{i}^{2}\right)^{1 / 2} \leq \mathrm{E} \sup _{t \in \mathcal{E}} X_{t} \leq\left(\sum_{i \geq 1} a_{i}^{2}\right)^{1 / 2}
\]

Proof. The Cauchy-Schwarz inequality implies
\[
Y:=\sup _{t \in \mathcal{E}} X_{t}=\sup _{t \in \mathcal{E}} \sum_{i \geq 1} t_{i} g_{i} \leq\left(\sum_{i \geq 1} a_{i}^{2} g_{i}^{2}\right)^{1 / 2} .
\]

Taking \(t_{i}=a_{i}^{2} g_{i} /\left(\sum_{j \geq 1} a_{j}^{2} g_{j}^{2}\right)^{1 / 2}\) yields that actually \(Y=\left(\sum_{i \geq 1} a_{i}^{2} g_{i}^{2}\right)^{1 / 2}\) and thus \(\mathrm{E} Y^{2}=\sum_{i \geq 1} a_{i}^{2}\). The right-hand side of (2.155) follows from the Cauchy-Schwarz inequality:
\[
\mathrm{E} Y \leq\left(\mathrm{E} Y^{2}\right)^{1 / 2}=\left(\sum_{i \geq 1} a_{i}^{2}\right)^{1 / 2}
\]

For the left-hand side, let \(\sigma=\max _{i \geq 1}\left|a_{i}\right|\). Since \(Y=\sup _{t \in \mathcal{E}} X_{t} \geq\left|a_{i}\right|\left|g_{i}\right|\) for any \(i\), we have \(\sigma \leq L \mathrm{E} Y\). Also,
\[
\mathrm{E} X_{t}^{2}=\sum_{i} t_{i}^{2} \leq \max _{i} a_{i}^{2} \sum_{j} \frac{t_{j}^{2}}{a_{j}^{2}} \leq \sigma^{2} .
\]

Then (2.118) implies \({ }^{26}\)
\[
\mathrm{E}(Y-\mathrm{E} Y)^{2} \leq L \sigma^{2} \leq L(\mathrm{E} Y)^{2},
\]
so that \(\sum_{i \geq 1} a_{i}^{2}=\mathrm{E} Y^{2}=\mathrm{E}(Y-\mathrm{E} Y)^{2}+(\mathrm{E} Y)^{2} \leq L(\mathrm{E} Y)^{2}\).
As a consequence of Theorem 2.10.1,
\[
\gamma_{2}(\mathcal{E}) \leq L\left(\sum_{i \geq 1} a_{i}^{2}\right)^{1 / 2} .
\]

This statement is purely about the geometry of ellipsoids. The proof we gave was rather indirect, since it involved Gaussian processes. Later on, in Chapter 4 we will learn how to give "purely geometric" proofs of similar statements that will have many consequences.

Let us now assume that the sequence \(\left(a_{i}\right)_{i \geq 1}\) is non-increasing. Since
\[
2^{n} \leq i \leq 2^{n+1} \Rightarrow a_{2^{n}} \geq a_{i} \geq a_{2^{n+1}}
\]
we get

\footnotetext{
\({ }^{26}\) One may extend (2.118) to the case where \(U\) is infinite by a proper definition of \(\sup _{t \in U} X_{t}\).
}
\[
\sum_{i \geq 1} a_{i}^{2}=\sum_{n \geq 0} \sum_{2^{n} \leq i<2^{n+1}} a_{i}^{2} \leq \sum_{n \geq 0} 2^{n} a_{2^{n}}^{2}
\]
and
\[
\sum_{i \geq 1} a_{i}^{2} \geq \sum_{n \geq 0} 2^{n} a_{2^{n+1}}^{2}=\frac{1}{2} \sum_{n \geq 1} 2^{n} a_{2^{n}}^{2}
\]
and thus \(\sum_{n \geq 0} 2^{n} a_{2^{n}}^{2} \leq 3 \sum_{i \geq 1} a_{i}^{2}\). So we may rewrite (2.155) as
\[
\frac{1}{L}\left(\sum_{n \geq 0} 2^{n} a_{2^{n}}^{2}\right)^{1 / 2} \leq \mathrm{E} \sup _{t \in \mathcal{E}} X_{t} \leq\left(\sum_{n \geq 0} 2^{n} a_{2^{n}}^{2}\right)^{1 / 2}
\]

Proposition 2.13.1 describes the size of ellipsoids with respect to Gaussian processes. Our next result describes their size with respect to Dudley's entropy bound (2.38).

Proposition 2.13.2. We have
\[
\frac{1}{L} \sum_{n \geq 0} 2^{n / 2} a_{2^{n}} \leq \sum_{n \geq 0} 2^{n / 2} e_{n}(\mathcal{E}) \leq L \sum_{n \geq 0} 2^{n / 2} a_{2^{n}}
\]

The right-hand sides in (2.160) and (2.161) are distinctly different. \({ }^{27}\) Dudley's bound fails to describe the behavior of Gaussian processes on ellipsoids. This is a simple occurrence of a general phenomenon. In some sense an ellipsoid is smaller than what one would predict just by looking at its entropy numbers \(e_{n}(\mathcal{E})\). This idea will be investigated further in Section 4.1.

Exercise 2.13.3. Prove that for an ellipsoid \(\mathcal{E}\) of \(\mathbb{R}^{m}\) one has
\[
\sum_{n \geq 0} 2^{n / 2} e_{n}(\mathcal{E}) \leq L \sqrt{\log (m+1)} \gamma_{2}(\mathcal{E}, d)
\]
and that this estimate is essentially optimal. Compare with (2.58).
The proof of (2.161) hinges on ideas which are at least 50 years old, and which relate to the methods of Exercise 2.5.9. The left-hand side is the easier part (it is also the most important for us). It follows from the next lemma, the proof of which is basically a special case of (2.45).

Lemma 2.13.4. We have \(e_{n}(\mathcal{E}) \geq \frac{1}{2} a_{2^{n}}\).
Proof. Consider the following ellipsoid in \(\mathbb{R}^{2^{n}}\) :
\[
\mathcal{E}_{n}=\left\{\left(t_{i}\right)_{i \leq 2^{n}} ; \sum_{i \leq 2^{n}} \frac{t_{i}^{2}}{a_{i}^{2}} \leq 1\right\}
\]

\footnotetext{
\({ }^{27}\) This difference may seem rather small, but, as we shall see in Chapter 4, there are natural situations where it really matters.
}

Since \(\mathcal{E}_{n}\) is the image of \(\mathcal{E}\) by a contraction \({ }^{28}\) (namely the "projection on the first \(2^{n}\) coordinates") it holds that \(e_{n}\left(\mathcal{E}_{n}\right) \leq e_{n}(\mathcal{E})\).

Throughout the rest of this section we denote by \(B\) the centered unit Euclidean ball of \(\mathbb{R}^{2^{n}}\) and by Vol the volume in this space. Let us consider a subset \(T\) of \(\mathcal{E}_{n}\), with card \(T \leq 2^{2^{n}}\), and \(\epsilon>0\); then
\[
\operatorname{Vol}\left(\bigcup_{t \in T}(\epsilon B+t)\right) \leq \sum_{t \in T} \operatorname{Vol}(\epsilon B+t) \leq 2^{2^{n}} \epsilon^{2^{n}} \operatorname{Vol} B=(2 \epsilon)^{2^{n}} \operatorname{Vol} B .
\]

Since we have assumed that the sequence ( \(a_{i}\) ) is non-increasing, we have \(a_{i} \geq a_{2^{n}}\) for \(i \leq 2^{n}\) and thus \(a_{2^{n}} B \subset \mathcal{E}_{n}\), so that \(\operatorname{Vol} \mathcal{E}_{n} \geq a_{2^{n}}^{2^{n}} \operatorname{Vol} B\). Thus, whenever \(2 \epsilon<a_{2^{n}}\), we cannot have \(\mathcal{E}_{n} \subset \bigcup_{t \in T}(\epsilon B+t)\), so that \(e_{n}\left(\mathcal{E}_{n}\right) \geq\) \(a_{2^{n}} / 2\).

We now turn to the upper bound, which relies on a special case of (2.46). We keep the notation of the proof of Lemma 2.13.4.

Lemma 2.13.5. We have
\[
e_{n+3}(\mathcal{E}) \leq e_{n+3}\left(\mathcal{E}_{n}\right)+a_{2^{n}} .
\]

Proof. We observe that when \(t \in \mathcal{E}\), then, using that \(a_{i} \leq a_{2^{n}}\) for \(i>2^{n}\) in the last inequality,
\[
1 \geq \sum_{i \geq 1} \frac{t_{i}^{2}}{a_{i}^{2}} \geq \sum_{i>2^{n}} \frac{t_{i}^{2}}{a_{i}^{2}} \geq \frac{1}{a_{2^{n}}^{2}} \sum_{i>2^{n}} t_{i}^{2}
\]
so that \(\left(\sum_{i>2^{n}} t_{i}^{2}\right)^{1 / 2} \leq a_{2^{n}}\) and, viewing \(\mathcal{E}_{n}\) as a subset of \(\mathcal{E}\), we have \(d\left(t, \mathcal{E}_{n}\right) \leq a_{2^{n}}\). Thus if for \(k \geq 1\) we cover \(\mathcal{E}_{n}\) by \(N_{k}\) balls of radius \(\epsilon\), the balls with the same centers but radius \(\epsilon+a_{2^{n}}\) cover \(\mathcal{E}\). This proves that \(e_{k}\left(\mathcal{E}_{n}\right) \leq e_{k}(\mathcal{E})+a_{2^{n}}\) and hence (2.162).

Lemma 2.13.6. Let \(\epsilon=\max _{k \leq n} a_{2^{k}} 2^{k-n}\). Consider a subset \(A\) of \(\mathcal{E}_{n}\) with the following property:
\[
\text { any two points of } A \text { are at mutual distance } \geq 2 \epsilon .
\]

Then \(\operatorname{card} A \leq N_{n+3}\).
Proof. The balls centered at the points of \(A\), with radius \(\epsilon\), have disjoint interiors, so that the volume of their union is \(\operatorname{card} A \operatorname{Vol}(\epsilon B)\), and since these balls are entirely contained in \(\mathcal{E}_{n}+\epsilon B\) we have
\[
\operatorname{card} A \operatorname{Vol}(\epsilon B) \leq \operatorname{Vol}\left(\mathcal{E}_{n}+\epsilon B\right) .
\]

\footnotetext{
\({ }^{28}\) Generally speaking, a map \(\varphi\) from a metric space ( \(T, d\) ) to a metric space ( \(T^{\prime}, d^{\prime}\) ) is called a contraction if \(d^{\prime}(\varphi(x), \varphi(y)) \leq d(x, y)\).
}

For \(t=\left(t_{i}\right)_{i \leq 2^{n}} \in \mathcal{E}_{n}\), we have \(\sum_{i \leq 2^{n}} t_{i}^{2} / a_{i}^{2} \leq 1\), and for \(t^{\prime}\) in \(\epsilon B\), we have \(\sum_{i \leq 2^{n}} t_{i}^{\prime 2} / \epsilon^{2} \leq 1\). Let \(c_{i}=2 \max \left(\epsilon, a_{i}\right)\). Since
\[
\sum_{i \leq 2^{n}} \frac{\left(t_{i}+t_{i}^{\prime}\right)^{2}}{c_{i}^{2}} \leq \sum_{i \leq 2^{n}} \frac{2 t_{i}^{2}+2 t_{i}^{\prime 2}}{c_{i}^{2}} \leq \sum_{i \leq 2^{n}} \frac{1}{2}\left(\frac{t_{i}^{2}}{a_{i}^{2}}+\frac{t_{i}^{\prime 2}}{\epsilon^{2}}\right) \leq 1
\]
we have
\[
\mathcal{E}_{n}+\epsilon B \subset \mathcal{E}^{1}:=\left\{t ; \sum_{i \leq 2^{n}} \frac{t_{i}^{2}}{c_{i}^{2}} \leq 1\right\}
\]

Therefore
\[
\operatorname{Vol}\left(\mathcal{E}_{n}+\epsilon B\right) \leq \operatorname{Vol} \mathcal{E}^{1}=\operatorname{Vol} B \prod_{i \leq 2^{n}} c_{i}
\]
and comparing with (2.164) yields
\[
\operatorname{card} A \leq \prod_{i \leq 2^{n}} \frac{c_{i}}{\epsilon}=2^{2^{n}} \prod_{i \leq 2^{n}} \max \left(1, \frac{a_{i}}{\epsilon}\right) .
\]

Next it follows from the choice of \(\epsilon\) that for any \(k \leq n\) we have \(a_{2^{k}} 2^{k-n} \leq \epsilon\).
Then \(a_{i} \leq a_{2^{k}} \leq \epsilon 2^{n-k}\) for \(2^{k}<i \leq 2^{k+1}\), so that
\[
\begin{aligned}
\prod_{i \leq 2^{n}} \max \left(1, \frac{a_{i}}{\epsilon}\right) & =\prod_{k \leq n-1} \prod_{2^{k}<i \leq 2^{k+1}} \max \left(1, \frac{a_{i}}{\epsilon}\right) \\
& \leq \prod_{k \leq n-1}\left(2^{n-k}\right)^{2^{k}}=2^{\sum_{k \leq n-1}(n-k) 2^{k}} \leq 2^{2^{n+2}}
\end{aligned}
\]
since \(\sum_{i \geq 0} i 2^{-i}=4\). Therefore \(\operatorname{card} A \leq 2^{2^{n}} \cdot 2^{2^{n+2}} \leq N_{n+3}\).
Lemma 2.13.7. We have
\[
e_{n+3}\left(\mathcal{E}_{n}\right) \leq 2 \max _{k \leq n}\left(a_{2^{k}} 2^{k-n}\right)
\]

Proof. Assume now that \(A\) is as large as possible under (2.163). Then the balls centered at points of \(A\) and with radius \(\leq 2 \epsilon\) cover \(\mathcal{E}_{n}\), for otherwise we could add a point to \(A\). Since \(\operatorname{card} A \leq N_{n+3}\) we have \(e_{n+3}\left(\mathcal{E}_{n}\right) \leq 2 \epsilon\).
Combining (2.165) with (2.162) we obtain
Corollary 2.13.8. We have
\[
e_{n+3}(\mathcal{E}) \leq 3 \max _{k \leq n}\left(a_{2^{k}} 2^{k-n}\right)
\]

Proof of Proposition 2.13.2. We have, using (2.166)
\[
\begin{aligned}
\sum_{n \geq 3} 2^{n / 2} e_{n}(\mathcal{E}) & =\sum_{n \geq 0} 2^{(n+3) / 2} e_{n+3}(\mathcal{E}) \leq L \sum_{n \geq 0} 2^{n / 2}\left(\sum_{k \leq n} 2^{k-n} a_{2^{k}}\right) \\
& =L \sum_{k \geq 0} 2^{k} a_{2^{k}} \sum_{n \geq k} 2^{-n / 2} \leq L \sum_{k \geq 0} 2^{k / 2} a_{2^{k}}
\end{aligned}
\]

Since \(\mathcal{E}\) is contained in the ball centered at the origin with radius \(a_{1}\), we have \(e_{n}(\mathcal{E}) \leq a_{1}\) for each \(n\). The result follows.

\subsection*{2.14 Rolling Up our Sleeves: Chaining on Ellipsoids}

Let us recall the ellipsoid \(\mathcal{E}\) of (2.154). We have proved (2.159) as a consequence of the Majorizing Measure Theorem, Theorem 2.10.1. We will later give a more geometrical proof of this result. In the present section we demonstrate the hard way that these results are deep, by explicitly constructing a chaining on the ellipsoid \(\mathcal{E} .^{29}\) This is surprisingly non-trivial. \({ }^{30}\) Let us assume that the sequence ( \(a_{i}\) ) is non-increasing, and for \(n \geq 0\) let \(I_{n}=\left\{i ; 2^{n} \leq i<2^{n+1}\right\}\) so that card \(I_{n}=2^{n}\) and
\[
\mathcal{E} \subset \mathcal{E}^{\prime}=\left\{t \in \ell^{2} ; \sum_{n \geq 0} \frac{1}{a_{2^{n}}^{2}} \sum_{i \in I_{n}} t_{i}^{2} \leq 1\right\}=\left\{t \in \ell^{2} ; \sum_{n \geq 0} \frac{2^{n}}{c_{n}} \sum_{i \in I_{n}} t_{i}^{2} \leq 1\right\}
\]
where \(c_{n}=2^{n} a_{2^{n}}\). Furthermore (as in the previous section) \(\sum_{n \geq 0} c_{n}=\) \(\sum_{n \geq 0} 2^{n} a_{2^{n}}^{2} \leq 3 \sum_{i \geq 1} a_{i}^{2}\). For such an ellipsoid \(\mathcal{E}^{\prime}\) we will construct sets \(U_{n} \subset \ell^{2}\) with card \(U_{n} \leq N_{n+n_{0}}\) (where \(n_{0}\) is a universal constant) such that
\[
\forall t \in \mathcal{E}^{\prime}, \sum_{n \geq 0} 2^{n / 2} d\left(t, U_{n}\right) \leq L\left(\sum_{n \geq 0} c_{n}\right)^{1 / 2}
\]

Let us now deduce from this result how to perform the chaining on the ellipsoid \(\mathcal{E}\) of (2.154). As we have just seen, such an ellipsoid is contained in an ellipsoid \(\mathcal{E}^{\prime}\) of the type (2.167) for which \(\sum_{n \geq 0} c_{n} \leq L \sum_{i \geq 1} a_{i}^{2}\). Consider the sets \(U_{n} \subset \ell^{2}\) as in (2.168). Consider a map \(\varphi: \ell^{2} \rightarrow \mathcal{E}\) such that \(d(x, \varphi(x)) \leq\) \(2 d(x, \mathcal{E})\) and observe that for \(t \in \mathcal{E}\) and \(x \in \ell^{2}\) we have \(d(x, \varphi(x)) \leq 2 d(x, t)\) so that \(d(t, \varphi(x)) \leq d(t, x)+d(x, \varphi(x)) \leq 3 d(t, x)\). Consequently, \(d\left(t, \varphi\left(U_{n}\right)\right) \leq\) \(3 d\left(t, U_{n}\right)\). The sets \(\varphi\left(U_{n}\right) \subset \mathcal{E}\) satisfy card \(\varphi\left(U_{n}\right) \leq \operatorname{card} U_{n} \leq N_{n+n_{0}}\). We define \(T_{n}=\{0\}\) for \(n \leq n_{0}\) and \(T_{n}=U_{n-n_{0}}\) for \(n>n_{0}\). Thus card \(T_{n} \leq N_{n}\) and (2.168) implies
\[
\forall t \in \mathcal{E}, \sum_{n \geq 0} 2^{n / 2} d\left(t, T_{n}\right) \leq L\left(\sum_{i} a_{i}^{2}\right)^{1 / 2}
\]

We now prepare for the construction of the sets \(U_{n}\). There is no loss of generality to assume that \(\sum_{n \geq 0} c_{n}=1\).
Lemma 2.14.1. Given \(t \in \mathcal{E}^{\prime}\) we can find a sequence \((p(n, t))_{n \geq 0}\) of integers with the following properties.
\[
\sum_{i \in I_{n}} t_{i}^{2} \leq 2^{-p(n, t)}
\]

\footnotetext{
\({ }^{29}\) There are obvious similarities between this section and Section 2.6. It is a good challenge to figure out by yourself how to do the chaining on ellipsoids after having studied Section 2.6.
\({ }^{30}\) I am grateful to Dali Liu for having suggested to include this section
}
\[
\begin{gathered}
\sum_{n \geq 0} 2^{n / 2-p(n, t) / 2} \leq L, \\
\forall n \geq 0, p(n+1, t) \leq p(n, t)+2 .
\end{gathered}
\]

Proof. Define \(q(n, t)\) as the smallest integer \(q \leq 2 n\) such that \(\sum_{i \in I_{n}} t_{i}^{2} \leq\) \(2^{q}\). Let \(A=\{n \geq 0 ; q(n, t)<2 n\}\), so that for \(n \in A\) we have \(2^{-q(n, t)} \leq\) \(2 \sum_{i \in I_{n}} t_{i}^{2}\). Thus, since \(t \in \mathcal{E}^{\prime}\),
\[
\sum_{n \in A} \frac{2^{n-q(n, t)}}{c_{n}} \leq 2 \sum_{n \geq 0} \frac{2^{n}}{c_{n}} \sum_{i \in I_{n}} t_{i}^{2} \leq 2 .
\]

Since \(\sum_{n} c_{n}=1\) then \(\sum_{n \in A} 2^{n / 2-q(n, t) / 2} \leq L\) by the Cauchy-Schwarz inequality. Since \(2^{n / 2-q(n, t) / 2}=2^{-n / 2}\) for \(n \notin A\) we have \(\sum_{n \geq 0} 2^{n / 2-q(n, t) / 2} \leq\) \(L\). We define now \(p(n, t)=\min \{q(k, t)+2(n-k) ; 0 \leq k \leq \bar{n}\}\) so that (2.171) holds. Also, since \(2^{-p(n, t) / 2} \leq \sum_{k \leq n} 2^{-q(k, t) / 2-(n-k)}\) we obtain
\[
\begin{aligned}
\sum_{n \geq 0} 2^{n / 2-p(n, t) / 2} & \leq \sum_{n \geq 0} \sum_{k \leq n} 2^{k / 2-q(k, t) / 2} 2^{-(n-k) / 2} \\
& =\sum_{k \geq 0} 2^{k / 2-q(k, t) / 2} \sum_{n \geq k} 2^{-(n-k) / 2} \leq L .
\end{aligned}
\]

For each \(n \geq 1\) and \(p \geq 0\) consider the set \(B(n, p) \subset \ell^{2}\) which consists of the \(t=\left(t_{i}\right)_{i \geq 1}\) such that \(t_{i}=0\) if \(i \geq 2^{n}\) and \(\|t\|_{2} \leq 2^{-p / 2+2}\). This is a ball of dimension \(2^{n}-1\) and radius \(2^{-p / 2+2}\). Using (2.47) for \(\epsilon=1 / 4\) there is a set \(V_{n, p} \subset B(n, p)\) with card \(V_{n, p} \leq L^{2^{n}}\) such that every point of \(B(n, p)\) is within distance \(\leq 2^{-p / 2}\) of \(V_{n, p}\). We consider the set \(V_{n}=\cup_{0 \leq p \leq 2 n} V_{n, p}\) so that card \(V_{n, p} \leq L^{2^{n}}\). We set \(U_{0}=\{0\}\) and we consider the sets \(U_{n}\) consisting of the elements \(x_{0}+\ldots+x_{n}\) where \(x_{k} \in V_{k}\)

For \(t \in \ell^{2}\) and \(n \geq 0\) we define \(t^{(n)} \in \ell^{2}\) by \(t_{i}^{(n)}=t_{i}\) if \(i<2^{n}\) and \(t_{i}^{(n)}=0\) if \(i \geq 2^{n}\). Note that \(t^{(n)}=t\) for \(t \in U_{n}\).

Lemma 2.14.2. For \(t \in \mathcal{E}^{\prime}\) consider the sequence ( \(p(n, t)\) ) of Lemma 2.14.1. Then for each \(n\) we can find \(u(n) \in U_{n}\) such that \(d\left(u(n), t^{(n)}\right) \leq 2^{-p(n, t) / 2}\).

Proof. The proof is by induction over \(n\). For \(n=0\) it suffices to take \(u(0)=0\) since \(t^{(0)}=0\). For the induction step from \(n\) to \(n+1\) we have \(t^{(n)}=u(n)+v(n)\) where \(u(n) \in U_{n}\) and \(\|v(n)\|_{2} \leq 2^{-p(n, t) / 2}\), so that \(t^{(n+1)}=u(n)+v^{\prime}(n)\) where \(v^{\prime}(n)=v(n)+t^{(n+1)}-t^{(n)}\). By (2.169) \(\left\|t^{(n+1)}-t^{(n)}\right\|_{2}=\left(\sum_{i \in I_{n}} t_{i}^{2}\right)^{1 / 2} \leq\) \(2^{-p(n, t) / 2}\). Thus \(\left\|v^{\prime}(n)\right\|_{2} \leq 2^{-p(n, t) / 2+1} \leq 2^{-p(n+1, t) / 2+2}\) where we have used (2.171) in the second inequality. Since \(v(n)_{i}=0\) for \(i \geq 2^{n}\) we have \(v^{\prime}(n)_{i}=0\) for \(i \geq 2^{n+1}\) so that \(v^{\prime}(n) \in B(n+1, p(n+1, t))\). Thus there is an element \(w \in V_{n+1, p(n+1, t)} \subset V_{n+1}\) for which \(\left\|v^{\prime}(n)-w\right\|_{2} \leq 2^{-p(n+1, t) / 2}\). Setting \(u(n+1):=u(n)+w \in U_{n+1}\) we then have \(t^{(n+1)}-u(n+1)=v^{\prime}(n)-w\).

Corollary 2.14.3. For \(t \in \mathcal{E}^{\prime}\) we have \(\sum_{n \geq 0} 2^{n / 2} d\left(t, U_{n}\right) \leq L\).
Proof. Recalling (2.169) we have
\[
\left\|t-t^{(n)}\right\|_{2}^{2}=\sum_{k>n} \sum_{i \in I_{k}} t_{i}^{2} \leq \sum_{k>n} 2^{-p(k, t)}
\]
so that \(\left\|t-t^{(n)}\right\|_{2} \leq \sum_{k>n} 2^{-p(k, t) / 2}\). Then
\[
\begin{aligned}
\sum_{n \geq 0} 2^{n / 2}\left\|t-t^{(n)}\right\|_{2} \leq \sum_{n \geq 0} \sum_{k>n} 2^{-p(k, t) / 2}=\sum_{k \geq 1} & 2^{-p(k, t) / 2} \sum_{0 \leq n<k} 2^{n / 2} \\
& \leq L \sum_{k \geq 1} 2^{k / 2-p(k, t) / 2} \leq L
\end{aligned}
\]
using (2.170) in the last inequality. Since \(d\left(t, U_{n}\right) \leq d\left(t^{(n)}, U_{n}\right)+\left\|t-t^{(n)}\right\|_{2}\) the result follows, using also Lemma 2.14.2.

\subsection*{2.15 Continuity of Gaussian Processes}

By far the most important result concerning continuity of Gaussian processes is Dudley's bound (1.19). However since the finiteness of the right-hand side of (1.19) is not necessary for the Gaussian process to be continuous, there are situations where this bound is not appropriate. \({ }^{31}\) In the present section we show that a suitable form of the generic chaining allows us to capture the exact modulus of continuity of a Gaussian process with respect to its canonical distance in full generality. Not surprisingly, the modulus of continuity is closely related to the rate at which the series \(\sum_{n} 2^{n / 2} \Delta\left(A_{n}(t)\right)\) converges uniformly on \(T\) for a suitable admissible sequence ( \(\mathcal{A}_{n}\) ). Our first result shows how to obtain a modulus of continuity using the generic chaining.
Lemma 2.15.1. Consider a metric space \((T, d)\) and a process \(\left(X_{t}\right)_{t \in T}\) which satisfies the increment condition (2.4):
\[
\forall u>0, \mathrm{P}\left(\left|X_{s}-X_{t}\right| \geq u\right) \leq 2 \exp \left(-\frac{u^{2}}{2 d(s, t)^{2}}\right)
\]

Assume that there exists a sequence ( \(T_{n}\) ) of subsets of \(T\) with \(\operatorname{card} T_{n} \leq N_{n}\) such that for a certain integer \(m\), and a certain number \(B\) one has
\[
\sup _{t \in T} \sum_{n \geq m} 2^{n / 2} d\left(t, T_{n}\right) \leq B
\]
\({ }^{31}\) In practice however, as of today, the Gaussian processes for which continuity is important can be handled through Dudley's bound, while for those which cannot be handled through this bound (such as in Chapter 4) it is boundedness which matters. For this reason, the considerations of the present section are of purely theoretical interest and may be skipped at first reading.

Consider \(\delta>0\). Then, for any \(u \geq 1\), with probability \(\geq 1-\exp \left(-u^{2} 2^{m}\right)\) we have
\[
\forall s, t \in T, d(s, t) \leq \delta \Rightarrow\left|X_{s}-X_{t}\right| \leq L u\left(2^{m / 2} \delta+B\right)
\]

Proof. We assume \(T\) finite for simplicity. For \(n \geq m\) and \(t \in T\) denote by \(\pi_{n}(t)\) an element of \(T_{n}\) such that \(d\left(t, \pi_{n}(t)\right)=d\left(t, T_{n}\right)\). Consider the event \(\Omega_{u}\) defined by \({ }^{32}\)
\[
\forall n \geq m+1, \forall t \in T,\left|X_{\pi_{n-1}(t)}-X_{\pi_{n}(t)}\right| \leq L u 2^{n / 2} d\left(\pi_{n-1}(t), \pi_{n}(t)\right)
\]
and
\[
\forall s^{\prime}, t^{\prime} \in T_{m},\left|X_{s^{\prime}}-X_{t^{\prime}}\right| \leq L u 2^{m / 2} d\left(s^{\prime}, t^{\prime}\right)
\]

Then as in Section 2.4 we have \(\mathrm{P}\left(\Omega_{u}\right) \geq 1-\exp \left(-u^{2} 2^{m}\right)\). Now, when \(\Omega_{u}\) occurs, using chaining as usual and (2.174) we get
\[
\forall t \in T,\left|X_{t}-X_{\pi_{m}(t)}\right| \leq L u B
\]

Moreover, using (2.172) in the inequality, \(d\left(t, \pi_{m}(t)\right)=d\left(t, T_{m}\right) \leq B 2^{-m / 2}\), so that, using (2.175),
\[
\begin{aligned}
d(s, t) \leq \delta & \Rightarrow d\left(\pi_{m}(s), \pi_{m}(t)\right) \leq \delta+2 B 2^{-m / 2} \\
& \Rightarrow\left|X_{\pi_{m}(s)}-X_{\pi_{m}(t)}\right| \leq L u\left(\delta 2^{m / 2}+B\right)
\end{aligned}
\]

Combining with (2.175) proves that \(\left|X_{s}-X_{t}\right| \leq L u\left(\delta 2^{m / 2}+B\right)\) and completes the proof.

Exercise 2.15.2. Deduce Dudley's bound (1.19) from Lemma 2.15.1.
We now turn to our main result, which exactly describes the modulus of continuity of a Gaussian process in term of certain admissible sequences. It implies in particular the remarkable fact (discovered by X . Fernique) that for Gaussian processes the "local modulus of continuity" (as in (2.177)) is also "global".

Theorem 2.15.3. There exists a constant \(L^{*}\) with the following property. Consider a Gaussian process \(\left(X_{t}\right)_{t \in T}\), with canonical associated distance \(d\) given by (0.1). Assume that \(S=\mathrm{Esup}_{t \in T} X_{t}<\infty\). For \(k \geq 1\) consider \(\delta_{k}>0\) and assume that
\[
\forall t \in T ; \mathrm{E} \sup _{\left\{s \in T ; d(s, t) \leq \delta_{k}\right\}}\left|X_{s}-X_{t}\right| \leq 2^{-k} S
\]

Let \(n_{0}=0\) and for \(k \geq 1\) consider an integer \(n_{k}\) for which

\footnotetext{
\({ }^{32}\) We are again following the general method outlined at the end of Section 2.4.
}
\[
L^{*} S 2^{-n_{k} / 2-k} \leq \delta_{k}
\]

Then we can find an admissible sequence ( \(\mathcal{A}_{n}\) ) of partitions of \(T\) such that
\[
\forall k \geq 0 ; \sup _{t \in T} \sum_{n \geq n_{k}} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq L S 2^{-k}
\]

Conversely, given integers \(n_{k}\) and an admissible sequence ( \(\mathcal{A}_{n}\) ) as in (2.179), and defining now \(\delta_{k}^{*}=S 2^{-n_{k} / 2-k}\), with probability \(\geq 1-\exp \left(-u^{2} 2^{n_{k}}\right)\) we have
\[
\sup _{\left\{s, t \in T ; d(s, t) \leq \delta_{k}^{*}\right\}}\left|X_{s}-X_{t}\right| \leq L u 2^{-k} S
\]

The abstract formulation here might make it hard at first to feel the power of the statement. The numbers \(\delta_{k}\) control the (local) modulus of continuity of the process. The numbers \(n_{k}\) control the uniform convergence (over \(t\) ) of the series \(\sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right)\). They relate to each other by the relation \(\delta_{k} \sim S 2^{-n_{k} / 2-k}\). The second part of the theorem asserts that in turn the numbers \(n_{k}\) control the uniform modulus of continuity (2.180).

Proof. According to the Majorizing Measure Theorem and specifically (2.114), there exists a constant \(L^{*}\) such that for each subset \(U\) of \(T\) there exists an admissible sequence ( \(\mathcal{A}_{n}\) ) of partitions of \(U\) such that
\[
\forall t \in U, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq \frac{L^{*}}{2} \mathrm{E} \sup _{s \in U} X_{s}
\]

Assuming (2.177), by induction over \(k\) we construct an admissible sequence \(\left(\mathcal{A}_{n}\right)_{n \leq n_{k}}\) such that
\[
1 \leq p \leq k \Rightarrow \sup _{t \in T} \sum_{n_{p-1}<n \leq n_{p}} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq L^{*} S 2^{-p}
\]

For \(k=1\) the existence of the sequence \(\left(\mathcal{A}_{n}\right)_{n<n_{1}}\) follows from the Majorizing Measure Theorem through (2.181) as explained, so we turn to the induction step from \(k\) to \(k+1\). Using (2.182) for \(p=k\) we deduce that for each \(t \in T\), \(2^{n_{k} / 2} \Delta\left(A_{n_{k}}(t)\right) \leq L^{*} S 2^{-k}\), so that \(\Delta\left(A_{n_{k}}(t)\right) \leq L^{*} S 2^{-n_{k} / 2-k} \leq \delta_{k}\) using (2.178) in the last inequality. Consequently, for any element \(C\) of \(\mathcal{A}_{n_{k}}\) we have \(\Delta(C) \leq \delta_{k}\), so that considering any point \(t\) of \(C\) we have, using (2.177) in the last inequality
\[
\mathrm{E} \sup _{s \in C} X_{s}=\mathrm{E} \sup _{s \in C}\left(X_{s}-X_{t}\right) \leq \mathrm{E} \sup _{\left\{s \in T ; d(s, t) \leq \delta_{k}\right\}}\left|X_{s}-X_{t}\right| \leq S 2^{-k}
\]

Using the Majorizing Measure Theorem we construct for each \(C \in \mathcal{A}_{n_{k}}\) an admissible sequence \(\left(\mathcal{A}_{C, n}\right)_{n \geq 0}\) of partitions of \(C\) for which
\[
\forall t \in C, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{C, n}(t)\right) \leq L^{*} S 2^{-k-1}
\]

For \(n_{k}<n \leq n_{k+1}\) we simply define \(\mathcal{A}_{n}\) as the collection of all sets in one of the partitions \(A_{C, n-1}\) where \(C \in \mathcal{A}_{n_{k}}\), so that \(\operatorname{card} \mathcal{A}_{n} \leq N_{n-1} \operatorname{card} \mathcal{A}_{n_{k}} \leq\) \(N_{n-1}^{2} \leq N_{n}\). Since for \(t \in C\) we have \(A_{n}(t)=A_{C, n-1}(t)\) it follows from (2.183) that for any \(C \in \mathcal{A}_{n_{k}}\) we have
\[
\sup _{t \in C} \sum_{n_{k}<n \leq n_{k+1}} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq \sup _{t \in C} \sum_{n>n_{k}} 2^{n / 2} \Delta\left(A_{C, n-1}(t)\right) \leq L^{*} S 2^{-k} .
\]

This completes the induction and the construction of the sequence ( \(\mathcal{A}_{n}\) ) since (2.182) implies (2.179).

It remains to prove the "conversely" part. For this for each \(n \geq 0\) we simply consider a subset \(T_{n}\) of \(T\) such that
\[
\forall A \in \mathcal{A}_{n}, \operatorname{card}\left(T_{n} \cap A\right)=1
\]

For each \(k\) we then use Lemma 2.15.1 for \(m=n_{k}\) and \(B=S 2^{-k}\).

\section*{Key ideas to remember. \({ }^{33}\)}
- The generic chaining efficiently organizes the standard chaining argument for processes whose increments have Gaussian-like tails governed by a distance \(d\) as in (2.4).
- The generic chaining applied to such processes motivates the introduction of our main measure \(\gamma_{2}(T, d)\) of the size of a metric space \((T, d)\). This measure involves the existence of suitable sequences of partitions.
- The fundamental problem then becomes how to construct such sequences of partitions in a metric space.
- There is a machine (called a partitioning scheme) to construct such sequences of partitions. The input to the machine is a functional, a function of the subsets of our basic metric space, which in a sense is a measure of their size. The existence of such functionals with specific growth properties is intrinsically linked to the existence of such sequences of partitions.
- The Majorizing Measure Theorem is the statement that for a Gaussian process with index set \(T\) and canonical distance \(d\) the quantity \(\operatorname{Esup}_{t \in T} X_{t}\) is exactly of order \(\gamma_{2}(T, d)\). The proof relies on a partitioning scheme, used for the functional \(F(A)=\mathrm{Esup}_{t \in A} X_{t}\). Sudakov minoration and concentration of measure are the main tools to prove that this functional satisfies the required growth condition.
- Gaussian processes can be seen as subsets of a standard Hilbert space, but the geometric understanding that would relate the size of a set with the size of its convex hull is still lacking.
- The traditional way to organize chaining uses entropy numbers. Even for sets as basic as ellipsoids in Hilbert space, entropy numbers provide only a suboptimal description of their size.

\footnotetext{
\({ }^{33}\) The function of this brief summary is not to explain the material again, but is a way for the reader to check that she did understand the main ideas. If any of the points made below is not clear to the reader, she may not be ready to proceed and may want to review the corresponding material.
}

\subsection*{2.16 Notes and Comments}

I have heard people saying that the problem of characterizing continuity and boundedness of Gaussian processes goes back (at least implicitly) to Kolmogorov. The understanding of Gaussian processes was long delayed by the fact that in the most immediate examples the index set is a subset of \(\mathbb{R}\) or \(\mathbb{R}^{n}\) and that the temptation to use the special structure of this index set is nearly irresistible. Probably the single most important conceptual progress about Gaussian processes was the realization, in the late sixties, that the boundedness of a (centered) Gaussian process is determined by the structure of the metric space \((T, d)\), where \(d\) is the usual distance \(d(s, t)=\left(\mathrm{E}\left(X_{s}-X_{t}\right)^{2}\right)^{1 / 2}\). It is difficult now to realize what a tremendous jump in understanding this was, since this seems so obvious a posteriori.

In 1967, R. Dudley obtained the inequality (2.38). (As he pointed out, R. Dudley did not state (2.38) though he performed all the essential steps, and (2.38) totally deserves to be called Dudley's bound.) A few years later, X. Fernique proved that in the "stationary case" Dudley's inequality can be reversed [43], i.e. he proved in that case the lower bound of Theorem 2.10.1. This historically important result was central to the work of Marcus and Pisier [85], [86] who built on it to solve all the classical problems on random Fourier series. Some of their results will be presented in Chapter 7. Interestingly, now that the right approach has been found, the proof of Fernique's result is not really easier than that of Theorem 2.10.1.

Another major contribution of Fernique (building on earlier ideas of C. Preston) was an improvement of Dudley's bound based on a new tool called majorizing measures (which we will study in Subsection 3.1.3). Fernique conjectured that his bound was essentially optimal. Gilles Pisier suggested in 1983 that I should work on this conjecture. In my first attempt I proved fast that Fernique's conjecture held in the case where the metric space ( \(T, d\) ) is ultrametric. I learned that Fernique had already done this, so I was discouraged for a while. In the second attempt, I tried to decide whether a majorizing measure existed on ellipsoids. I had the hope that some simple density with respect to the volume measure would work. It was difficult to form any intuition, and I struggled in the dark for months. At some point I tried a combination of suitable point masses, and easily found a direct construction of the majorizing measure on ellipsoids. This made it believable that Fernique's conjecture was true, but I still tried to disprove it. Then I realized that I did not understand why a direct approach using a partitioning scheme should fail, while this understanding should be useful to construct a counterexample. Once I tried this direct approach, it was a matter of three days to prove Fernique's conjecture. Gilles Pisier made two comments about this discovery. The first one was "you are lucky", by which he meant that I was lucky that Fernique's conjecture was true, since a counter example would have been of limited interest. I am grateful to this day for his second comment: "I wish I had proved this myself, but I am very glad you did it."

Fernique's concept of majorizing measures is difficult to grasp, and was dismissed by the main body of probabilists as a mere curiosity. (I myself found it very difficult to understand.) This could be the main reason why Fernique's path-breaking work did not receive the recognition it should have. I have tried to repair this and to express my personal admiration by dedicating this book to his memory and by paying homage to his work at numerous places in this book.

In 2000, while discussing one of the open problems of this book with Keith Ball (be he blessed for his interest in it!) I discovered that one could replace majorizing measures by the totally natural variation on the usual chaining arguments that was presented here. That this was not discovered much earlier is a striking illustration of the inefficiency of my brain. For two decades it looked like majorizing measures would not be of any use anymore, but they now play again a major role again for reasons to be explained in Chapter 5.

In [150] the author presented a particularly simple proof of Theorem 2.10.1 (expressed in terms of majorizing measures since the generic chaining had not been invented yet). It is based a on partition scheme related to the one we use here. The precise relationship is discussed on page 72 of [171].

It is on purpose that I did not mention Slepian's lemma, which is the statement that (2.127) holds for \(L=1\). This lemma is very specific to Gaussian processes, and focusing on it seems a good way to guarantee that one will never move beyond these. One notable progress I made was to discover (ages ago) the scheme of proof of Proposition 2.10.8 that dispenses with Slepian's lemma, and that we shall use in many situations. Comparison results such as Slepian's lemma are not at the root of results such as the Majorizing Measure Theorem, but rather are (at least qualitatively) a consequence of them as in Corollary 2.10.12. This being said, Slepian's lemma is historically very important as it crystallizes the link between \(\mathrm{E}_{\sup _{t \in T}} X_{t}\) and the structure of the metric space \((T, d)\).

\section*{3. Trees and Other Measures of Size}

In this chapter we systematically investigate different ways to measure the size of a metric space. One of them, Fernique's functional of Section 3.3 will play a major role in the sequel, as it is the form which lends itself to vast generalizations. The concept of a tree presented in Section 3.1 is historically important: the author discovered many of the results he presents while thinking in terms of trees. We know now how to present these results and their proofs without ever mentioning trees, arguably in a more elegant fashion, so that trees are not used explicitly elsewhere in this book. However, it might be too early to dismiss this concept, at least as an instrument of discovery.

\subsection*{3.1 Trees}

We shall describe different ways to measure the size of a metric space and show that they are all equivalent to the functional \(\gamma_{2}(T, d) .^{1}\)

In a nutshell, a tree is a certain structure that requires a "lot of space" to be constructed, so that a metric space needs to be large in order to contain large trees. At the simplest level, it already takes some space to construct in a set \(A\) sets \(B_{1}, \ldots, B_{n}\) which are appropriately separated from each other. This is even more so if the sets \(B_{1}, \ldots, B_{n}\) are themselves large (for example because they contain many sets far from each other). Trees are a proper formulation of the iteration of this idea. The basic use of trees is to measure the size of a metric space by the size of the largest tree (of a certain type) which it contains. Different types of trees yield different measures of size.

A tree \(\mathcal{T}\) of a metric space ( \(T, d\) ) is a finite collection of non-empty subsets of \(T\) with the following two properties.

Given \(A, B\) in \(\mathcal{T}\), if \(A \cap B \neq \emptyset\), then either \(A \subset B\) or else \(B \subset A\).
\(\mathcal{T}\) has a largest element.
The important condition here is (3.1), and (3.2) is just for convenience.
If \(A, B \in \mathcal{T}\) and \(B \subset A, B \neq A\), we say that \(B\) is a child of \(A\) if

\footnotetext{
\({ }^{1}\) It is possible to consider more general notions corresponding to other functionals considered in the book, but for simplicity we consider only the case of \(\gamma_{2}\).
}
\[
C \in \mathcal{T}, B \subset C \subset A \Rightarrow C=B \text { or } C=A
\]

We denote by \(c(A)\) the number of children of \(A\). Since our trees are finite, some of their sets will have no children. It is convenient to "shrink these sets to a single point", so we will consider only trees with the following property:
\[
\text { If } A \in \mathcal{T} \text { and } c(A)=0 \text {, then } A \text { contains exactly one point }
\]

A fundamental property of trees is as follows. Consider trees \(\mathcal{T}_{1}, \ldots, \mathcal{T}_{m}\) and for \(1 \leq \ell \leq m\) let \(A_{\ell}\) be the largest element of \(\mathcal{T}_{\ell}\). Assume that the sets \(A_{\ell}\) are disjoint, and consider a set \(A\) with \(\bigcup_{\ell \leq m} A_{\ell} \subset A \subset T\). Then the collection of subsets of \(T\) consisting of \(A\) and of \(\bigcup_{\ell \leq m} \mathcal{T}_{\ell}\) is a tree. The proof is straightforward. This fact allows one to construct iteratively more and more complicated (and larger) trees.

An important structure in a tree is a branch. A sequence \(A_{0}, A_{1}, \ldots, A_{k}\) is a branch if \(A_{\ell+1}\) is a child of \(A_{\ell}\), and if moreover \(A_{0}\) is the largest element of \(\mathcal{T}\) while \(A_{k}\) has no child. Then by (3.4) the set \(A_{k}\) is reduced to a single point \(t\), and \(A_{0}, \ldots, A_{k}\) are exactly those elements of \(\mathcal{T}\) which contain \(t\). So in order to describe the branches of \(\mathcal{T}\) it is convenient to introduce the set
\[
S_{\mathcal{T}}=\{t \in T ;\{t\} \in \mathcal{T}\},
\]
which we call the support of \(\mathcal{T}\). If a set \(A\) in a tree has no child, one may call it a leaf. Thus a leaf of a tree is reduced to one single point, and the support of a tree is the union of its leaves. By considering all the collections \(\{A \in \mathcal{T} ; t \in A\}\) as \(t\) varies in \(S_{\mathcal{T}}\) we obtain all the branches of \(\mathcal{T}\).

\subsection*{3.1.1 Separated Trees}

We now quantify our desired property that the children of a given set should be far from each other in an appropriate sense. A separated tree is a tree \(\mathcal{T}\) such that to each \(A\) in \(\mathcal{T}\) with \(c(A) \geq 1\) is associated an integer \(s(A) \in \mathbb{Z}\) with the following properties. First,
\[
\text { If } B_{1} \text { and } B_{2} \text { are distinct children of } A \text {, then } d\left(B_{1}, B_{2}\right) \geq 4^{-s(A)} \text {. }
\]

Here \(d\left(B_{1}, B_{2}\right)=\inf \left\{d\left(x_{1}, x_{2}\right) ; x_{1} \in B_{1}, x_{2} \in B_{2}\right\}\). We observe that in (3.6) we make no restriction on the diameter of the children of \(A\). (Such restrictions will however occur in the other notion of tree that we consider later.) Second, to rule out pathologies, we will also make the following purely technical assumption:
\[
\text { If } B \text { is a child of } A \text {, then } s(B)>s(A) \text {. }
\]

To measure the size of a separated tree \(T\) we introduce its depth, i.e.
\[
\rho(\mathcal{T}):=\inf _{t \in S_{\mathcal{T}}} \sum_{t \in A \in \mathcal{T}} 4^{-s(A)} \sqrt{\log c(A)} .
\]

Here and below we make the convention that the summation does not include the term \(A=\{t\}\) (for which \(c(A)=0\) ). The quantity (3.8) takes into account both the separation between the children of \(A\) (through the term \(4^{-s(A)}\) ) and their number (through the term \(\sqrt{\log c(A)}\) ). This will be a common feature of all our notions of sizes of trees.

We observe that in (3.8) we have the infimum over \(t \in S_{\mathcal{T}}\). In words:
A tree is large if it is large along every branch.
We can then measure the size of \(T\) by
\[
\sup \{\rho(\mathcal{T}) ; \mathcal{T} \text { separated tree } \subset T\}
\]
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-096.jpg?height=741&width=687&top_left_y=941&top_left_x=578)

Fig. 3.1. A separated tree. The children of \(A_{0}\) are \(A_{1}\) and \(B\). The children of \(A_{1}\) are \(A_{2}\) and \(C\). \(A_{0}, A_{1}, A_{2}, A_{3}\) form a branch, of which \(A_{3}\) is a leaf.

\subsection*{3.1.2 Organized trees}

The notion of separated tree we just considered is but one of many possible notions of trees and it does not seem fundamental. Rather, the quantity (3.9) is used as a convenient intermediate technical step to prove the equivalence of several more important quantities. Let us now consider another notion
of trees, which is more restrictive (and apparently much more important). An organized tree is a tree \(\mathcal{T}\) such that to each \(A \in \mathcal{T}\) with \(c(A) \geq 1\) are associated an integer \(j=j(A) \in \mathbb{Z}\), and points \(t_{1}, \ldots, t_{c(\Lambda)}\) with the properties that
\[
1 \leq \ell<\ell^{\prime} \leq c(A) \Rightarrow 4^{-j-1} \leq d\left(t_{\ell}, t_{\ell^{\prime}}\right) \leq 4^{-j+2}
\]
and that each ball \(B\left(t_{\ell}, 4^{-j-2}\right)\) contains exactly one child of \(A\). In some sense \(4^{-j(A)}\) tells you at which scale the children of \(A\) live. Please note that it may happen that \(4^{-j(A)}\) is much smaller than \(\Delta(A)\).

If \(B_{1}\) and \(B_{2}\) are distinct children of \(A\) in an organized tree, then
\[
d\left(B_{1}, B_{2}\right) \geq 4^{-j(A)-2},
\]
so that an organized tree is also a separated tree, with \(s(A)=j(A)+2\), but the notion of organized tree is more restrictive. (For example we have no control over the diameter of the children of \(A\) in a separated tree.)
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-097.jpg?height=419&width=1064&top_left_y=1151&top_left_x=406)

Fig. 3.2. An organized tree. Here \(j\left(A_{2}\right)>j\left(A_{1}\right)\).

We define the depth \(\tau(\mathcal{T})\) of an organized tree by
\[
\tau(\mathcal{T}):=\inf _{t \in S_{\mathcal{T}}} \sum_{t \in A \in \mathcal{T}} 4^{-j(A)} \sqrt{\log c(A)} .
\]

Another way to measure the size of \(T\) is then
\[
\sup \{\tau(\mathcal{T}) ; \mathcal{T} \text { organized tree } \subset T\} .
\]

If we simply view an organized tree \(\mathcal{T}\) as a separated tree using (3.11), then \(\rho(\mathcal{T})=\tau(\mathcal{T}) / 16\) (where \(\rho(\mathcal{T})\) is the depth of \(\mathcal{T}\) as a separated tree). Thus we have shown the following.

Proposition 3.1.1. We have
\[
\sup \{\tau(\mathcal{T}) ; \mathcal{T} \text { organized tree }\} \leq 16 \sup \{\rho(\mathcal{T}) ; \mathcal{T} \text { separated tree }\}
\]

The next result provides the fundamental connection between trees and the functional \(\gamma_{2}\).

Proposition 3.1.2. We have
\[
\gamma_{2}(T, d) \leq L \sup \{\tau(\mathcal{T}) ; \mathcal{T} \text { organized tree }\}
\]

Proof. We consider the functional
\[
F(A)=\sup \{\tau(\mathcal{T}) ; \mathcal{T} \subset A, \mathcal{T} \text { organized tree }\}
\]
where we write \(\mathcal{T} \subset A\) as a shorthand for " \(\forall B \in \mathcal{T}, B \subset A\) ".
Next we prove that this functional satisfies the growth condition (2.77) for \(r=16\) whenever \(a\) is of the type \(16^{-j}\), for \(c^{*}=1 / L\). For this consider \(n \geq 1\) and \(m=N_{n}\). Consider \(j \in \mathbb{Z}\) and \(t_{1}, \ldots, t_{m} \in T\) with
\[
1 \leq \ell<\ell^{\prime} \leq m \Rightarrow 16^{-j} \leq d\left(t_{\ell}, t_{\ell^{\prime}}\right) \leq 2 \cdot 16^{-j+1}
\]

Consider sets \(H_{\ell} \subset B\left(t_{\ell}, 2 \cdot 16^{-j-1}\right)\) and \(\alpha<\min _{\ell \leq m} F\left(H_{\ell}\right)\). Consider, for \(\ell \leq m\) an organized tree \(\mathcal{T}_{\ell} \subset H_{\ell}\) with \(\tau\left(\mathcal{T}_{\ell}\right)>\alpha\) and denote by \(A_{\ell}\) its largest element. Next we claim that the tree \(\mathcal{T}\) consisting of \(C=\bigcup_{\ell \leq m} H_{\ell}\) (its largest element) and the union of the trees \(\mathcal{T}_{\ell}, \ell \leq m\), is organized, with \(j(C)=2 j-1\), and \(A_{1}, \ldots, A_{m}\) as children of \(C\) (so that \(c(C)=m\) ). To see this we observe that since \(4^{-j(C)-1}=16^{-j}\) we have \(4^{-j(C)-1} \leq\) \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \leq 2 \cdot 16^{-j+1} \leq 4^{-j(C)+2}\), so that (3.10) holds for \(C\). Furthermore \(A_{\ell} \subset H_{\ell} \subset B\left(t_{\ell}, 2 \cdot 16^{-j-1}\right) \subset B\left(t_{\ell}, 4^{-j(C)-2}\right)\), so that this ball contains exactly one child of \(C\). The other conditions follow from the fact that the trees \(\mathcal{T}_{\ell}\) are themselves organized. Moreover \(S_{\mathcal{T}}=\bigcup_{\ell \leq m} S_{\mathcal{T}_{\ell}}\).

Consider \(t \in S_{\mathcal{T}}\), and let \(\ell\) with \(t \in S_{\mathcal{T}_{\ell}}\). Then \(\bar{t} \in C \in \mathcal{T}\), and also \(t \in A \in \mathcal{T}\) whenever \(t \in A \in \mathcal{T}_{\ell}\). Thus, using also in the second line that \(j(C)=2 j-1\) and that \(c(C)=m\), we obtain
\[
\begin{aligned}
\sum_{t \in A \in \mathcal{T}} 4^{-j(A)} \sqrt{\log c(A)} & \geq 4^{-j(C)} \sqrt{\log c(C)}+\sum_{t \in A \in \mathcal{T}_{\ell}} 4^{-j(A)} \sqrt{\log c(A)} \\
& \geq 4 \cdot 16^{-j} \sqrt{\log m}+\tau\left(\mathcal{T}_{\ell}\right) \geq \frac{1}{L} 16^{-j} 2^{n / 2}+\alpha
\end{aligned}
\]

Since \(\alpha\) is arbitrary, we have proved that
\[
F\left(\bigcup_{\ell \leq m} H_{\ell}\right) \geq \tau(\mathcal{T}) \geq \frac{1}{L} 16^{-j} 2^{n / 2}+\min _{\ell \leq m} F\left(H_{\ell}\right)
\]

This completes the proof of the growth condition (2.77).

If one examines the proof of Theorem 2.9.1, one observes that it requires only the growth condition (2.77) to hold true when \(a\) is of the type \(r^{-j}\), and we have just proved that this is the case (for \(r=16\) ), so that from (2.81) we have proved that \(\gamma_{2}(T, d) \leq L(F(T)+\Delta(T))\). It remains only to prove that \(\Delta(T) \leq L F(T)\). For this we simply note that if \(s, t \in T\), and \(j_{0}\) is the largest integer with \(4^{-j_{0}} \geq d(s, t)\), then the tree \(\mathcal{T}\) consisting of \(T,\{t\},\{s\}\), is organized with \(j(T)=j_{0}\) and \(c(T)=2\), so that \(F(T) \geq \tau(\mathcal{T}) \geq 4^{-j_{0}} \sqrt{\log 2}\) and \(4^{-j_{0}} \leq L F(T)\).

\subsection*{3.1.3 Majorizing Measures}

For a probability measure \(\mu\) on a metric space ( \(T, d\) ), with countable support, \({ }^{2}\) we define for each \(t \in T\) the quantity
\[
I_{\mu}(t):=\int_{0}^{\infty} \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \mathrm{d} \epsilon=\int_{0}^{\Delta(T)} \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \mathrm{d} \epsilon
\]

The second equality follows from the fact that \(\mu(B(t, \epsilon))=1\) when \(B(t, \epsilon)=\) \(T\), so that then the integrand is 0 . It is important to master the mechanism at play in the following elementary exercise.

Exercise 3.1.3. Consider a number \(\Delta>0\) and non-increasing function \(f:\) \([0, \Delta] \rightarrow \mathbb{R}^{+}\). Define \(\epsilon_{0}=\Delta\) and for \(n \geq 1\) define \(\epsilon_{n}=\inf \left\{\epsilon>0 ; f(\epsilon) \leq 2^{n}\right\}\). Prove that
\[
\frac{1}{2} \sum_{n \geq 1} 2^{n} \epsilon_{n} \leq \int_{0}^{\Delta} f(\epsilon) \mathrm{d} \epsilon \leq 2 \sum_{n \geq 0} 2^{n} \epsilon_{n}
\]

Proposition 3.1.4. Given a metric space ( \(T, d\) ) we can find on \(T\) a probability measure \(\mu\), supported by a countable subset of \(T\), and such that
\[
\sup _{t \in T} I_{\mu}(t)=\sup _{t \in T} \int_{0}^{\infty} \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \mathrm{d} \epsilon \leq L \gamma_{2}(T, d)
\]

Any probability measure \({ }^{3} \mu\) on ( \(T, d\) ) is called a majorizing measure. The reason for this somewhat unsatisfactory name is that Xavier Fernique proved that for a Gaussian process and a probability measure \(\mu\) on \(T\) one has
\[
\mathrm{E} \sup _{t \in T} X_{t} \leq L \sup _{t \in T} I_{\mu}(t)
\]

\footnotetext{
\({ }^{2}\) We assume \(\mu\) with countable support because we do not need a more general setting. The advantage of this hypothesis is that there are no measurability problems.
\({ }^{3}\) To avoid technicalities one may assume that \(\mu\) has countable support.
}
so that \(\mu\) can be used to "majorize" the process \(\left(X_{t}\right)_{t \in T} .{ }^{4}\) This was a major advance over Dudley's bound. The (in)famous theory of majorizing measures used the quantity
\[
\inf _{\mu} \sup _{t \in T} I_{\mu}(t)
\]
as a measure of the size of the metric space ( \(T, d\) ), where the infimum is over all choices of the probability measure \(\mu\). This method is technically quite challenging. We are going to prove that the quantity (3.20) is equivalent to \(\gamma_{2}(T, d)\). A related idea which is still very useful is explained in Section 3.3.

Proof. Consider an admissible sequence ( \(\mathcal{A}_{n}\) ) with
\[
\forall t \in T, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq 2 \gamma_{2}(T, d)
\]

Let us now pick a point \(t_{n, A}\) in each set \(A \in \mathcal{A}_{n}\), for each \(n \geq 0\). Since card \(\mathcal{A}_{n} \leq N_{n}\), for each \(n\) there are at most \(N_{n}\) points of the type \(t_{n, A}\). Attributing a mass \(1 /\left(2^{n} N_{n}\right)\) to each of them we obtain a total mass \(\leq 1\). Thus there is a probability measure \(\mu\) on \(T\), supported by a countable set, and satisfying \(\mu\left(\left\{t_{n, A}\right\}\right) \geq 1 /\left(2^{n} N_{n}\right)\) for each \(n \geq 0\) and each \(A \in \mathcal{A}_{n}\). Then,
\[
\forall n \geq 1, \forall A \in \mathcal{A}_{n}, \mu(A) \geq \mu\left(\left\{t_{n, A}\right\}\right) \geq \frac{1}{2^{n} N_{n}} \geq \frac{1}{N_{n}^{2}}
\]
so that given \(t \in T\) and \(n \geq 1\),
\[
\begin{aligned}
\epsilon>\Delta\left(A_{n}(t)\right) & \Rightarrow \mu(B(t, \epsilon)) \geq \frac{1}{N_{n}^{2}} \\
& \Rightarrow \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \leq 2^{n / 2+1}
\end{aligned}
\]

Now, since \(\mu\) is a probability, \(\mu(B(t, \epsilon))=1\) for \(\epsilon>\Delta(T)\), and then \(\log (1 / \mu(B(t, \epsilon)))=0\). Thus
\[
\begin{aligned}
I_{\mu}(t)=\int_{0}^{\infty} \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \mathrm{d} \epsilon & =\sum_{n \geq 0} \int_{\Delta\left(A_{n+1}(t)\right)}^{\Delta\left(A_{n}(t)\right)} \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \mathrm{d} \epsilon \\
& \leq \sum_{n \geq 1} 2^{(n+1) / 2+1} \Delta\left(A_{n}(t)\right) \leq L \gamma_{2}(T, d)
\end{aligned}
\]
using (3.21).

\footnotetext{
\({ }^{4}\) One typically uses the name only when such the right-hand side of (3.19) is usefully small.
}

Proposition 3.1.5. If \(\mu\) is a probability measure on \(T\) (supported by a countable set) and \(\mathcal{T}\) is a separated tree on \(T\), then
\[
\rho(\mathcal{T}) \leq L \sup _{t \in T} I_{\mu}(t)
\]

Combining with (3.14), (3.15) and (3.18), this completes the proof that the four "measures of the size of \(T\) " considered in this section, namely (3.9), (3.13), (3.20) and \(\gamma_{2}(T, d)\) are indeed equivalent.

Proof. The basic observation is as follows. The sets
\[
B\left(C, 4^{-s(A)-1}\right)=\left\{x \in T ; d(x, C)<4^{-s(A)-1}\right\}
\]
are disjoint as \(C\) varies over the children of \(A\) (as follows from (3.6)), so that one of them has measure \(\leq c(A)^{-1}\).

We then proceed in the following manner, constructing recursively an appropriate branch of the tree. This is a typical and fundamental way to proceed when working with trees. We start with the largest element \(A_{0}\) of \(\mathcal{T}\). We then select a child \(A_{1}\) of \(A_{0}\) with \(\mu\left(B\left(A_{1}, 4^{-s\left(A_{0}\right)-1}\right)\right) \leq 1 / c\left(A_{0}\right)\), and a child \(A_{2}\) of \(A_{1}\) with \(\mu\left(B\left(A_{2}, 4^{-s\left(A_{1}\right)-1}\right)\right) \leq 1 / c\left(A_{1}\right)\), etc., and continue this construction as long as we can. It ends only when we reach a set of \(\mathcal{T}\) that has no child, and hence by (3.4) is reduced to a single point \(t\) which we now fix. For any set \(A\) with \(t \in A \in \mathcal{T}\), by construction we have
\[
\mu\left(B\left(t, 4^{-s(A)-1}\right)\right) \leq \frac{1}{c(A)}
\]
so that
\[
4^{-s(A)-2} \sqrt{\log c(A)} \leq \int_{4^{-s(A)-2}}^{4^{-s(A)-1}} \sqrt{\frac{1}{\log \mu(B(t, \epsilon))}} \mathrm{d} \epsilon
\]
because the integrand is \(\geq \sqrt{\log c(A)}\) and the length of the interval of integration is larger than \(4^{-s(A)-2}\). By (3.7) the intervals \(] 4^{-s(A)-2}, 4^{-s(A)-1}[\) are disjoint for different sets \(A\) with \(t \in A \in \mathcal{T}\), so summation of the inequalities (3.22) yields
\[
\frac{1}{16} \rho(\mathcal{T}) \leq \sum_{t \in A \in \mathcal{T}} 4^{-s(A)-2} \sqrt{\log c(A)} \leq \int_{0}^{\infty} \sqrt{\frac{1}{\log \mu(B(t, \epsilon))}} \mathrm{d} \epsilon=I_{\mu}(t)
\]

In the rest of this chapter, we will implicitly use the previous method of "selecting recursively the branch of the tree we follow" to prove lower bounds without mentioning trees.

We end this section by an exercise completing the proof of (2.143).
Exercise 3.1.6. Consider metric spaces \(\left(T_{k}, d_{k}\right)_{k \leq N}\) and probability measures \(\mu_{k}\) on \(T_{k}\). Consider the product probability \(\mu\) on \(T=\prod_{k \leq N} T_{k}\) and the distance (2.142).
(a) Prove that for \(t=\left(t_{k}\right)_{k \leq N}\) we have
\[
I_{\mu}(t) \leq L \sum_{k \leq N} I_{\mu_{k}}\left(t_{k}\right)
\]

Hint: use (2.147).
(b) Complete the proof of (2.143).

\subsection*{3.2 Rolling Up our Sleeves: Trees in Ellipsoids}

It is one thing to have proved abstract results, but quite another thing to visualize the combinatorics in concrete situations. Consider an ellipsoid \(\mathcal{E}\) as in (2.154), so that according to (2.155), \(S:=\sqrt{\sum_{i \leq N} a_{i}^{2}}\) measures its size. Assuming \(S<\infty\) the goal of the present section is to construct explicitly an organized tree \(\mathcal{T}\) whose depth \(\tau(\mathcal{T})\) witnesses the size of the ellipsoid, i.e. \(\tau(\mathcal{T}) \geq S / L\). This elaborate exercise will have us confront a number of technical difficulties.

We first reduce to the case where each \(a_{i}\) is of the type \(2^{-k}\) for some \(k \in \mathbb{Z}\). Let \(\mathbb{N}^{*}=\mathbb{N} \backslash\{0\}\). For \(k \in \mathbb{Z}\) let us set \(I_{k}=\left\{i \in \mathbb{N}^{*} ; 2^{-k} \leq a_{i} \leq 2^{-k-1}\right\}\), so that the sets \(I_{k}\) cover \(\mathbb{N}^{*}\) and \(\sum_{k} 2^{-2 k}\) card \(I_{k} \geq \sum_{i \geq 1} a_{i}^{2} / 4 \geq S^{2} / 4\), while at the same time
\[
\mathcal{E} \supset \mathcal{E}^{\prime}=\left\{t \in \ell^{2} ; \sum_{k} 2^{2 k} \sum_{i \in I_{k}} t_{i}^{2} \leq 1\right\}
\]

Thus we have reduced the problem to considering only ellipsoids of the type \(\mathcal{E}^{\prime}\). Here comes a somewhat unexpected argument. We are going to replace \(\mathcal{E}^{\prime}\) by a set of the type
\[
\mathcal{P}=\left\{t \in \ell^{2} ; \forall k, 2^{2 k} \sum_{i \in I_{k}} t_{i}^{2} \leq \alpha_{k}\right\}
\]
where \(\sum_{k} \alpha_{k}=1\) (a condition which ensures that \(\mathcal{P} \subset \mathcal{E}^{\prime}\) ). At first sight the set \(\mathcal{P}\) looks much smaller than \(\mathcal{E}^{\prime}\), but this is not the case. First, how should we chose \(\alpha_{k}\) to ensure that \(\mathcal{P}\) is as large as possible? Considering independent Gaussian r.v.s \(\left(g_{i}\right)\) we have \(\sup _{t \in \mathcal{P}} \sum_{i \geq 1} t_{i} g_{i}=\sum_{k} \sqrt{\alpha_{k}} 2^{-k} \sqrt{\sum_{i \in I_{k}} g_{i}^{2}}\), so that since \(\mathrm{E} \sqrt{\sum_{i \in I_{k}} g_{i}^{2}}\) is about \(\sqrt{\operatorname{card} I_{k}}\) by (2.155), we obtain
\[
\mathrm{E} \sup _{t \in \mathcal{P}} \sum_{i \geq 1} t_{i} g_{i} \text { is about } \sum_{k} \sqrt{\alpha_{k}} 2^{-k} \sqrt{\operatorname{card} I_{k}}
\]

So to maximize this quantity (and, in a sense, the size of \(\mathcal{P}\) ), it is a good idea to chose \(\alpha_{k}=2^{-2 k} \operatorname{card} I_{k} / S^{\prime 2}\) where \(S^{\prime 2}=\sum_{\ell} 2^{-2 \ell} \operatorname{card} I_{\ell}\). Then \(\mathcal{P}\) takes the form
\[
\mathcal{P}=\left\{t \in \ell^{2} ; \forall k, \sum_{i \in I_{k}} t_{i}^{2} \leq \frac{2^{-4 k}}{S^{\prime 2}} \operatorname{card} I_{k}\right\}
\]

This set is very simple: geometrically, \(\mathcal{P}\) is a product of spheres of dimension card \(I_{k}\) and radius \(r_{k}\), where \(r_{k}\) is defined by \(r_{k}^{2}=2^{-4 k} \operatorname{card} I_{k} / S^{\prime 2}\). It will be very useful to reduce to the case where the radii of these spheres are quite different from each other. This is the purpose of the next lemma.

Lemma 3.2.1. There is a subset \(J\) of \(\mathbb{Z}\) with the following two properties:
\[
\begin{gathered}
\sum_{k \in J} 2^{-2 k} \operatorname{card} I_{k} \geq S^{\prime 2} / L . \\
k, n \in J, k<n \Rightarrow r_{n} \leq 2^{-6} r_{k} .
\end{gathered}
\]

Proof. Since \(S<\infty\) the sequence \(a_{k}=2^{-2 k}\) card \(I_{k}\) is bounded. We apply Lemma 2.9.5 (or more precisely the version of this lemma where the index set is \(\mathbb{Z}\) rather than \(\mathbb{N}\) ) with \(\alpha=2\) to find a finite subset \(I \subset \mathbb{Z}\) such that
\[
k, n \in I, k \neq n \Rightarrow a_{n}<a_{k} 2^{|n-k|}
\]
and
\[
\sum_{k \in I} a_{k} \geq S^{\prime 2} / L
\]

Consider \(k, n \in I\) with \(k<n\). Then \(a_{n}<2^{n-k} a_{k}\), and recalling the value of \(a_{n}\) this means that card \(I_{n} \leq 2^{3(n-k)}\) card \(I_{k}\). Recalling the value of \(r_{k}\) this means that \(r_{n}^{2} \leq r_{k}^{2} / 2\). Let us now enumerate \(I\) as a finite sequence \((n(\ell))_{\ell \leq N}\) in increasing order, so that \(r_{n(\ell+1)} \leq r_{n(\ell)} / \sqrt{2}\) and \(r_{n(\ell+12)} \leq 2^{-6} r_{n(\ell)}\). For \(0 \leq p \leq 11\) consider the set \(J_{p}=\{n(p+12 q) ; q \geq 0, p+12 q \leq N\}\), so that \(I=\cup_{0 \leq p \leq 11} J_{p}\). Consequently \(\sum_{k \in I} a_{k}=\sum_{0 \leq p \leq 11} \sum_{k \in J_{p}} a_{k}\). Thus using (3.28) there exists some \(p, 0 \leq p \leq 11\) such that \(\sum_{k \in J_{p}} a_{k} \geq S^{\prime 2} / L\) and the set \(J_{p}\) satisfies the desired requirements.

Consider a set \(J\) as constructed in Lemma 3.2.1. We then replace \(\mathcal{P}\) by the subset \(\mathcal{P}^{\prime}\) consisting of the points \(t \in \mathcal{P}\) such that \(t_{i}=0\) when \(i \in I_{k}\) and \(k \notin J\). We note that \(\sum_{k \in J} r_{k} \sqrt{\operatorname{card} I_{k}}=\sum_{k \in J} 2^{-2 k} \operatorname{card} I_{k} / S^{\prime} \geq S^{\prime} / L\), where the last inequality follows from (3.25).

We have now finished our preliminary reductions. To construct inside any ellipsoid \(\mathcal{E}\) an organized tree \(\mathcal{T}\) such that its depth \(\tau(\mathcal{T})\) witnesses the size of \(\mathcal{E}\) it suffices to perform the same task for a set of the type
\[
\mathcal{P}^{\prime}=\left\{t \in \ell^{2}\left(I^{*}\right) ; \forall k \leq N, \sum_{i \in I_{k}} t_{i}^{2} \leq r_{k}^{2}\right\}
\]
where \(N\) is a given integer, where \(\left(I_{k}\right)_{k \leq N}\) are disjoint subsets of \(\mathbb{N}^{*}\) of union \(I^{*}\), and where \(r_{k+1} \leq 2^{-6} r_{k}\). Just as in (3.24), the size of \(\mathcal{P}^{\prime}\) is about \(\sum_{k \leq N} r_{k} \sqrt{\operatorname{card} I_{k}}\).

For \(k \leq N\) let us consider the sphere
\[
\mathbb{S}_{k}=\left\{t \in \ell^{2}\left(I^{*}\right) ; \sum_{i \in I_{k}} t_{i}^{2} \leq r_{k}^{2}, i \notin I_{k} \Rightarrow t_{i}=0\right\}
\]

It follows from the volume argument (2.45) (used for \(A=B\) and \(\epsilon=1 / 2\) ) that there is a subset \(U_{k}\) of \(\mathbb{S}_{k}\) with
\[
\operatorname{card} U_{k} \geq 2^{\operatorname{card} I_{k}}
\]
such that any two distinct points of \(U_{k}\) are at distance \(\geq r_{k} / 2\). Given \(1 \leq\) \(m \leq N\) and for \(k \leq m\) given \(y_{k} \in U_{k}, y_{k}=\left(y_{k, i}\right)_{i \in I^{*}}\) consider the set \(A=A\left(y_{1}, \ldots, y_{m}\right) \subset \mathcal{P}^{\prime}\) defined as
\[
A\left(y_{1}, \ldots, y_{m}\right)=\left\{t \in \ell^{2}\left(I^{*}\right) ; \forall k \leq m, \forall i \in I_{k}, t_{i}=y_{k, i}\right\}
\]

We will show that these sets, together with the set \(\mathcal{P}^{\prime}\) form an organized tree \(\mathcal{T}\) (as defined in the previous section). When \(m<N\) we have \(c(A)=\) card \(U_{m+1}\) : the children of \(A\) are the sets \(A\left(y_{1}, \ldots, y_{m}, y\right)\) where \(y \in U_{m+1}\). When \(m=N\) we have \(c(A)=0\) and \(A\left(y_{1}, \ldots, y_{N}\right)\) consists of single point. We now check the condition (3.10). Consider \(m<N\). Define \(j(A)\) as the smallest integer \(j\) such that \(4^{-j-1} \leq r_{m+1} / 2\), so that \(r_{m+1} \leq 2 \cdot 4^{-j}\). For \(y \in U_{m+1}\) consider the unique point \(t(y) \in A\left(y_{1}, \ldots, y_{m}, y\right)\) such that \(t(y)_{i}=0\) if \(i \in I_{k}\) for \(k>m+1\). Then for \(y, y^{\prime} \in U_{m+1}, y \neq y^{\prime}\) the distance \(d\left(t(y), t\left(y^{\prime}\right)\right)\) is the same as the distance between \(y\) and \(y^{\prime}\), which are different points of \(U_{m+1} \subset \mathbb{S}_{m+1}\). Thus
\[
4^{-j(A)-1} \leq r_{m+1} / 2 \leq d\left(t(y), t\left(y^{\prime}\right)\right) \leq 2 r_{m+1} \leq 4^{-j(A)+1}
\]

Furthermore, recalling that \(r_{k+1} \leq 2^{-6} r_{k}\) (so that in particular \(\sum_{k \geq m} r_{k} \leq\) \(\left.2 r_{m}\right)\) if \(x \in A\left(y_{1}, \ldots, y_{m}, y\right)\) then
\[
\|x-t(y)\| \leq \sum_{k \geq m+2} r_{k} \leq 2 r_{m+2} \leq 2 \cdot 2^{-6} r_{m+1} \leq 2^{-6} 4^{-j(A)+1}=4^{-j(A)-2}
\]
so that \(A\left(y_{1}, \ldots, y_{m}, y\right) \subset B\left(t(y), 4^{-j(A)-2}\right)\) as required. Let us now study \(\tau(\mathcal{T})\). A branch in \(\mathcal{T}\) is defined by a point \(t \in S_{\mathcal{T}}\), which is the unique point of a set of the type \(A\left(y_{1}, \ldots, y_{N}\right)\). Let us set \(A_{0}=\mathcal{P}^{\prime}\) and \(A_{m}:=A\left(y_{1}, \ldots, y_{m}\right)\) for \(1 \leq m<N\). Then \(t \in A_{m}\) for \(0 \leq m<N\). Also \(c\left(A_{m}\right)=\operatorname{card} U_{m+1}\) and \(4^{-j\left(A_{m}\right)} \geq r_{m+1} / 2\). Thus, using (3.29) in the last equality,
\[
\begin{aligned}
\sum_{t \in A \in \mathcal{T}} 4^{-j(A)} & \sqrt{\log c(A)} \geq \sum_{0<m<N} 4^{-j\left(A_{m}\right)} \sqrt{\log c\left(A_{m}\right)} \\
& \geq \sum_{0<m<N} r_{m+1} \sqrt{\log \operatorname{card} U_{m+1}} / L \geq \sum_{1 \leq k \leq N} r_{k} \sqrt{\operatorname{card} I_{k}} / L
\end{aligned}
\]
and, as we have seen, this last quantity is the size of \(\mathcal{P}^{\prime}\).

\subsection*{3.3 Fernique's Functional}

\subsection*{3.3.1 Fernique's Functional}

In Section 3.1 we presented four equivalent methods to measure the size of a metric space. (Besides our usual \(\gamma_{2}(T, d)\), these were the maximum depth of a separated or organized tree contained in \(T\) and majorizing measures.) Recalling (3.17), it will turn out that a fifth measure of size, the quantity
\[
\operatorname{Fer}(T, d):=\sup _{\mu} \int_{T} I_{\mu}(t) \mathrm{d} \mu(t)
\]
will play an important role. Here the supremum is over all probability measures on \(T\) which are supported by a countable set.

Why is the functional (3.30) important? This is far from obvious. In the context of Gaussian processes, this functional has no special importance, and the related notion of majorizing measures is not particularly useful. We will first understand the usefulness of Fernique's functional in Chapter 5 while studying a class of processes which are conditionally Gaussian. Furthermore in following chapters we will be able to use similar ideas in far more general situations, while the proper generalization of the other functionals has not been found yet. In this section we will prove that Fernique's functional is equivalent to the functional \(\gamma_{2}(T, d)\),
\[
\frac{1}{L} \gamma_{2}(T, d) \leq \operatorname{Fer}(T, d) \leq L \gamma_{2}(T, d) .
\]

We will not give the simplest possible proof of this fact. Rather we will prepare for future work by giving arguments which contain in germ the ideas which will prove fruitful. The ideas of this section will not be critically used before Chapter 11.

A further understanding of Fernique's functional will be reached is Section 3.5 where we will basically show the remarkable fact that the supremum in the right-hand side of (3.30) is obtained when \(\mu\) is the "law of the supremum", i.e. the law of a r.v. such that \(X_{\tau}=\sup _{t \in T} X_{t}\), see Theorem 3.5.1 below.

The right-hand side inequality in (3.31) is the easiest, and is a consequence of the following.

\section*{Proposition 3.3.1. Consider a probability measure \(\mu\) on a metric space} ( \(T, d\) ). Then
\[
\int_{T} I_{\mu}(t) \mathrm{d} \mu(t) \leq L \gamma_{2}(T, d) .
\]

Proof. For each \(t \in T\) we define \(\epsilon_{0}(t)=\Delta(T)\) and for \(n \geq 1\) we define
\[
\epsilon_{n}(t)=\inf \left\{\epsilon>0 ; \mu(B(t, \epsilon)) \geq N_{n+1}^{-1}\right\} .
\]

Thus \(\sqrt{\log (1 / \mu(B(t, \epsilon)))} \leq L 2^{n / 2}\) for \(\epsilon \geq \epsilon_{n}(t)\) and then
\[
\begin{array}{r}
I_{\mu}(t)=\int_{0}^{\Delta(T)} \sqrt{\frac{1}{\log (\mu(B(t, \epsilon)))}} \mathrm{d} \epsilon=\sum_{n \geq 0} \int_{\epsilon_{n+1}(t)}^{\epsilon_{n}(t)} \sqrt{\frac{1}{\log (\mu(B(t, \epsilon)))}} \mathrm{d} \epsilon \\
\leq L \sum_{n \geq 0} 2^{n / 2} \epsilon_{n}(t)
\end{array}
\]

Consider an admissible sequence ( \(\mathcal{A}_{n}\) ) of partitions with
\[
\forall t \in T, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq 2 \gamma_{2}(T, d)
\]

Let us fix \(n \geq 0\) and set
\[
T_{n}=\left\{t \in T ; \mu\left(A_{n}(t)\right) \geq N_{n+1}^{-1}\right\} ; T_{n}^{\prime}=T \backslash T_{n}=\left\{t \in T ; \mu\left(A_{n}(t)\right)<N_{n+1}^{-1}\right\}
\]

Thus \(T_{n}^{\prime}\) is the union of the sets \(A \in \mathcal{A}_{n}\) which are of measure \(\leq N_{n+1}^{-1}\). Since \(\operatorname{card} \mathcal{A}_{n} \leq N_{n}\) we have \(\mu\left(T_{n}^{\prime}\right) \leq N_{n} N_{n+1}^{-1}=N_{n}^{-1}\). Also, by definition of \(\epsilon_{n}(t)\) we have \(\epsilon_{n}(t) \leq \Delta\left(A_{n}(t)\right)\) if \(t \in T_{n}\) and \(\epsilon_{n}(t) \leq \Delta(T)\) if \(t \in T_{n}^{\prime}\). Consequently,
\[
\begin{aligned}
\int_{T} 2^{n / 2} \epsilon_{n}(t) \mathrm{d} \mu(t)=\int_{T_{n}} 2^{n / 2} & \epsilon_{n}(t) \mathrm{d} \mu(t)+\int_{T_{n}^{\prime}} 2^{n / 2} \epsilon_{n}(t) \mathrm{d} \mu(t) \\
& \leq \int_{T_{n}} 2^{n / 2} \Delta\left(A_{n}(t)\right) \mathrm{d} \mu(t)+L \Delta(T) 2^{n / 2} N_{n}^{-1}
\end{aligned}
\]

Combining with (3.34) we obtain
\[
\int_{T} I_{\mu}(t) \mathrm{d} \mu(t) \leq L \Delta(T)+L \int_{T} \sum_{n \geq 1} 2^{n / 2} \Delta\left(A_{n}(t)\right) \mathrm{d} \mu(t)
\]

Integrating (3.35) with respect to \(\mu\) proves that the last term of (3.36) is \(\leq K \gamma_{2}(T, d)\) and concludes the proof since \(\Delta(T) \leq L \gamma_{2}(T, d)\).

\subsection*{3.3.2 Fernique's Convexity Argument}

Our approach to the left-hand side of (3.31) is based on the following elementary fact, which is a consequence of the Hahn-Banach theorem.

Lemma 3.3.2. Consider a number a \(>0\). Consider a set \(\mathcal{S}\) of functions on a finite set \(T\). Assume that for each probability measure \(\nu\) on \(T\) there exists \(f \in \mathcal{S}\) such that \(\int f \mathrm{~d} \nu \leq a\). Then for each \(\epsilon>0\) there is a function \(f\) in the convex hull of \(\mathcal{S}\) such that \(f \leq a+\varepsilon\).

Proof. Denote \(\mathcal{S}^{+}\)the set of functions \(g\) such that there exists \(f \in \mathcal{S}\) with \(f \leq g\). Denote by \(\mathcal{C}\) the closed convex hull of \(\mathcal{S}^{+}\). We prove that the constant function a equal to \(a\) everywhere belongs to \(\mathcal{C}\). We proceed by contradiction. If this is not the case, by the Hahn-Banach theorem we may separate \(\mathcal{C}\) and a. That is, there exists a linear functional \(\varphi\) on the space of functions on \(T\) such that \(\varphi(f)>\varphi(\mathrm{a})\) for \(f \in \mathcal{C}\). Consider then a function \(g\) on \(T\) with \(g \geq 0\). For each \(\lambda>0\) and each \(f \in \mathcal{S}\) we have \(f+\lambda g \geq f\) so that \(f+\lambda g \in \mathcal{C}\) and hence \(\varphi(f)+\lambda \varphi(g)>\varphi(\mathrm{a})\). This proves that \(\varphi(g) \geq 0\), i.e. \(\varphi\) is positive. Since \(T\) is finite, \(\varphi\) is of the form \(\varphi(g)=\sum_{t \in T} \alpha_{t} g(t)\) for numbers \(\alpha_{t} \geq 0\). Setting \(\beta=\sum_{t \in T} \alpha(t)\) consider the probability measure \(\nu\) on \(T\) given by \(\nu(\{t\})=\alpha_{t} / \beta\) for \(t \in T\). Then \(\varphi(g)=\beta \int g \mathrm{~d} \nu\) for each function \(g\) on \(T\). Taking \(g=\) a shows that \(\beta a=\varphi(\mathrm{a})\). By hypothesis there exists \(f \in \mathcal{C}\) with \(\int f \mathrm{~d} \nu \leq a\). Then \(\varphi(f)=\beta \int f \mathrm{~d} \nu \leq \beta a=\varphi(\mathrm{a})\), a contradiction.

So we have proved that a \(\in \mathcal{C}\), the closure of the convex hull of \(\mathcal{S}^{+}\). Consequently there is one point of this convex hull which is \(\leq a+\epsilon\) everywhere. The result follows.

Of course, the hypothesis that \(T\) is finite is inessential, it is just to avoid secondary complications.

Let us give a version of the basic lemma sufficiently general to cover all our needs.

Lemma 3.3.3. Consider a finite metric space ( \(T, d\) ). Consider a convex function \(\Phi:] 0,1] \rightarrow \mathbb{R}^{+}\). Assume that for each probability measure \(\mu\) on \(T\) and a certain number \(D\) one has
\[
\int_{T} \mathrm{~d} \mu(t) \int_{0}^{\Delta(T)} \Phi(\mu(B(t, \epsilon))) \mathrm{d} \epsilon \leq D
\]

Then there exists a probability measure \(\mu\) on \(T\) for which
\[
\sup _{t \in T} \int_{0}^{\Delta(T)} \Phi(\mu(B(t, \epsilon))) \mathrm{d} \epsilon \leq 2 D
\]

Proof. Let us denote by \(\mathcal{M}(T)\) the set of probability measures on \(T\). The class \(\mathcal{C}\) of functions \(f\) on \(T\) that satisfy
\[
\exists \mu \in \mathcal{M}(T) ; \forall t \in T, f_{\mu}(t):=\int_{0}^{\Delta(T)} \Phi(\mu(B(t, \epsilon))) \mathrm{d} \epsilon \leq f(t)
\]
is convex. This is immediate to check using the convexity of \(\Phi\). For each probability measure \(\nu\) on \(T\), there exists \(f\) in \(\mathcal{C}\) with \(\int f \mathrm{~d} \nu \leq B\) : this is true for \(f=f_{\nu}\) by (3.37). Consequently by Lemma 3.3.2, there exists \(f \in \mathcal{C}\) such that \(f \leq 2 B\), which is the content of the lemma.

Corollary 3.3.4. Consider a finite metric space ( \(T, d\) ). Assume that for a certain number \(C\) and for each probability measure \(\mu\) on \(T\) we have
\[
\int_{T} I_{\mu}(t) \mathrm{d} \mu(t) \leq C
\]

Then there is probability measure \(\mu\) on \(T\) such that
\[
\forall t \in T, I_{\mu}(t) \leq 2 C+2 \Delta(T)
\]

Proof. Calculus shows that the function \(\Phi(x)=\sqrt{\log (e / x)}\) is convex for \(x \in] 0,1]\), and \(\sqrt{\log (1 / x)} \leq \Phi(x) \leq 1+\sqrt{\log (1 / x)}\). Thus
\[
I_{\mu}(t) \leq \int_{0}^{\Delta(T)} \Phi(\mu(B(t, \epsilon))) \mathrm{d} \epsilon \leq \Delta(T)+I_{\mu}(t)
\]
so that (3.39) implies that (3.37) holds for \(D=C+\Delta(T)\) and (3.40) then follows from (3.38).

Lemma 3.3.5. We have \(\Delta(T) \leq L \operatorname{Fer}(T, d)\).
Proof. Consider \(s, u \in T\) with \(d(s, u) \geq \Delta(T) / 2\) and the probability \(\mu\) on \(T\) such that \(\mu(\{s\})=\mu(\{u\})=1 / 2\). For \(\epsilon<\Delta(T) / 2 \leq d(s, u)\) we have \(\mu(B(s, \epsilon)) \leq 1 / 2\) and this implies that \(I_{\mu}(s) \geq \Delta(T) \sqrt{\log 2} / 2\). Similarly we have \(I_{\mu}(u) \geq \Delta(T) / L\) so that \(\int_{T} I_{\mu}(t) \mathrm{d} \mu(t) \geq \Delta(T) / L\).

Proof of (3.31) when \(T\) is finite. Combining Corollary 3.3.4 and Lemma 3.3.5 we obtain that there exists a probability \(\mu\) on \(T\) such that \(\sup _{t \in T} I_{\mu}(t) \leq\) \(L \operatorname{Fer}(T, d)\). On the other hand we have proved in Section 3.1 that \(\gamma_{2}(T, d) \leq\) \(L \sup _{t \in T} I_{\mu}(t) .^{5}\)

\subsection*{3.3.3 From Majorizing Measures to Sequences of Partitions}

In Section 3.1 we have proved that given a probability measure \(\mu\) on a metric space \(T\) we have
\[
\gamma_{2}(T, d) \leq L \sup _{t \in T} I_{\mu}(t)
\]

We do not know how to generalize the arguments of the proof to the more general settings we will consider later. We give now a direct proof, following a scheme which we know how to generalize. The contents of this section will not be relevant until Chapter 11. First, we prove that
\[
\Delta(T) \leq L \sup _{t \in T} I_{\mu}(t)
\]

For this we consider \(s, t \in T\) with \(d(s, t)>\Delta(T) / 2\) so that since the balls \(B(t, \Delta(T) / 2)\) and \(B(s, \Delta(T) / 2)\) are disjoint, one of them, say the first one,

\footnotetext{
\({ }^{5}\) The argument by which we have proved this inequality will not generalize, but fortunately there is another route, which is described in the next section.
}
has a measure \(\leq 1 / 2\). Then \(\mu(B(t, \epsilon)) \leq 1 / 2\) for \(\epsilon \leq \Delta(T) / 2\) and thus \(I_{\mu}(t) \geq\) \(\sqrt{\log 2} \Delta(T) / 2\).

We start the main argument. We will construct an admissible sequence ( \(\mathcal{A}_{n}\) ) of partitions of \(T\) which witnesses (3.41). For \(A \in \mathcal{A}_{n}\) we also construct an integer \(j_{n}(A)\) as follows. First, we set \(\mathcal{A}_{0}=\mathcal{A}_{1}=\{T\}\) and \(j_{0}(T)=\) \(j_{1}(T)=j_{0}\), the largest integer with \(\Delta(T) \leq 2^{-j_{0}(T)}\). Next for \(n \geq 1\) we require the conditions
\[
\begin{gathered}
A \in \mathcal{A}_{n} \Rightarrow \Delta(A) \leq 2^{-j_{n}(A)+2} \\
t \in A \in \mathcal{A}_{n} \Rightarrow \mu\left(B\left(t, 2^{-j_{n}(A)}\right)\right) \geq N_{n-1}^{-1}
\end{gathered}
\]

The construction proceeds as follows. Having constructed \(\mathcal{A}_{n}\) we split each element \(A\) of \(\mathcal{A}_{n}\) into at most \(N_{n}\) pieces, ensuring that \(\operatorname{card} \mathcal{A}_{n+1} \leq N_{n}^{2}=\) \(N_{n+1}\). For this we set
\[
A_{0}=\left\{t \in A ; \mu\left(B\left(t, 2^{-j_{n}(A)-1}\right)\right) \geq 1 / N_{n}\right\} .
\]

We claim first that we may cover \(A_{0}\) by \(<N_{n}\) sets, each of diameter \(\leq\) \(2^{-j_{n}(A)+1}\). For this we consider a subset \(W\) of \(A_{0}\), maximal with respect to the property that any two points of \(W\) are at distance \(>2^{-j_{n}(A)}\). The balls of radius \(2^{-j_{n}(A)-1}\) centered at the points of \(W\) are disjoint, and each of them is of measure \(>N_{n}^{-1}\) by (3.45), so that there are \(<N_{n}\) of them. Since \(W\) is maximum, the balls of radius \(2^{-j_{n}(A)}\) centered at the points of \(W\) cover \(A_{0}\), and each of them has diameter \(\leq 2^{-j_{n}(A)+1}\). Thus there exists a partition of \(A_{0}\) in \(<N_{n}\) sets of diameter \(\leq 2^{-j_{n}(A)+1}\). The required partition of \(A\) consists of these sets \(B\) and of \(A_{1}=A \backslash A_{0}\). For each set \(B\) we set \(j_{n+1}(B)=j_{n}(A)+1\), and we set \(j_{n+1}\left(A_{1}\right)=j_{n}(A)\), so that conditions (3.43) and 3.44 hold.

This completes the construction. The important point is that
\[
\begin{aligned}
B \in \mathcal{A}_{n+1}, B \subset A \in A_{n}, j_{n+1}(B)=j_{n}(A) & \Rightarrow \mu\left(B\left(t, 2^{-j_{n+1}(B)}\right)\right) \\
= & \mu\left(B\left(t, 2^{-j_{n}(A)}\right)\right) \leq N_{n}^{-1}
\end{aligned}
\]

This property holds because if \(t \in A\) and \(\mu\left(B\left(t, 2^{-j_{n}(A)}\right)\right)>N_{n}^{-1}\) then \(t \in\) \(A_{0}\) and the element \(B\) of \(\mathcal{A}_{n+1}\) which contains \(t\) has been assigned a value \(j_{n+1}(B)=j_{n}(A)+1\).

To prove (3.41) we will prove that
\[
\forall t \in T, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq L I_{\mu}(t)
\]

We fix \(t \in T\). We set \(j(n)=j_{n}\left(A_{n}(t)\right)\), and \(a(n)=2^{n / 2} 2^{-j(n)}\). Using (3.43) it suffices to prove that
\[
\sum_{n \geq 0} a(n) \leq L I_{\mu}(t) .
\]

We consider \(\alpha=\sqrt{2}\) and the corresponding set \(I\) as in (2.84). (Leaving to the reader to prove that the sequence \((a(n))\) is bounded, which will become clear soon.) Thus as in (2.91) we have
\[
n \in I, n \geq 1 \Rightarrow j(n+1)=j(n)+1, j(n-1)=j(n)
\]

We enumerate \(I \backslash\{0\}\) as a sequence \(\left(n_{k}\right)_{k \geq 0}\) (leaving again to the reader the easier case where \(I\) is finite), so that \(j\left(n_{k+1}\right) \geq j\left(n_{k}+1\right)=j\left(n_{k}\right)+1\) and
\[
\sum_{n \geq 0} a(n) \leq L a(0)+L \sum_{k \geq 1} a\left(n_{k}\right)
\]

From (3.48) we have \(j\left(n_{k}-1\right)=j\left(n_{k}\right)\). Using (3.46) for \(n=n_{k}-1\) we obtain
\[
\mu\left(B\left(t, 2^{-j\left(n_{k}\right)}\right)\right)<N_{n_{k}-1}^{-1}
\]
so that \(\sqrt{\log (1 / \mu(B(t, \epsilon))} \geq 2^{n_{k} / 2} / L\) for \(\epsilon<2^{-j\left(n_{k}\right)}\). Since \(j\left(n_{k+1}\right)>j\left(n_{k}\right)\) this implies
\[
\begin{aligned}
a\left(n_{k}\right)=2^{n_{k} / 2-j\left(n_{k}\right)} \leq 2 \cdot 2^{n_{k} / 2}\left(2^{-j\left(n_{k}\right)}-2^{j\left(n_{k+1}\right)}\right) & \\
& \leq L \int_{2^{-j\left(n_{k+1}\right)}}^{2^{-j\left(n_{k}\right)}} \sqrt{\log \frac{1}{\mu(B(t, \epsilon))}} \mathrm{d} \mu(\epsilon)
\end{aligned}
\]

Summation of these inequalities and use of (3.42) and (3.49) proves (3.47).

\subsection*{3.4 Witnessing Measures}

Proposition 3.4.1. ([91]) For a metric space ( \(T, d\) ), define
\[
\delta_{2}(T, d)=\sup _{\mu} \inf _{t \in T} I_{\mu}(t)
\]
where the supremum is taken over all probability measures \(\mu\) on \(T .^{6}\) Then
\[
\frac{1}{L} \gamma_{2}(T, d) \leq \delta_{2}(T, d) \leq L \gamma_{2}(T, d)
\]

It is obvious that \(\inf _{t \in T} I_{\mu}(t) \leq \int_{T} I_{\mu}(t) \mathrm{d} \mu(t)\) so that \(\delta_{2}(T, d) \leq \operatorname{Fer}(T, d)\). Thus the right-hand side of (3.51) follows from the right-hand side of (3.31) while the left-hand side of (3.31) follows from the left-hand side of (3.51).

The most important consequence of (3.31) is that there exists a probability measure \(\mu\) on \(T\) for which \(\inf _{t \in T} I_{\mu}(t) \geq \gamma_{2}(T, d) / L\). Such a probability

\footnotetext{
\({ }^{6}\) Please observe that the order of the infimum and the supremum is not as in (3.20).
}
measure "witnesses that the value of \(\gamma_{2}(T, d)\) is large" because of the righthand side of (3.51). In this spirit, we will call \(\mu\) a witnessing measure, and we define its "size" as the quantity \(\inf _{t \in T} I_{\mu}(t) .^{7}\) Witnessing measures can be magically convenient. One of the first advances the author made beyond the results of [171] was the realization that witnessing measures yield a proof of Theorem 5.2.1 below an order of magnitude easier than the original proof. This approach is now replaced by the use of Fernique's functional because, unfortunately we do not know how to extend the idea of witnessing measure to settings where multiple distances will be considered. Finding proper generalizations of Proposition 3.4.1 to more general settings is an attractive research problem, see in particular Problem 10.15.4.
Proof of Proposition 3.4.1. The right-hand side inequality follows from Proposition 3.3.1 and the trivial fact that \(\inf _{t \in T} I_{\mu}(t) \leq \int I_{\mu}(t) \mathrm{d} \mu(t)\). The reader should review the material of Section 3.1 to follow the proof of the converse. Recalling (3.5), given an organized tree \(\mathcal{T}\) we define a measure \(\mu\) on \(T\) by \(\mu(A)=0\) if \(A \cap S_{\mathcal{T}}=\emptyset\) and by
\[
t \in S_{\mathcal{T}} \Rightarrow \mu(\{t\})=\frac{1}{\prod_{t \in A \in \mathcal{T}} c(A)}
\]

The intuition is that the mass carried by \(A \in \mathcal{T}\) is equally divided between the children of \(A\). Then \(I_{\mu}(t)=\infty\) if \(t \notin S_{\mathcal{T}}\). Consider \(t \in A \in \mathcal{T}\) and \(j=j(A)\). Then, since \(\mathcal{T}\) is an organized tree, \(B\left(t, 4^{-j-2}\right)\) meets only one child of \(A\), so that \(\mu\left(B\left(t, 4^{-j-2}\right)\right) \leq 1 / c(A)\). Copying the argument of (3.22) readily implies that \(L I_{\mu}(t) \geq \sum_{t \in A \in \mathcal{T}} 4^{-j(A)} \sqrt{\log c(A)}\) from which the result follows by (3.15).
Exercise 3.4.2. For a metric space ( \(T, d\) ) define
\[
\chi_{2}(T, d)=\sup _{\mu} \inf \int \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \mathrm{d} \mu(t)
\]
where the infimum is taken over all admissible sequences and the supremum over all probability measures. It is obvious that \(\chi_{2}(T, d) \leq \gamma_{2}(T, d)\). Prove that \(\gamma_{2}(T, d) \leq L \chi_{2}(T, d)\). Hint: Prove that the functional \(\chi_{2}(T, d)\) satisfies the appropriate growth condition. Warning: the argument takes about half a page and is fairly non-trivial.

\subsection*{3.5 An inequality of Fernique}

We end up this chapter with a beautiful inequality of Fernique. It will not be used anywhere else in this work, but is presented to emphasize Fernique's lasting contributions to the theory of Gaussian processes.

\footnotetext{
Thus a probability measure \(\mu\) on \(T\) is both a majorizing and a witnessing measure. It bounds \(\gamma_{2}(T, d)\) from above by \(L \sup _{t \in T} I_{\mu}(t)\) and from below by \(\inf _{t \in T} I_{\mu}(t) / L\). Furthermore one may find \(\mu\) such that these two bounds are of the same order.
}

Theorem 3.5.1. Consider a Gaussian process \(\left(X_{t}\right)_{t \in T}\). Provide \(T\) with the canonical distance associated to this process and consider a probability measure \(\mu\) on \(T\). Consider a r.v. \(\tau\) of law \(\mu\). Then for any probability measure \(\nu\) on \(T\) we have
\[
\mathrm{E} X_{\tau} \leq L \int I_{\nu}(t) \mathrm{d} \mu(t)+L \Delta(T, d)
\]

Here of course \(X_{\tau}\) is the r.v. \(X_{\tau(\omega)}(\omega)\). We leave some technical details aside and prove the result only when \(T\) is finite. The basic principle is as follows.

Lemma 3.5.2. Consider a standard Gaussian r.v. \(g\) and a set \(A\). Then \(\mathrm{E} 1_{A}|g| \leq L \mathrm{P}(A) \sqrt{\log (2 / \mathrm{P}(A))}\).

Proof. We write
\[
\mathrm{E} 1_{A}|g|=\int_{0}^{\infty} \mathrm{P}(A \cap\{|g| \geq t\}) \mathrm{d} t \leq \int_{0}^{\infty} \min \left(\mathrm{P}(A), 2 \exp \left(-t^{2} / 2\right)\right) \mathrm{d} t
\]

Letting \(\alpha=\sqrt{2 \log (2 / \mathrm{P}(A))}\) we split the integral in the regions \(t \leq \alpha\) and \(t>\alpha\). We bound the first part by \(\alpha \mathrm{P}(A)\) and the second by
\[
\int_{\alpha}^{\infty} 2 \exp \left(-t^{2} / 2\right) \mathrm{d} t \leq \frac{1}{\alpha} \int_{\alpha}^{\infty} 2 t \exp \left(-t^{2} / 2\right) \mathrm{d} t=\mathrm{P}(A) / \alpha \leq L \mathrm{P}(A) \alpha
\]

Corollary 3.5.3. Consider Gaussian r.v.s \(\left(g_{i}\right)_{i \leq N}\) with \(\mathrm{E} g_{i}^{2} \leq a^{2}\). Consider a r.v. \(\tau\) valued in \(\{1, \ldots, N\}\), and let \(\alpha_{i}=\mathrm{P}(\tau=i)\). Then \(\mathrm{E}\left|g_{\tau}\right| \leq\) \(L a \sum_{i \leq N} \alpha_{i} \sqrt{\log 2 / \alpha_{i}}\).

Proof. Since \(\left|g_{\tau}\right|=\sum_{i \leq N} \mathbf{1}_{\{\tau=i\}}\left|g_{i}\right|\), and using Lemma 3.5.2 to obtain \(\mathrm{E} 1_{\{\tau=i\}}\left|g_{i}\right| \leq L a \alpha_{i} \sqrt{\log 2 / \alpha_{i}}\).

We also need the following elementary convexity inequality.
Lemma 3.5.4. Consider numbers \(\alpha_{i}>0\) with \(\sum_{i \leq N} \alpha_{i} \leq \alpha \leq 1\). Then
\[
\sum_{i \leq N} \alpha_{i} \sqrt{\log \left(2 / \alpha_{i}\right)} \leq \alpha \sqrt{\log (2 N / \alpha)} .
\]

Proof. Calculus shows that the function \(\varphi(x)=x \sqrt{\log (2 / x)}\) is concave increasing for \(x \leq 1\), so that if \(\alpha^{\prime}=\sum_{i \leq N} \alpha_{i}\) then \(N^{-1} \sum_{i \leq N} \varphi\left(\alpha_{i}\right) \leq\) \(\varphi\left(\alpha^{\prime} / N\right) \leq \varphi(\alpha / N)\).

The slightly technical part of the proof of Theorem 3.5.1 is contained in the following.

Lemma 3.5.5. Consider probability measures \(\mu\) and \(\nu\) on a metric space \((T, d)\). Then there exist \(n_{0} \in \mathbb{Z}\) and a sequence of partitions \(\left(\mathcal{B}_{n}\right)_{n \geq n_{0}}\) (which need not be increasing) of \(T\) with the following properties. First, \(\mathcal{B}_{n_{0}}\) contains only one set. Next, the sets of \(\mathcal{B}_{n}\) are of diameter \(\leq 2^{-n+2}\). Finally,
\[
\begin{aligned}
\sum_{n \geq n_{0}} 2^{-n} \sum_{A \in \mathcal{B}_{n}, B \in \mathcal{B}_{n+1}} \mu(A \cap B) \sqrt{\log (2 / \mu(A \cap B))} & \\
& \leq L \int I_{\nu}(t) \mathrm{d} \mu(t)+L \Delta(T, d)
\end{aligned}
\]

Proof. We consider the largest integer \(n_{0}\) with \(2^{-n_{0}} \geq \Delta(T, d)\). We set \(\mathcal{B}_{n_{0}}=\) \(\{T\}\). For \(n>n_{0}\) we proceed as follows. For \(k \geq 0\) we set
\[
T_{n, k}=\left\{t \in T ; 1 / N_{k+1}<\nu\left(B\left(t, 2^{-n}\right)\right) \leq 1 / N_{k}\right\}
\]

The sets \(\left(T_{n, k}\right)_{k \geq 0}\) form a partition of \(T\). Consider a subset \(V\) of \(T_{n, k}\) such that the points of \(V\) are at mutual distance \(>2^{-n+1}\). The balls of radius \(2^{-n}\) centered at the points of \(V\) are disjoint and by definition of \(T_{n, k}\) they have a \(\nu\)-measure \(>1 / N_{k+1}\). Thus card \(V \leq N_{k+1}\) and according to Lemma 2.9.3, \(e_{k+1}\left(T_{n, k}\right) \leq 2^{-n+1}\) and thus \(T_{n, k}\) can be partitioned into at most \(N_{k+1}\) sets of diameter \(\leq 2^{-n+2}\). We construct such a partition \(\mathcal{B}_{n, k}\) of \(T_{n, k}\) for each \(k\) and we consider the corresponding partition \(\mathcal{B}_{n}\) of \(T\).

We now turn to the proof of (3.54). First we note that \(\operatorname{card} \mathcal{B}_{n, k} \leq N_{k+1}\) and \(\operatorname{card} \mathcal{B}_{n+1, \ell} \leq N_{\ell+1}\). Also \(\sum_{A \in \mathcal{B}_{n, k}, B \in \mathcal{B}_{n+1, \ell}} \mu(A \cap B) \leq \mu\left(T_{n, k} \cap T_{n+1, \ell}\right)\). We then use (3.53) to obtain
\[
\begin{aligned}
S_{n, k, \ell} & :=\sum_{A \in \mathcal{B}_{n, k}, B \in \mathcal{B}_{n+1, \ell}} \mu(A \cap B) \sqrt{\log (2 / \mu(A \cap B))} \\
& \leq \mu\left(T_{n, k} \cap T_{n+1, \ell}\right) \sqrt{\log \left(2 N_{k+1} N_{\ell+1} / \mu\left(T_{n, k} \cap T_{n+1, \ell}\right)\right)}
\end{aligned}
\]

The left-hand side of (3.54) is \(\sum_{n \geq n_{0}} 2^{-n} \sum_{k, \ell} S_{n, k, \ell}\). We will use the decomposition
\[
\sum_{k, \ell} S_{n, k, \ell}=\sum_{(k, \ell) \in I(n)} S_{n, k, \ell}+\sum_{(k, \ell) \in J(n)} S_{n, k, \ell}
\]
where \(I(n)=\left\{(k, \ell) ; \mu\left(T_{n, k} \cap T_{n+1, \ell}\right) \geq 1 /\left(N_{k+1} N_{\ell+1}\right)\right\}\) and \(J(n)=\) \(\left\{(k, \ell) ; \mu\left(T_{n, k} \cap T_{n+1, \ell}\right)<1 /\left(N_{k+1} N_{\ell+1}\right)\right\}\). Then
\[
\begin{aligned}
\sum_{(k, \ell) \in I(n)} S_{n, k, \ell} & \leq \sum_{k, \ell} \mu\left(T_{n, k} \cap T_{n+1, \ell}\right) \sqrt{\log \left(2 N_{k+1}^{2} N_{\ell+1}^{2}\right)} \\
& \leq L \sum_{k, \ell} \mu\left(T_{n, k} \cap T_{n+1, \ell}\right)\left(2^{k / 2}+2^{\ell / 2}\right) \\
& \leq L \sum_{k} \mu\left(T_{n, k}\right) 2^{k / 2}+L \sum_{\ell} \mu\left(T_{n+1, \ell}\right) 2^{\ell / 2} .
\end{aligned}
\]

Now the definition of \(T_{n, k}\) shows that
\[
\sum_{k \geq 1} \mu\left(T_{n, k}\right) 2^{k / 2} \leq \int \sqrt{\log \left(1 / \nu\left(B\left(t, 2^{-n}\right)\right)\right.} \mathrm{d} \mu(t)
\]
and thus
\[
\sum_{n \geq n_{0}} 2^{-n} \sum_{k \geq 1} 2^{k / 2} \mu\left(T_{n, k}\right) \leq L \int I_{\nu}(t) \mathrm{d} \mu(t)
\]

Next, since the function \(\varphi(x)=x \sqrt{\log 2 N_{k+1} N_{\ell+1} / x}\) increases for \(x \leq\) \(N_{k+1} N_{\ell+1}\), for \((k, \ell) \in J(n)\) we have \(\varphi\left(\mu\left(T_{n, k} \cap T_{n+1, \ell}\right)\right) \leq \varphi\left(1 /\left(N_{k+1} N_{\ell+1}\right)\right)\) so that
\[
\begin{aligned}
\sum_{(k, \ell) \in J(n)} S_{n, k, \ell} & \leq \sum_{(k, \ell) \in J(n)} \varphi\left(\mu\left(T_{n, k} \cap T_{n+1, \ell}\right)\right) \leq \sum_{(k, \ell) \in J(n)} \varphi\left(1 / N_{k+1} N_{\ell+1}\right) \\
& \leq L \sum_{k, \ell} \frac{2^{k / 2}+2^{\ell / 2}}{N_{k+1} N_{\ell+1}} \leq L
\end{aligned}
\]

Combining these estimates yields the desired inequality:
\[
\sum_{n \geq n_{0}} 2^{-n} \sum_{k, \ell \geq 0} S_{n, k, \ell} \leq L \int I_{\nu}(t) \mathrm{d} \mu(t)+L 2^{-n_{0}}
\]

Proof of Theorem 3.5.1. For \(n \geq n_{0}\) and \(A \in \mathcal{B}_{n}\) we fix an arbitrary point \(t_{n, A} \in A\). We lighten notation by writing \(t_{0}=t_{n_{0}, T}\). We define \(\pi_{n}(t)=t_{n, A}\) for \(t \in A \in \mathcal{B}_{n}\). We write
\[
X_{\tau}-X_{t_{0}}=\sum_{n \geq n_{0}} X_{\pi_{n+1}(\tau)}-X_{\pi_{n}(\tau)}
\]
so that defining \(Y_{n, \tau}:=X_{\pi_{n+1}(\tau)}-X_{\pi_{n}(\tau)}\) we have
\[
\mathrm{E}\left|X_{\tau}-X_{t_{0}}\right| \leq \sum_{n \geq n_{0}} \mathrm{E}\left|Y_{n, \tau}\right|
\]

Given \(A \in \mathcal{B}_{n}\) and \(B \in \mathcal{B}_{n+1}\) let us define the variable \(Y_{A, B}:=X_{t_{n+1, B}}-\) \(X_{t_{n, A}}\). The sets \(A \cap B\) for \(A \in \mathcal{B}_{n}\) and \(B \in \mathcal{B}_{n+1}\) form a partition of \(T\). When \(\tau(\omega) \in A \cap B\) we have \(\pi_{n}(\tau)=t_{n, A}\) and \(\pi_{n+1}(\tau)=t_{n+1, B}\) so that \(Y_{n, \tau}=Y_{A, B}\). The event \(\tau(\omega) \in A \cap B\) has probability \(\mu(A \cap B)\) since \(\tau\) has law \(\mu\). When \(A \cap B \neq \emptyset\) we have \(d\left(t_{n+1, B}, t_{n, A}\right) \leq \Delta(A)+\Delta(B) \leq L 2^{-n}\), so that \(\mathrm{E} Y_{A, B}^{2}=d\left(t_{n+1, B}, t_{n, A}\right)^{2} \leq L 2^{-2 n}\). It then follows from Corollary 3.5.3 that
\[
\mathrm{E}\left|Y_{n, \tau}\right| \leq L 2^{-n} \sum_{A \in \mathcal{B}_{n}, B \in \mathcal{B}_{n+1}} \mu(A \cap B) \sqrt{\log (2 / \mu(A \cap B))}
\]
and summation over \(n\) and use of (3.54) finishes the proof.
It is actually possible to give a complete geometric description of the quantity \(\sup _{\tau} \mathrm{E} X_{\tau}\) where the supremum is taken over all the \(\tau\) of given law \(\mu\), see [137].
Key ideas to remember.
- Trees in a metric space ( \(T, d\) ) are well-separated structures which are easy to visualize, and provide a convenient way to measure the size of this metric space, by the size of the largest tree it contains. For suitable classes of trees, this measure of size is equivalent to \(\gamma_{2}(T, d)\).
- One may also measure the size of a metric space by the existence of certain probability measures on this space. Fernique's majorizing measures were used early to control from above the size of a metric space in a way very similar to the functional \(\gamma_{2}(T, d)\), which is however far more technically convenient than majorizing measures.
- An offshoot of the idea of majorizing measures, Fernique's functional, is an equivalent way to measure the size of a metric space and will be of fundamental importance in the sequel.
- The size of a metric space \((T, d)\) can also be bounded from below by the existence of well-scattered probability measures on \(T\).

\section*{4. Matching Theorems}

We remind the reader that, before attacking any chapter, she should find useful to read the overview of this chapter, which is provided in the appropriate subsection of Chapter 1. Here this overview should help to understand the overall approach.

\subsection*{4.1 The Ellipsoid Theorem}

As pointed out after Proposition 2.13.2, an ellipsoid \(\mathcal{E}\) is in some sense quite smaller than what one would predict by looking only at the numbers \(e_{n}(\mathcal{E})\). We will trace the roots of this phenomenon to a simple geometric property, namely that an ellipsoid is "sufficiently convex", and we will formulate a general version of this principle for sufficiently convex bodies. The case of ellipsoids already suffices to provide tight upper bounds on certain matchings, which is the main goal of the present chapter. The general case is at the root of certain very deep facts of Banach space theory, such as Bourgain's celebrated solution of the \(\Lambda_{p}\)-problem in Sections 19.3.1 and 19.3.2.

Recall the ellipsoid \(\mathcal{E}\) of (2.154), which is defined as the set
\[
\mathcal{E}=\left\{t \in \ell^{2} ; \sum_{i \geq 1} \frac{t_{i}^{2}}{a_{i}^{2}} \leq 1\right\}
\]
and is the unit ball of the norm
\[
\|x\|_{\mathcal{E}}:=\left(\sum_{i \geq 1} \frac{x_{i}^{2}}{a_{i}^{2}}\right)^{1 / 2}
\]

Lemma 4.1.1. We have
\[
\|x\|_{\mathcal{E}},\|y\|_{\mathcal{E}} \leq 1 \Rightarrow\left\|\frac{x+y}{2}\right\|_{\mathcal{E}} \leq 1-\frac{\|x-y\|_{\mathcal{E}}^{2}}{8}
\]

Proof. The parallelogram identity implies
\[
\|x-y\|_{\mathcal{E}}^{2}+\|x+y\|_{\mathcal{E}}^{2}=2\|x\|_{\mathcal{E}}^{2}+2\|y\|_{\mathcal{E}}^{2} \leq 4
\]
so that
\[
\|x+y\|_{\mathcal{E}}^{2} \leq 4-\|x-y\|_{\mathcal{E}}^{2}
\]
and
\[
\left\|\frac{x+y}{2}\right\|_{\mathcal{E}} \leq\left(1-\frac{1}{4}\|x-y\|_{\mathcal{E}}^{2}\right)^{1 / 2} \leq 1-\frac{1}{8}\|x-y\|_{\mathcal{E}}^{2} .
\]

Since (4.2) is the only property of ellipsoids we will use, it clarifies matters to state the following definition.

Definition 4.1.2. Consider a number \(p \geq 2\). A norm \(\|\cdot\|\) in a Banach space is called \(p\)-convex if for a certain number \(\eta>0\) we have
\[
\|x\|,\|y\| \leq 1 \Rightarrow\left\|\frac{x+y}{2}\right\| \leq 1-\eta\|x-y\|^{p} .
\]

Saying just that the unit ball of the Banach space is convex implies that for \(\|x\|,\|y\| \leq 1\) we have \(\|(x+y) / 2\| \leq 1\). Here (4.3) quantitatively improves on this inequality. Geometrically it means that the unit ball of the Banach space is "round enough".

Thus (4.2) implies that the Banach space \(\ell^{2}\) provided with the norm \(\|\cdot\|_{\mathcal{E}}\) is 2 -convex. For \(1<q<\infty\) the classical Banach space \(L^{q}\) is \(p\)-convex where \(p=\max (2, q)\). The reader is referred to [80] for this result and any other classical facts about Banach spaces. Let us observe that, taking \(y=-x\) in (4.3) we must have
\[
2^{p} \eta \leq 1
\]

In this section we shall study the metric space ( \(T, d\) ) where \(T\) is the unit ball of a \(p\)-convex Banach space \(B\), and where \(d\) is the distance induced on \(B\) by another norm \(\|\cdot\|_{\sim}\). This concerns in particular the case where \(T\) is the ellipsoid (2.154) and \(\|\cdot\|_{\sim}\) is the \(\ell^{2}\) norm.

Given a metric space ( \(T, d\) ), we consider the functionals
\[
\gamma_{\alpha, \beta}(T, d)=\inf \left(\sup _{t \in T} \sum_{n \geq 0}\left(2^{n / \alpha} \Delta\left(A_{n}(t)\right)\right)^{\beta}\right)^{1 / \beta},
\]
where \(\alpha\) and \(\beta\) are positive numbers, and where the infimum is over all admissible sequences \(\left(\mathcal{A}_{n}\right)\). Thus, with the notation of Definition 2.7.3, we have \(\gamma_{\alpha, 1}(T, d)=\gamma_{\alpha}(T, d)\). For matchings, the important functionals are \(\gamma_{2,2}(T, d)\) and \(\gamma_{1,2}(T, d)\) (but it requires no extra effort to consider the general case). The importance of these functionals is that under certain conditions they nicely relate to \(\gamma_{2}(T, d)\) through Hölder's inequality. We explain right now how this is done, even though this spoils the surprise of how the terms \(\sqrt{\log N}\) occur in Section 4.5.

Lemma 4.1.3. Consider a finite metric space \(T\), and assume that \(\operatorname{card} T \leq\) \(N_{m}\). Then
\[
\gamma_{2}(T, d) \leq \sqrt{m} \gamma_{2,2}(T, d)
\]

Proof. Since \(T\) is finite there exists \({ }^{1}\) an admissible sequence \(\left(\mathcal{A}_{n}\right)\) of \(T\) for which
\[
\forall t \in T, \sum_{n \geq 0} 2^{n} \Delta\left(A_{n}(t)\right)^{2} \leq \gamma_{2,2}(T, d)^{2}
\]

Since card \(T \leq N_{m}\), we may assume that \(\mathcal{A}_{m}\) consists of all the sets \(\{t\}\) for \(t \in T\). Then \(A_{m}(t)=\{t\}\) for each \(t\), so that in (4.7) the sum is really over \(n \leq m-1\). Since for any numbers \(\left(a_{n}\right)_{0 \leq n \leq m-1}\) we have \(\sum_{0 \leq n \leq m-1} a_{n} \leq\) \(\sqrt{m}\left(\sum_{0 \leq n \leq m-1} a_{n}^{2}\right)^{1 / 2}\) by the Cauchy-Schwarz inequality, it follows that
\[
\forall t \in T, \sum_{n \geq 0} 2^{n / 2} \Delta\left(A_{n}(t)\right) \leq \sqrt{m} \gamma_{2,2}(T, d)
\]

How to relate the functionals \(\gamma_{1,2}\) and \(\gamma_{2}\) by a similar argument is shown in Lemma 4.7.9 below.

We may wonder how it is possible, using something as simple as the Cauchy-Schwarz inequality in Lemma 4.1.3, that we can ever get essentially exact results. At a general level the answer is obvious: it is because we use this inequality in the case of near equality. That this is indeed the case for the ellipsoids of Corollary 4.1.7 below is a non-trivial fact about the geometry of these ellipsoids.

Theorem 4.1.4. If \(T\) is the unit ball of a \(p\)-convex Banach space, if \(\eta\) is as in (4.3) and if the distance \(d\) on \(T\) is induced by another norm, then
\[
\gamma_{\alpha, p}(T, d) \leq K(\alpha, p, \eta) \sup _{n \geq 0} 2^{n / \alpha} e_{n}(T, d)
\]

Before we prove this result (in Section 4.2) we explore some of its consequences. The following exercise stresses the main point of this theorem.

Exercise 4.1.5. Consider a general metric space ( \(T, d\) ).
(a) Prove that
\[
\gamma_{\alpha, p}(T, d) \leq K(\alpha)\left(\sum_{n \geq 0}\left(2^{n / \alpha} e_{n}(T, d)\right)^{p}\right)^{1 / p}
\]
and that
\[
\sup _{n \geq 0} 2^{n / \alpha} e_{n}(T, d) \leq K(\alpha) \gamma_{\alpha, p}(T, d)
\]
(b) Prove that it is essentially impossible in general to improve on (4.9). Hint: you probably want to review Chapter 3 before you try this.

\footnotetext{
\({ }^{1}\) Since there are only finitely many admissible sequences, the infimum over these is achieved.
}

Thus, knowing only the numbers \(e_{n}(T, d)\), we would expect only the general bound (4.9). The content of Theorem 4.1.4 is that the size of \(T\), as measured by the functional \(\gamma_{\alpha, p}\), is actually much smaller than that.

Corollary 4.1.6. (The Ellipsoid Theorem.) Consider the ellipsoid \(\mathcal{E}\) of (2.154) and \(\alpha \geq 1\). Then \({ }^{2}\)
\[
\gamma_{\alpha, 2}(\mathcal{E}) \leq K(\alpha) \sup _{\epsilon>0} \epsilon\left(\operatorname{card}\left\{i ; a_{i} \geq \epsilon\right\}\right)^{1 / \alpha}
\]

Proof. Without loss of generality we may assume that the sequence ( \(a_{i}\) ) is non-increasing. We apply Theorem 4.1.4 to the case \(\|\cdot\|=\|\cdot\|_{\mathcal{E}}\), and where \(d\) is the distance of \(\ell^{2}\), and we get
\[
\gamma_{\alpha, 2}(\mathcal{E}) \leq K(\alpha) \sup _{n \geq 0} 2^{n / \alpha} e_{n}(\mathcal{E})
\]

To bound the right-hand side we write
\[
\sup _{n \geq 0} 2^{n / \alpha} e_{n}(\mathcal{E}) \leq 2^{2 / \alpha} e_{0}(\mathcal{E})+\sup _{n \geq 0} 2^{(n+3) / \alpha} e_{n+3}(\mathcal{E})
\]

We now proceed as in the proof of Proposition 2.13.2. Using (2.166), we have
\[
\begin{aligned}
\sup _{n \geq 0} 2^{(n+3) / \alpha} e_{n+3}(\mathcal{E}) & \leq K(\alpha) \sup _{n \geq 0} 2^{n / \alpha} \max _{k \leq n} 2^{k-n} a_{2^{k}} \\
& =K(\alpha) \sup _{0 \leq k \leq n} 2^{k-n(1-1 / \alpha)} a_{2^{k}} \\
& =K(\alpha) \sup _{k \geq 0} 2^{k / \alpha} a_{2^{k}}
\end{aligned}
\]
and since \(e_{0}(\mathcal{E}) \leq a_{1}\) we have proved that \(\gamma_{\alpha, 2}(\mathcal{E}) \leq K(\alpha) \sup _{n \geq 0} 2^{n / \alpha} a_{2^{n}}\). Finally, the choice \(\epsilon=a_{2^{n}}\) shows that
\[
2^{n / \alpha} a_{2^{n}} \leq \sup _{\epsilon>0} \epsilon\left(\operatorname{card}\left\{i ; a_{i} \geq \epsilon\right\}\right)^{1 / \alpha}
\]
since \(\operatorname{card}\left\{i ; a_{i} \geq a_{2^{n}}\right\} \geq 2^{n}\) because the sequence ( \(a_{i}\) ) is non-increasing.
The restriction \(\alpha \geq 1\) is inessential and can be removed by a suitable modification of (2.166). The important cases are \(\alpha=1\) and \(\alpha=2\). We will use the following convenient reformulation.

Corollary 4.1.7. Consider a countable set \(J\), numbers \(\left(b_{i}\right)_{i \in J}\) and the ellipsoid
\[
\mathcal{E}=\left\{x \in \ell^{2}(J) ; \sum_{j \in J} b_{j}^{2} x_{j}^{2} \leq 1\right\}
\]

Then
\[
\gamma_{\alpha, 2}(\mathcal{E}) \leq K(\alpha) \sup _{u>0} \frac{1}{u}\left(\operatorname{card}\left\{j \in J ;\left|b_{j}\right| \leq u\right\}\right)^{1 / \alpha}
\]

\footnotetext{
\({ }^{2}\) Recalling that a subset of \(\ell^{2}\) is always provided with the distance induced by the \(\ell^{2}\) norm.
}

Proof. Without loss of generality we can assume that \(J=\mathbb{N}\). We then set \(a_{i}=1 / b_{i}\), we apply Corollary 4.1.6, and we set \(\epsilon=1 / u\).

We give right away a striking application of this result. This application is at the root of the results of Section 4.7. We denote by \(\lambda\) Lebesgue's measure.

Proposition 4.1.8. Consider the set \(\mathcal{L}\) of functions \(f:[0,1] \rightarrow \mathbb{R}\) such that \(f(0)=f(1)=0, f\) is continuous on \([0,1], f\) is differentiable outside a finite set and \(\sup \left|f^{\prime}\right| \leq 1 .^{3}\) Then \(\gamma_{1,2}\left(\mathcal{L}, d_{2}\right) \leq L\), where \(d_{2}(f, g)=\|f-g\|_{2}=\) \(\left(\int_{[0,1]}(f-g)^{2} \mathrm{~d} \lambda\right)^{1 / 2}\).

Proof. The very beautiful idea (due to Coffman and Shor [36]) is to use the Fourier transform to represent \(\mathcal{L}\) as a subset of an ellipsoid. The Fourier coefficients of a function \(f \in \mathcal{L}\) are defined for \(p \in \mathbb{Z}\) by
\[
c_{p}(f)=\int_{0}^{1} \exp (2 \pi i p x) f(x) \mathrm{d} x
\]

The key fact is the Plancherel formula,
\[
\|f\|_{2}=\left(\sum_{p \in \mathbb{Z}}\left|c_{p}(f)\right|^{2}\right)^{1 / 2}
\]
which states that the Fourier transform is an isometry from \(L^{2}([0,1])\) into \(\ell_{\mathbb{C}}^{2}(\mathbb{Z})\). Thus, if
\[
\mathcal{D}=\left\{\left(c_{p}(f)\right)_{p \in \mathbb{Z}} ; f \in \mathcal{L}\right\}
\]
the metric space ( \(\mathcal{L}, d_{2}\) ) is isometric to a subspace of ( \(\mathcal{D}, d\) ), where \(d\) is the distance induced by \(\ell_{\mathbb{C}}^{2}(\mathbb{Z})\). It is then obvious from the definition that \(\gamma_{1,2}\left(\mathcal{L}, d_{2}\right) \leq \gamma_{1,2}(\mathcal{D}, d)\), so that it suffices to prove that \(\gamma_{1,2}(\mathcal{D}, d)<\infty\). By integration by parts, and since \(f(0)=f(1)=0, c_{p}\left(f^{\prime}\right)=-2 \pi i p c_{p}(f)\), so that, using (4.13) for \(f^{\prime}\), we get
\[
\sum_{p \in \mathbb{Z}} p^{2}\left|c_{p}(f)\right|^{2} \leq \sum_{p \in \mathbb{Z}}\left|c_{p}\left(f^{\prime}\right)\right|^{2}=\left\|f^{\prime}\right\|_{2}^{2}
\]

For \(f \in \mathcal{L}\) we have \(f(0)=0\) and \(\left|f^{\prime}\right| \leq 1\) so that \(|f| \leq 1\) and \(\left|c_{0}(f)\right| \leq 1\). Thus for \(f \in \mathcal{L}\) we have
\[
\left|c_{0}(f)\right|^{2}+\sum_{p \in \mathbb{Z}} p^{2}\left|c_{p}(f)\right|^{2} \leq 2
\]
and thus \(\mathcal{D}\) is a subset of the complex ellipsoid \(\mathcal{E}\) in \(\ell_{\mathbb{C}}^{2}(\mathbb{Z})\) defined by
\[
\mathcal{E}:=\left\{\left(c_{p}\right) \in \ell_{\mathbb{C}}^{2}(\mathbb{Z}) ; \sum_{p \in \mathbb{Z}} \max \left(1, p^{2}\right)\left|c_{p}\right|^{2} \leq 2\right\}
\]

\footnotetext{
\({ }^{3}\) The same result holds for the set \(\mathcal{L}^{\prime}\) of 1-Lipschitz functions \(f\) with \(f(0)=f(1)=\) 0 , since \(\mathcal{L}\) is dense in \(\mathcal{L}^{\prime}\).
}

Viewing each complex number \(c_{p}\) as a pair ( \(x_{p}, y_{p}\) ) of real numbers with \(\left|c_{p}\right|^{2}=x_{p}^{2}+y_{p}^{2}\) yields that \(\mathcal{E}\) is (isometric to) the real ellipsoid defined by
\[
\sum_{p \in \mathbb{Z}} \max \left(1, p^{2}\right)\left(x_{p}^{2}+y_{p}^{2}\right) \leq 2 .
\]

We then apply Corollary 4.1.7 as follows. The set \(J\) consists of two copies of \(\mathbb{Z}\). There is a two-to-one map \(\varphi\) from \(J\) to \(\mathbb{Z}\) and \(b_{j}=\max (1,|\varphi(j)|)\). Then \(\operatorname{card}\left\{j \in J ;\left|b_{j}\right| \leq u\right\} \leq L u\) for \(u \geq 1\) and \(=0\) for \(u<1\).

Exercise 4.1.9. (a) For \(k \geq 1\) consider the space \(T=\{0,1\}^{2^{k}}\). Writing \(t=\left(t_{i}\right)_{i \leq 2^{k}}\) a point of \(T\), consider on \(T\) the distance \(d\left(t, t^{\prime}\right)=2^{-j-1}\), where \(j=\min \left\{i \leq 2^{k} ; t_{i} \neq t_{i}^{\prime}\right\}\). Consider the set \(\mathcal{L}\) of 1-Lipschitz functions on ( \(T, d\) ) which are zero at \(t=(0, \ldots, 0)\). Prove that \(\gamma_{1,2}\left(\mathcal{L}, d_{\infty}\right) \leq L \sqrt{k}\), where \(d_{\infty}\) denotes the distance induced by the uniform norm. Hint: use Lemma 4.5.18 below to prove that \(e_{n}\left(\mathcal{L}, d_{\infty}\right) \leq L 2^{-n}\) and conclude using (4.9).
(b) Let \(\mu\) denote the uniform probability on \(T\) and \(d_{2}\) the distance induced by \(L^{2}(\mu)\). It can be shown that \(\gamma_{1,2}\left(\mathcal{L}, d_{2}\right) \geq \sqrt{k} / L\). (This could be challenging even if you master Chapter 3.) Meditate upon the difference with Proposition 4.1.8.

\subsection*{4.2 Partitioning Scheme, II}

Consider parameters \(\alpha, p \geq 1\).
Theorem 4.2.1. Consider a metric space ( \(T, d\) ) and a number \(r \geq 4\). Assume that for \(j \in \mathbb{Z}\) we are given functions \(s_{j} \geq 0\) on \(T\) with the following property:

Whenever we consider a subset \(A\) of \(T\) and \(j \in \mathbb{Z}\) with \(\Delta(A) \leq 2 r^{-j}\)
then for each \(n \geq 1\) either \(e_{n}(A) \leq r^{-j-1}\) or else there exists \(t \in A\)
with \(s_{j}(t) \geq\left(2^{n / \alpha} r^{-j-1}\right)^{p}\).
Then we can find an admissible sequence ( \(\mathcal{A}_{n}\) ) of partitions such that
\[
\forall t \in T ; \sum_{n \geq 0}\left(2^{n / \alpha} \Delta\left(A_{n}(t)\right)\right)^{p} \leq K(\alpha, p, r)\left(\Delta(T, d)^{p}+\sup _{t \in T} \sum_{j \in \mathbb{Z}} s_{j}(t)\right)
\]

The proof is identical to that of Theorem 2.9.8 which corresponds to the case \(\alpha=2\) and \(p=1\).

Proof of Theorem 4.1.4. We recall that by hypothesis \(T\) is the unit ball for the norm \(\|\cdot\|\) of \(p\)-convex Banach space (but we study \(T\) for the metric \(d\) induced by a different norm). For \(t \in T\) and \(j \in \mathbb{Z}\) we set
\[
c_{j}(t)=\inf \left\{\|v\| ; v \in B_{d}\left(t, r^{-j}\right) \cap T\right\} \leq 1
\]
where the index \(d\) emphasizes that the ball is for the distance \(d\) rather than for the norm. Since \(T\) is the unit ball we have \(c_{j}(t) \leq 1\). Let us set
\[
D=\sup _{n \geq 0} 2^{n / \alpha} e_{n}(T, d)
\]

The proof relies on Theorem 4.2.1 for the functions
\[
s_{j}(t)=K D^{p}\left(c_{j+2}(t)-c_{j-1}(t)\right)
\]
for a suitable value of \(K\). Since \(c_{j}(t) \leq 1\) it is clear that
\[
\forall t \in T, \sum_{j \in \mathbb{Z}} s_{j}(t) \leq 3 K D^{p}
\]
and (using also that \(\Delta(T, d) \leq 2 e_{0}(T, d)\) ) the issue is to prove that (4.14) holds for a suitable constant \(K\) in (4.18). Consider then a set \(A \subset T\) with \(\Delta(A) \leq 2 r^{-j}\), consider \(n \geq 1\) and assume that \(e_{n}(A)>a:=r^{-j-1}\). The goal is to find \(t \in A\) such that \(s_{j}(t) \geq\left(2^{n / \alpha} r^{-j-1}\right)^{p}\) i.e.
\[
K D^{p}\left(c_{j+2}(t)-c_{j-1}(t)\right) \geq\left(2^{n / \alpha} r^{-j-1}\right)^{p}
\]

For this let \(m=N_{n}\), According to Lemma 2.9.3, (a) there exist points \(\left(t_{\ell}\right)_{\ell \leq m}\) in \(A\), such that \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \geq a\) whenever \(\ell \neq \ell^{\prime}\). We will show that one of the points \(t_{\ell}\) satisfies (4.19). Consider \(H_{\ell}=T \cap B_{d}\left(t_{\ell}, a / r\right)=T \cap B_{d}\left(t_{\ell}, r^{-j-2}\right)\). By definition of \(c_{j+1}\left(t_{\ell}\right)\) we have \(c_{j+2}\left(t_{\ell}\right)=\inf \left\{\|v\| ; v \in H_{\ell}\right\}\). The basic idea is that the points of the different sets \(H_{\ell}\) cannot be too close to each other for the norm of \(T\) because there are \(N_{n}\) such sets. So, since the norm is sufficiently convex, we will find a point in the convex hull of these sets with a norm quite smaller than \(\max _{\ell \leq m} c_{j+2}\left(t_{\ell}\right)\). To implement the idea, consider \(u^{\prime}\) such that
\[
2>u^{\prime}>\max _{\ell \leq m} \inf \left\{\|v\| ; v \in H_{\ell}\right\}=\max _{\ell \leq m} c_{j+2}\left(t_{\ell}\right)
\]

For \(\ell \leq m\) consider \(v_{\ell} \in H_{\ell}\) with \(\left\|v_{\ell}\right\| \leq u^{\prime}\). It follows from (4.3) that for \(\ell, \ell^{\prime} \leq m\),
\[
\left\|\frac{v_{\ell}+v_{\ell^{\prime}}}{2 u^{\prime}}\right\| \leq 1-\eta\left\|\frac{v_{\ell}-v_{\ell^{\prime}}}{u^{\prime}}\right\|^{p}
\]

Set
\[
u=\inf \left\{\|v\| ; v \in \operatorname{conv} \bigcup_{\ell \leq m} H_{\ell}\right\}
\]

Since \(\left(v_{\ell}+v_{\ell^{\prime}}\right) / 2 \in \operatorname{conv} \bigcup_{\ell \leq m} H_{\ell}\), by definition of \(u\) we have \(u \leq\left\|v_{\ell}+v_{\ell}^{\prime}\right\| / 2\), and (4.21) implies
\[
\frac{u}{u^{\prime}} \leq 1-\eta\left\|\frac{v_{\ell}-v_{\ell^{\prime}}}{u^{\prime}}\right\|^{p}
\]
so that, using that \(u^{\prime}<2\) in the second inequality below,
\[
\left\|v_{\ell}-v_{\ell^{\prime}}\right\| \leq u^{\prime}\left(\frac{u^{\prime}-u}{\eta u^{\prime}}\right)^{1 / p}<R:=2\left(\frac{u^{\prime}-u}{\eta}\right)^{1 / p}
\]
and hence the points \(w_{\ell}:=R^{-1}\left(v_{\ell}-v_{1}\right)\) belong to the unit ball \(T\). Now, since \(H_{\ell} \subset B_{d}\left(t_{\ell}, a / r\right)\) we have \(v_{\ell} \in B_{d}\left(t_{\ell}, a / r\right)\). Since \(r \geq 4\), we have \(d\left(v_{\ell}, v_{\ell^{\prime}}\right) \geq\) \(a / 2\) for \(\ell \neq \ell^{\prime}\), and, since the distance \(d\) arises from a norm, by homogeneity we have \(d\left(w_{\ell}, w_{\ell^{\prime}}\right) \geq R^{-1} a / 2\) for \(\ell \neq \ell^{\prime}\). Then Lemma 2.9.3, (c) implies that \(e_{n-1}(T, d) \geq R^{-1} a / 4\), so that from (4.17) it holds that \(2^{(n-1) / \alpha} R^{-1} a / 4 \leq D\), and recalling that \(R=2\left(\left(u^{\prime}-u\right) / \eta\right)^{1 / p}\) we obtain
\[
\left(2^{n / \alpha} r^{j-1}\right)^{p} \leq K D^{p}\left(u^{\prime}-u\right)
\]
where \(K\) depends on \(\alpha\) only. Since this holds for any \(u^{\prime}\) as in (4.20), there exists \(\ell\) such that
\[
\left(2^{n / \alpha} r^{j-1}\right)^{p} \leq K D^{p}\left(c_{j+2}\left(t_{\ell}\right)-u\right)
\]

Now, by construction, for \(\ell^{\prime} \leq m\) we have
\[
H_{\ell^{\prime}} \subset B_{d}\left(t_{\ell^{\prime}}, a / r\right)=B_{d}\left(t_{\ell^{\prime}}, r^{-j-2}\right) \subset B_{d}\left(t_{\ell}, r^{-j+1}\right)
\]
since \(d\left(t_{\ell}, t_{\ell^{\prime}}\right) \leq 2 r^{-j}\) as \(t_{\ell}, t_{\ell^{\prime}} \in A\) and \(\Delta(A) \leq 2 r^{-j}\). Thus conv \(\bigcup_{\ell^{\prime} \leq m} H_{\ell^{\prime}} \subset\) \(B_{d}\left(t_{\ell}, r^{-j+1}\right) \cap T\), and from (4.16) and (4.22) we have \(u \geq c_{j-1}\left(t_{\ell}\right)\) and we have proved (4.19).

Exercise 4.2.2. Write the previous proof using a certain functional with an appropriate growth condition.

The following generalization of Theorem 4.1.4 yields very precise results when applied to ellipsoids. It will not be used in the sequel, so we refer to [171] for a proof.

Theorem 4.2.3. Consider \(\beta, \beta^{\prime}, p>0\) with
\[
\frac{1}{\beta}=\frac{1}{\beta^{\prime}}+\frac{1}{p}
\]

Then, under the conditions of Theorem 4.1.4 we have
\[
\gamma_{\alpha, \beta}(T, d) \leq K(p, \eta, \alpha)\left(\sum_{n}\left(2^{n / \alpha} e_{n}(T, d)\right)^{\beta^{\prime}}\right)^{1 / \beta^{\prime}}
\]

Exercise 4.2.4. Use Theorem 4.2.3 to obtain a geometrical proof of (2.159). Hint: Choose \(\alpha=2, \beta=1, \beta^{\prime}=p=2\) and use (2.166).

\subsection*{4.3 Matchings}

The rest of this chapter is devoted to the following problem. Consider \(N\) r.v.s \(X_{1}, \ldots, X_{N}\) independently and uniformly distributed in the unit cube \([0,1]^{d}\), where \(d \geq 1\). Consider a typical realization of these points. How evenly distributed in \([0,1]^{d}\) are the points \(X_{1}, \ldots, X_{N}\) ? To measure this, we will match the points \(\left(X_{i}\right)_{i \leq N}\) with non-random "evenly distributed" points \(\left(Y_{i}\right)_{i \leq N}\), that is, we will find a permutation \(\pi\) of \(\{1, \ldots, N\}\) such that the points \(X_{i}\) and \(Y_{\pi(i)}\) are "close". There are different ways to measure "closeness". For example one may wish that the sum of the distances \(d\left(X_{i}, Y_{\pi(i)}\right)\) be as small as possible (Section 4.5), that the maximum distance \(d\left(X_{i}, Y_{\pi(i)}\right)\) be as small as possible (Section 4.7), or one can use more complicated measures of "closeness" (Section 17.1).

The case \(d=1\) is by far the simplest. Assuming that the \(X_{i}\) are labeled in a way that \(X_{1} \leq X_{2} \leq \ldots\) and similarly for the \(Y_{i}\) one has \(\operatorname{Esup}{ }_{i \leq N}\left|X_{i}-Y_{i}\right| \leq\) \(L \sqrt{N}\). This is a consequence of the classical inequality (which we will later prove as an exercise)
\[
\mathrm{E} \sup _{0 \leq t \leq 1}\left|\operatorname{card}\left\{i \leq N ; X_{i} \leq t\right\}-N t\right| \leq L \sqrt{N}
\]

The case where \(d=2\) is very special, and is the object of the present chapter. The case \(d \geq 3\) will be studied in Chapter 18. The reader having never thought of the matter might think that the points \(X_{1}, \ldots, X_{N}\) are very evenly distributed. This is not quite the case; for example, with probability close to one, one is bound to find a little square of area about \(N^{-1} \log N\) that contains no point \(X_{i}\). This is a very local irregularity. In a somewhat informal manner one can say that this irregularity occurs at scale \(\sqrt{\log N} / \sqrt{N}\). This specific irregularity is mentioned just as an easy illustration, and plays no part in the considerations of the present chapter. What matters here \({ }^{4}\) is that in some sense there are irregularities at all scales \(2^{-k}\) for \(1 \leq k \leq L^{-1} \log N\), and that these are all of the same order. To see this, let us think that we actually move the points \(X_{i}\) to the points \(Y_{\pi(i)}\) in straight lines. In a given small square of side \(2^{-k}\) there is often an excess of points \(X_{i}\) of order \(\sqrt{N 2^{-2 k}}=2^{-k} \sqrt{N}\). When matched these points will leave the square and will cross its boundary. The number of points crossing this boundary per unit of length is independent of the scale \(2^{-k}\). It will also often happen that there is a deficit of points \(X_{i}\) in this square of side \(2^{-k}\), and in this case some points \(X_{i}\) will have to cross the boundary to enter it. The flows at really different scales should be roughly independent, and there are about \(\log N\) such scales, so when we combine what happens at different scales we should get an extra factor \(\sqrt{\log N}\) (and not \(\log N)\). Crossing our fingers, we should believe that about \(\sqrt{N \log N}\) points \(X_{i}\) per unit of length cross a typical interval contained in the square, so that the total length of the segments joining the points \(X_{i}\) to the points \(Y_{\pi(i)}\)

\footnotetext{
\({ }^{4}\) This is much harder to visualize and is specific to the case \(d=2\).
}
should be of that order. \({ }^{5}\) This fact that all scales have the same weight is typical of dimension 2 . In dimension 1, it is the large scales that matter most, while in dimension \(\geq 3\) it is the small ones.

Exercise 4.3.1. Perform this calculation.
One can summarize the situation by saying that
obstacles to matchings at different scales may combine
in dimension 2 but not in dimension \(\geq 3\).
It is difficult to state a real theorem to this effect, but this is actually seen with great clarity in the proofs. The crucial estimates involve controlling sums each term of which represents a different scale. In dimension 2, many terms contribute to the final sum (which therefore results in the contribution of many different scales), while in higher dimension only a few terms contribute. (The case of higher dimension remains non-trivial because which terms contribute depend on the value of the parameter.) Of course these statements are very mysterious at this stage, but we expect that a serious study of the methods involved will gradually bring the reader to share this view.

What does it mean to say that the non-random points \(\left(Y_{i}\right)_{i \leq N}\) are evenly distributed? When \(N\) is a square, \(N=n^{2}\), everybody will agree that the \(N\) points \((k / n, \ell / n), 1 \leq k, \ell \leq n\) are evenly distributed, and unless you love details you are welcomed to stick to this case. More generally we will say that the non-random points \(\left(Y_{i}\right)_{i \leq N}\) are evenly spread if one can cover \([0,1]^{2}\) with \(N\) rectangles with disjoint interiors, such that each rectangle \(R\) has an area \(1 / N\), contains exactly one point \(Y_{i}\), and is such that \({ }^{6} R \subset B\left(Y_{i}, 10 / \sqrt{N}\right)\). To construct such points one may proceed as follows. Consider the largest integer \(k\) with \(k^{2} \leq N\), and observe that \(k(k+3) \geq(k+1)^{2} \geq N\), so that there exists integers \(\left(n_{i}\right)_{i \leq k}\) with \(k \leq n_{i} \leq k+3\) and \(\sum_{i<k} n_{i}=N\). Cut the unit square into \(k\) vertical strips, in a way that the \(i\)-th strip has width \(n_{i} / N\) and to this \(i\)-th strip attribute \(n_{i}\) points placed at even intervals \(1 / n_{i} .^{7}\)

The basic tool to construct matchings is the following classical fact. The proof, based on the Hahn-Banach theorem, is given in Section B.1.

Proposition 4.3.2. Consider a matrix \(C=\left(c_{i j}\right)_{i, j \leq N}\). Let

\footnotetext{
\({ }^{5}\) As we will see later, we have guessed the correct result.
\({ }^{6}\) There is nothing magic about the number 10. Thinks of it as a universal constant. The last thing I want is to figure out the best possible value. That 10 works should be obvious from the following construction.
\({ }^{7}\) A more elegant approach dispenses from this slightly awkward construction. It is the concept of "transportation cost". One attributes mass \(1 / N\) to each point \(X_{i}\), and one measures the "cost of transporting" the resulting probability measure to the uniform probability on \([0,1]^{2}\). In the presentation one thus replaces the evenly spread points \(Y_{i}\) by a more canonical object, the uniform probability on \([0,1]^{2}\). This approach does not make the proofs any easier, so we shall not use it despite its aesthetic appeal.
}
\[
M(C)=\inf \sum_{i \leq N} c_{i \pi(i)}
\]
where the infimum is over all permutations \(\pi\) of \(\{1, \ldots, N\}\). Then
\[
M(C)=\sup \sum_{i \leq N}\left(w_{i}+w_{i}^{\prime}\right)
\]
where the supremum is over all families \(\left(w_{i}\right)_{i \leq N},\left(w_{i}^{\prime}\right)_{i \leq N}\) that satisfy
\[
\forall i, j \leq N, w_{i}+w_{j}^{\prime} \leq c_{i j}
\]

Thus, if \(c_{i j}\) is the cost of matching \(i\) with \(j, M(C)\) is the minimal cost of a matching, and is given by the "duality formula" (4.27).

A well-known application of Proposition 4.3.2 is another "duality formula".
Proposition 4.3.3. Consider points \(\left(X_{i}\right)_{i \leq N}\) and \(\left(Y_{i}\right)_{i \leq N}\) in a metric space \((T, d)\). Then
\[
\inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right)=\sup _{f \in \mathcal{C}} \sum_{i \leq N}\left(f\left(X_{i}\right)-f\left(Y_{i}\right)\right)
\]
where \(\mathcal{C}\) denotes the class of 1 -Lipschitz functions on ( \(T\), d), i.e. functions \(f\) for which \(|f(x)-f(y)| \leq d(x, y)\).

Proof. Given any permutation \(\pi\) and any 1-Lipschitz function \(f\) we have
\[
\sum_{i \leq N} f\left(X_{i}\right)-f\left(Y_{i}\right)=\sum_{i \leq N}\left(f\left(X_{i}\right)-f\left(Y_{\pi(i)}\right)\right) \leq \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) .
\]

This proves the inequality \(\geq\) in (4.29). To prove the converse, we use (4.27) with \(c_{i j}=d\left(X_{i}, Y_{j}\right)\), so that
\[
\inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right)=\sup \sum_{i \leq N}\left(w_{i}+w_{i}^{\prime}\right)
\]
where the supremum is over all families \(\left(w_{i}\right)\) and \(\left(w_{i}^{\prime}\right)\) for which
\[
\forall i, j \leq N, w_{i}+w_{j}^{\prime} \leq d\left(X_{i}, Y_{j}\right)
\]

Given a family \(\left(w_{i}^{\prime}\right)_{i \leq N}\), consider the function
\[
f(x)=\min _{j \leq N}\left(-w_{j}^{\prime}+d\left(x, Y_{j}\right)\right)
\]

It is 1 -Lipschitz, since it is the minimum of functions which are themselves 1-Lipschitz. By definition we have \(f\left(Y_{j}\right) \leq-w_{j}^{\prime}\) and by (4.31) for \(i \leq N\) we have \(w_{i} \leq f\left(X_{i}\right)\), so that
\[
\sum_{i \leq N}\left(w_{i}+w_{i}^{\prime}\right) \leq \sum_{i \leq N}\left(f\left(X_{i}\right)-f\left(Y_{i}\right)\right)
\]

Exercise 4.3.4. Consider a function \(f\) which achieves the supremum in the right-hand side of (4.29). Prove that for an optimal matching we have \(f\left(X_{i}\right)-\) \(f\left(Y_{\pi(i)}\right)=d\left(X_{i}, Y_{\pi(i)}\right)\). If you know \(f\), this basically tells you how to find the matching. To find \(Y_{\pi(i)}\), move from \(X_{i}\) in the direction of steepest descent of \(f\) until you find a points \(Y_{j}\).

The following is a well-known, and rather useful result of combinatorics. We deduce it from Proposition 4.3.2 in Section B.1, but other proofs exist, based on different ideas, see e.g. [10] § 2.
Corollary 4.3.5 (Hall's Marriage Lemma). Assume that to each \(i \leq\) \(N\) we associate a subset \(A(i)\) of \(\{1, \ldots, N\}\) and that, for each subset \(I\) of \(\{1, \ldots, N\}\) we have
\[
\operatorname{card}\left(\bigcup_{i \in I} A(i)\right) \geq \operatorname{card} I
\]

Then we can find a permutation \(\pi\) of \(\{1, \ldots, N\}\) for which
\[
\forall i \leq N, \pi(i) \in A(i)
\]

\subsection*{4.4 Discrepancy Bounds}

Generally speaking, the study of expressions of the type
\[
\sup _{f \in \mathcal{F}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \mu\right)\right|
\]
for a class of functions \(\mathcal{F}\) will be important in the present book, particularly in Chapter 14. A bound on such a quantity is called a discrepancy bound because since
\[
\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \mu\right)\right|=N\left|\frac{1}{N} \sum_{i \leq N} f\left(X_{i}\right)-\int f \mathrm{~d} \mu\right|
\]
it bounds uniformly on \(\mathcal{F}\) the "discrepancy" between the true measure \(\int f \mathrm{~d} \mu\) and the "empirical measure" \(N^{-1} \sum_{i \leq N} f\left(X_{i}\right)\). Finding such a bound simply requires finding a bound for the supremum of the process \(\left(\left|Z_{f}\right|\right)_{f \in \mathcal{F}}\), where the (centered) r.v.s \(Z_{f}\) are given by \({ }^{8}\)
\[
Z_{f}=\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \mu\right)
\]
a topic at the very center of our attention.
A relation between discrepancy bounds and matching theorems can be guessed from Proposition 4.3.3 and will be made explicit in the next section. In this book every matching theorem will be proved through a discrepancy bound.

\footnotetext{
\({ }^{8}\) Please remember this notation which is used throughout this chapter.
}

\subsection*{4.5 The Ajtai-Komlós-Tusnády Matching Theorem}

Theorem 4.5.1 ([5]). If the points \(\left(Y_{i}\right)_{i \leq N}\) are evenly spread and the points \(\left(X_{i}\right)_{i \leq N}\) are i.i.d. uniform on \([0,1]^{2}\), then (for \(N \geq 2\) )
\[
\mathrm{E} \inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \leq L \sqrt{N \log N}
\]
where the infimum is over all permutations of \(\{1, \ldots, N\}\) and where \(d\) is the Euclidean distance.

The term \(\sqrt{N}\) is just a scaling effect. There are \(N\) terms \(d\left(X_{i}, Y_{\pi(i)}\right)\) each of which should be about \(1 / \sqrt{N}\). The non-trivial part of the theorem is the factor \(\sqrt{\log N}\). In Section 4.6 we shall show that (4.36) can be reversed, i.e.
\[
\mathrm{E} \inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \geq \frac{1}{L} \sqrt{N \log N}
\]

In order to understand that the bound (4.36) is not trivial, you can study the following greedy matching algorithm which was shown to me by Yash Kanoria.
Exercise 4.5.2. For each \(n \geq 0\) consider the partition \(\mathcal{H}_{n}\) of \([0,1]^{2}\) into \(2^{2 n}\) equal squares. Consider the largest integer \(n_{0}\) with \(2^{2 n_{0}} \leq N\) and proceed as follows. For each small square in \(\mathcal{H}_{n_{0}}\) match as many as possible of the points \(X_{i}\) with points \(Y_{i}\) in the same square. Remove the points \(X_{i}\) and the points \(Y_{i}\) that you have matched this way. For the remaining points proceed as follows. In each small square of \(\mathcal{H}_{n_{0}-1}\) match as many of the remaining points \(X_{i}\) to remaining points \(Y_{i}\) inside the same square. Remove all the points \(X_{i}\) and the points \(Y_{i}\) that you have removed at this stage and continue in this manner. Prove that the expected cost of the matching thus constructed is \(\leq L \sqrt{N} \log N .{ }^{9}\)
Let us state the "discrepancy bound" at the root of Theorem 4.5.1. Consider the class \(\mathcal{C}\) of 1 -Lipschitz functions on \([0,1]^{2}\), i.e. of functions \(f\) that satisfy
\[
\forall x, y \in[0,1]^{2},|f(x)-f(y)| \leq d(x, y)
\]
where \(d\) denotes the Euclidean distance. We denote by \(\lambda\) the uniform measure on \([0,1]^{2}\).

Theorem 4.5.3. We have
\[
\mathrm{E} \sup _{f \in \mathcal{C}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \leq L \sqrt{N \log N}
\]

\footnotetext{
\({ }^{9}\) It can be shown that this bound can be reversed.
}

Research problem 4.5.4. Prove that the following limit
\[
\lim _{N \rightarrow \infty} \frac{1}{\sqrt{N \log N}} \mathrm{E} \sup _{f \in \mathcal{C}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right|
\]
exists.
At the present time there does not seem to exist the beginning of a general approach for attacking a problem of this type, and certainly the methods of the present book are not appropriate for this. Quite amazingly, however, the corresponding problem has been solved in the case where the cost of the matching is measured by the square of the distance, see [6]. The methods seem rather specific to the case of the square of a distance.

Theorem 4.5.3 is obviously interesting in its own right, and proving it is the goal of this section. Before we discuss it, let us put matchings behind us.
Proof of Theorem 4.5.1. We recall (4.29), i.e.
\[
\inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right)=\sup _{f \in \mathcal{C}} \sum_{i \leq N}\left(f\left(X_{i}\right)-f\left(Y_{i}\right)\right),
\]
and we simply write
\[
\sum_{i \leq N}\left(f\left(X_{i}\right)-f\left(Y_{i}\right)\right) \leq\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f d \lambda\right)\right|+\left|\sum_{i \leq N}\left(f\left(Y_{i}\right)-\int f \mathrm{~d} \lambda\right)\right|
\]

Next, we claim that
\[
\left|\sum_{i \leq N}\left(f\left(Y_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \leq L \sqrt{N}
\]

We recall that since \(\left(Y_{i}\right)_{i \leq N}\) are evenly spread one can cover \([0,1]^{2}\) with \(N\) rectangles \(R_{i}\) with disjoint interiors, such that each rectangle \(R_{i}\) has an area \(1 / N\) and is such that \(Y_{i} \in R_{i} \subset B\left(Y_{i}, 10 / \sqrt{N}\right)\). Consequently \(N \int f \mathrm{~d} \lambda=\) \(N \sum_{i \leq N} \int_{R_{i}} f \mathrm{~d} \lambda\) and
\[
\begin{aligned}
\left|\sum_{i \leq N}\left(f\left(Y_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| & =\left|\sum_{i \leq N} f\left(Y_{i}\right)-N \int f \mathrm{~d} \lambda\right| \\
& \leq \sum_{i \leq N}\left|\left(f\left(Y_{i}\right)-N \int_{R_{i}} f \mathrm{~d} \lambda\right)\right| \\
& \leq \sum_{i \leq N} N\left|\int_{R_{i}}\left(f\left(Y_{i}\right)-f(x)\right) \mathrm{d} \lambda(x)\right|
\end{aligned}
\]

Since \(f\) is Lipschitz and \(R_{i}\) is of diameter \(\leq L / \sqrt{N}\) we have \(\left|f\left(Y_{i}\right)-f(x)\right| \leq\) \(L / \sqrt{N}\) when \(x \in R_{i}\). This proves the claim.

Now, using (4.39) and taking expectation
\[
\begin{aligned}
\mathrm{E} \inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) & \leq L \sqrt{N}+\mathrm{E} \sup _{f \in \mathcal{C}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \\
& \leq L \sqrt{N \log N}
\end{aligned}
\]
by (4.38).

\subsection*{4.5.1 The Long and Instructive Way}
S. Bobkov and M. Ledoux recently found [27] a magically simple proof of Theorem 4.5.3. We will present it in Subsection 4.5.2. This proof relies on very specific features, and it is unclear as to whether it will apply to other matching theorems. In the present section we write a far more pedestrian (but far more instructive) proof with the general result Theorem 6.8.3 in mind.

To prove Theorem 4.5.3 the overall strategy is clear. We think of the left-hand side as \(\operatorname{Esup}_{f \in \mathcal{C}}\left|Z_{f}\right|\), where \(Z_{f}\) is the random variable of (4.35). We then find nice tail properties for these r.v.s, and we use the methods of Chapter 2. In the end (and because we are dealing with a deep fact) we shall have to prove some delicate "smallness" property of the class \(\mathcal{C}\). This smallness property will ultimately be derived from the ellipsoid theorem. The (very beautiful) strategy for the hard part of the estimates relies on a kind of two-dimensional version of Proposition 4.1.8 and is outlined on page 121.

The class \(\mathcal{C}\) of 1 -Lipschitz function on the unit square is not small in any sense for the simple reason that it contains all the constant functions. However the expression \(\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\) does not change if we replace \(f\) by \(f+a\) where \(a\) is a constant. In particular
\[
\sup _{f \in \mathcal{C}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right|=\sup _{f \in \widehat{\mathcal{C}}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right|
\]
where we define \(\widehat{\mathcal{C}}\) as the set of 1-Lipschitz functions on the unit square for which \(f(1 / 2,1 / 2)=0 .{ }^{10}\) The gain is that we now may hope that \(\widehat{\mathcal{C}}\) is small in the appropriate sense. To prove Theorem 4.5.3, we will prove the following.

Theorem 4.5.5. We have
\[
\mathrm{E} \sup _{f \in \widehat{\mathcal{C}}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \leq L \sqrt{N \log N}
\]

The following fundamental classical result will allow us to control the tails of the r.v. \(Z_{f}\) of (4.35). It will be used many times.

\footnotetext{
\({ }^{10}\) There is no real reason other than my own fancy to impose that the functions are zero right in the middle of the square.
}

Lemma 4.5.6 (Bernstein's inequality). Let \(\left(W_{i}\right)_{i \geq 1}\) be independent r.v.s with \(\mathrm{E} W_{i}=0\) and consider a number a with \(\left|W_{i}\right| \leq a\) for each \(i\). Then, for \(v>0\),
\[
\mathrm{P}\left(\left|\sum_{i \geq 1} W_{i}\right| \geq v\right) \leq 2 \exp \left(-\min \left(\frac{v^{2}}{4 \sum_{i \geq 1} \mathrm{E} W_{i}^{2}}, \frac{v}{2 a}\right)\right)
\]

Proof. For \(|x| \leq 1\), we have
\[
\left|e^{x}-1-x\right| \leq x^{2} \sum_{k \geq 2} \frac{1}{k!}=x^{2}(e-2) \leq x^{2}
\]
and thus, since \(\mathrm{E} W_{i}=0\), for \(a|\lambda| \leq 1\), we have
\[
\left|\mathrm{E} \exp \lambda W_{i}-1\right| \leq \lambda^{2} \mathrm{E} W_{i}^{2}
\]

Therefore \(\mathrm{E} \exp \lambda W_{i} \leq 1+\lambda^{2} \mathrm{E} W_{i}^{2} \leq \exp \lambda^{2} \mathrm{E} W_{i}^{2}\), and thus
\[
\mathrm{E} \exp \lambda \sum_{i \geq 1} W_{i}=\prod_{i \geq 1} \mathrm{E} \exp \lambda W_{i} \leq \exp \lambda^{2} \sum_{i \geq 1} \mathrm{E} W_{i}^{2}
\]

Now, for \(0 \leq \lambda \leq 1 / a\) we have
\[
\begin{aligned}
\mathrm{P}\left(\sum_{i \geq 1} W_{i} \geq v\right) & \leq \exp (-\lambda v) \mathrm{E} \exp \lambda \sum_{i \geq 1} W_{i} \\
& \leq \exp \left(\lambda^{2} \sum_{i \geq 1} \mathrm{E} W_{i}^{2}-\lambda v\right)
\end{aligned}
\]

If \(a v \leq 2 \sum_{i \geq 1} \mathrm{E} W_{i}^{2}\), we take \(\lambda=v /\left(2 \sum_{i \geq 1} \mathrm{E} W_{i}^{2}\right)\), obtaining a bound \(\exp \left(-v^{2} /\left(4 \sum_{i \geq 1}^{-1} \mathrm{E} W_{i}^{2}\right)\right)\). If \(a v>2 \sum_{i \geq 1} \mathrm{E} W_{i}^{2}\), we take \(\lambda=1 / a\), and we note that
\[
\frac{1}{a^{2}} \sum_{i \geq 1} \mathrm{E} W_{i}^{2}-\frac{v}{a} \leq \frac{a v}{2 a^{2}}-\frac{v}{a}=-\frac{v}{2 a}
\]
so that \(\mathrm{P}\left(\sum_{i \geq 1} W_{i} \geq v\right) \leq \exp \left(-\min \left(v^{2} / 4 \sum_{i \geq 1} \mathrm{E} W_{i}^{2}, v / 2 a\right)\right)\). Changing \(W_{i}\) into \(-W_{i}\) we obtain the same bound for \(\mathrm{P}\left(\sum_{i \geq 1} W_{i} \leq-v\right)\).

Corollary 4.5.7. For each \(v>0\) we have
\[
\mathrm{P}\left(\left|Z_{f}\right| \geq v\right) \leq 2 \exp \left(-\min \left(\frac{v^{2}}{4 N\|f\|_{2}^{2}}, \frac{v}{4\|f\|_{\infty}}\right)\right)
\]
where \(\|f\|_{p}\) denotes the norm of \(f\) in \(L_{p}(\lambda)\).
Proof. We use Bernstein's inequality with \(W_{i}=f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\) if \(i \leq N\) and \(W_{i}=0\) if \(i>N\). We then observe that \(\mathrm{E} W_{i}^{2} \leq \mathrm{E} f^{2}=\|f\|_{2}^{2}\) and \(\left|W_{i}\right| \leq 2 \sup |f|=2\|f\|_{\infty}\).

Let us then pretend for a while that in (4.45) the bound was instead \(2 \exp \left(-v^{2} /\left(4 N\|f\|_{2}^{2}\right)\right)\). Thus we would be back to the problem we considered first, bounding the supremum of a stochastic process under the increment condition (2.4), where the distance on \(\mathcal{C}\) is given by \(d\left(f_{1}, f_{2}\right)=\sqrt{2 N} \| f_{1}-\) \(f_{2} \|_{2}\). The first thing to point out is that Theorem 4.5.3 is a prime example of a natural situation where using covering numbers does not yield the correct result, where we recall that for a metric space ( \(T, d\) ), the covering number \(N(T, d, \epsilon)\) denotes the smallest number of balls of radius \(\epsilon\) that are needed to cover \(T\). This is closely related to the fact that, as explained in Section 2.13, covering numbers do not describe well the size of ellipsoids. It is hard to formulate a theorem to the effect that covering numbers do not suffice, but the root of the problem is described in the next exercise, and a more precise version can be found later in Exercise 4.5.20.

Exercise 4.5.8. Prove that for each \(0<\epsilon \leq 1\)
\[
\log N\left(\widehat{\mathcal{C}}, d_{2}, \epsilon\right) \geq \frac{1}{L \epsilon^{2}}
\]
where \(d_{2}\) denotes the distance in \(L^{2}\left([0,1]^{2}\right)\). Hint: Consider an integer \(n \geq 0\), and divide \([0,1]^{2}\) into \(2^{2 n}\) equal squares of area \(2^{-2 n}\). For every such square \(C\) consider a number \(\epsilon_{C}= \pm 1\). Consider then the function \(f \in \mathcal{C}\) such that for \(x \in C\) one has \(f(x)=\epsilon_{C} d(x, B)\), where \(B\) denotes the boundary of \(C\). There are \(2^{2^{2 n}}\) such functions. Prove that by appropriate choices of the signs \(\epsilon_{C}\) one may find at least \(\exp \left(2^{2 n} / L\right)\) functions of this type which are at mutual distance \(\geq 2^{-n} / L\).

Since covering numbers do not suffice, we will appeal to the generic chaining, Theorem 2.7.2. As we will show later, in Exercise 4.5.21 we have \(\gamma_{2}\left(\widehat{\mathcal{C}}, d_{2}\right)=\) \(\infty\). To overcome this issue we will replace \(\widehat{\mathcal{C}}\) by a sufficiently large finite subset \(\mathcal{F} \subset \widehat{\mathcal{C}}\), for which we shall need the crucial estimate \(\gamma_{2}\left(\mathcal{F}, d_{2}\right) \leq L \sqrt{\log N}\). This will be done by proving that \(\gamma_{2,2}\left(\widehat{\mathcal{C}}, d_{2}\right)<\infty\) where \(\gamma_{2,2}\) is the functional of (4.5), so that \(\gamma_{2,2}\left(\mathcal{F}, d_{2}\right)<\infty\), and appealing to Lemma 4.1.3.

The main ingredient towards the control of \(\gamma_{2,2}\left(\widehat{\mathcal{C}}, d_{2}\right)\) is the following 2-dimensional version of Proposition 4.1.8.

Lemma 4.5.9. Consider the space \(\mathcal{C}^{*}\) of 1 -Lipschitz functions on \([0,1]^{2}\) which are zero on the boundary of \([0,1]^{2}\). Then \(\gamma_{2,2}\left(\mathcal{C}^{*}, d_{2}\right)<\infty\).

Proof. We represent \(\mathcal{C}^{*}\) as a subset of an ellipsoid using the Fourier transform. The Fourier transform associates to each function \(f\) on \(L^{2}\left([0,1]^{2}\right)\) the complex numbers \(c_{p, q}(f)\) given by
\[
c_{p, q}(f)=\iint_{[0,1]^{2}} f\left(x_{1}, x_{2}\right) \exp \left(2 i \pi\left(p x_{1}+q x_{2}\right)\right) \mathrm{d} x_{1} \mathrm{~d} x_{2}
\]

The Plancherel formula
\[
\|f\|_{2}=\left(\sum_{p, q \in \mathbb{Z}}\left|c_{p, q}(f)\right|^{2}\right)^{1 / 2}
\]
asserts that Fourier transform is an isometry, so that if
\[
\left.\mathcal{D}=\left\{\left(c_{p, q}(f)\right)_{p, q \in \mathbb{Z}} ; f \in \mathcal{C}^{*}\right)\right\}
\]
it suffices to show that \(\gamma_{2,2}(\mathcal{D}, d)<\infty\) where \(d\) is the distance in the complex Hilbert space \(\ell_{\mathbb{C}}^{2}(\mathbb{Z} \times \mathbb{Z})\). Using (4.47) and integration by parts we get
\[
-2 i \pi p c_{p, q}(f)=c_{p, q}\left(\frac{\partial f}{\partial x}\right)
\]

Using (4.48) for \(\partial f / \partial x\), and since \(\|\partial f / \partial x\|_{2} \leq 1\) we get \(\sum_{p, q \in \mathbb{Z}} p^{2}\left|c_{p, q}(f)\right|^{2}\) \(\leq 1 / 4 \pi^{2}\). Proceeding similarly for \(\partial f / \partial y\), we get
\[
\mathcal{D} \subset \mathcal{E}=\left\{\left(c_{p, q}\right) \in \ell_{\mathbb{C}}^{2}(\mathbb{Z} \times \mathbb{Z}) ;\left|c_{0,0}\right| \leq 1, \sum_{p, q \in \mathbb{Z}}\left(p^{2}+q^{2}\right)\left|c_{p, q}\right|^{2} \leq 1\right\}
\]

We view each complex number \(c_{p, q}\) as a pair ( \(x_{p, q}, y_{p, q}\) ) of real numbers, and \(\left|c_{p, q}\right|^{2}=x_{p, q}^{2}+y_{p, q}^{2}\), so that
\[
\begin{aligned}
\mathcal{E}= & \left\{\left(\left(x_{p, q}\right),\left(y_{p, q}\right)\right) \in \ell^{2}(\mathbb{Z} \times \mathbb{Z}) \times \ell^{2}(\mathbb{Z} \times \mathbb{Z})\right. \\
& \left.x_{0,0}^{2}+y_{0,0}^{2} \leq 1, \sum_{p, q \in \mathbb{Z}}\left(p^{2}+q^{2}\right)\left(x_{p, q}^{2}+y_{p, q}^{2}\right) \leq 1\right\}
\end{aligned}
\]

For \(u \geq 1\), we have
\[
\operatorname{card}\left\{(p, q) \in \mathbb{Z} \times \mathbb{Z} ; p^{2}+q^{2} \leq u^{2}\right\} \leq(2 u+1)^{2} \leq L u^{2}
\]

We then deduce from Corollary 4.1.7 that \(\gamma_{2,2}(\mathcal{E}, d)<\infty\).
Proposition 4.5.10. We have \(\gamma_{2,2}\left(\widehat{\mathcal{C}}, d_{2}\right)<\infty\).
I am grateful to R. van Handel who showed me the following simple arguments, which replaces pages of gritty work in [171]. The basic idea is to deduce this from Lemma 4.5.9, essentially by showing that \(\widehat{\mathcal{C}}\) is a Lipschitz image of a subset of \(\mathcal{C}^{*}\), or more exactly of the clone considered in the next lemma.

Lemma 4.5.11. The set \(\mathcal{C}^{\sharp}\) of 1 -Lipschitz functions on \([-1,2]^{2}\) which are zero on the boundary of this set satisfies \(\gamma_{2,2}\left(\mathcal{C}^{\sharp}, d^{\sharp}\right)<\infty\) where \(d^{\sharp}\) is the distance induced by \(L^{2}\left([-1,2]^{2}, \mathrm{~d} \lambda\right)\).

Proof. This should be obvious form Lemma 4.5.9, we just perform the same construction on two squares of different sizes, \([0,1]^{2}\) and \([-1,2]^{2}\).

Lemma 4.5.12. Each Lipschitz function \(f \in \widehat{\mathcal{C}}\) is the restriction to \([0,1]^{2}\) of a function \(f^{\sharp}\) of \(\mathcal{C}^{\sharp}\).

Proof. A function \(f \in \widehat{\mathcal{C}}\) may be extended to a 1 -Lipschitz function \(\tilde{f}\) on \(\mathbb{R}^{2}\) by the formula \(\tilde{f}(y)=\inf _{x \in[0,1]^{2}} f(x)+d(x, y)\). Since \(f(1 / 2,1 / 2)=0\) by definition of \(\widehat{\mathcal{C}}\) and since \(f\) is 1-Lipschitz, then \(|f(x)| \leq 1 / \sqrt{2} \leq 1\) for \(x \in[0,1]^{2}\). The function \(f^{\sharp}(y)=\min \left(\tilde{f}(y), d\left(y, \mathbb{R}^{2} \backslash[-1,2]^{2}\right)\right.\) ) is 1-Lipschitz. Since each point of \([0,1]^{2}\) is at distance \(\geq 1\) of \(\mathbb{R}^{2} \backslash[-1,2]^{2}\), \(f^{\sharp}\) coincides with \(f\) on \([0,1]^{2}\), and it is zero on the boundary of \([-1,2]^{2}\).

Proof of Proposition 4.5.10. To each function \(f\) of \(\mathcal{C}^{\sharp}\) we associate its restriction \(\varphi(f)\) to \([0,1]^{2}\). Since the map \(\varphi\) is a contraction, by Lemma 4.5.11 we have \(\gamma_{2,2}\left(\varphi\left(\mathcal{C}^{\sharp}\right)\right)<\infty\), and by Lemma 4.5.12 we have \(\widehat{\mathcal{C}} \subset \varphi\left(\mathcal{C}^{\sharp}\right)\).

Let us now come back to Earth and deal with the actual bound (4.45). For this we develop an appropriate version of Theorem 2.7.2. It will be used many times. The ease with which one deals with two distances is remarkable. The proof of the theorem contain a principle which will be used many times: if we have two admissible sequences of partitions such that for each of them, the sets of the partition as small in a certain sense, then we can construct an admissible sequence of partitions whose sets are small in both senses.

Theorem 4.5.13. Consider a set \(T\) provided with two distances \(d_{1}\) and \(d_{2}\). Consider a centered process \(\left(X_{t}\right)_{t \in T}\) which satisfies
\[
\begin{gathered}
\forall s, t \in T, \forall u>0 \\
\mathrm{P}\left(\left|X_{s}-X_{t}\right| \geq u\right) \leq 2 \exp \left(-\min \left(\frac{u^{2}}{d_{2}(s, t)^{2}}, \frac{u}{d_{1}(s, t)}\right)\right)
\end{gathered}
\]

Then
\[
\mathrm{E} \sup _{s, t \in T}\left|X_{s}-X_{t}\right| \leq L\left(\gamma_{1}\left(T, d_{1}\right)+\gamma_{2}\left(T, d_{2}\right)\right)
\]

This theorem will be applied when \(d_{1}\) is the \(\ell_{\infty}\) distance, but it sounds funny, when considering two distances, to call them \(d_{2}\) and \(d_{\infty}\).

Proof. We denote by \(\Delta_{j}(A)\) the diameter of the set \(A\) for \(d_{j}\). We consider an admissible sequence \(\left(\mathcal{B}_{n}\right)_{n \geq 0}\) such that \({ }^{11}\)
\[
\forall t \in T, \sum_{n \geq 0} 2^{n} \Delta_{1}\left(B_{n}(t)\right) \leq 2 \gamma_{1}\left(T, d_{1}\right)
\]
and an admissible sequence \(\left(\mathcal{C}_{n}\right)_{n \geq 0}\) such that
\[
\forall t \in T, \sum_{n \geq 0} 2^{n / 2} \Delta_{2}\left(C_{n}(t)\right) \leq 2 \gamma_{2}\left(T, d_{2}\right)
\]

\footnotetext{
\({ }^{11}\) The factor 2 in the right-hand side below is just in case the infimum over all partitions is not attained.
}

Here \(B_{n}(t)\) is the unique element of \(\mathcal{B}_{n}\) that contains \(t\) (etc.). We define partitions \(\mathcal{A}_{n}\) of \(T\) as follows. We set \(\mathcal{A}_{0}=\{T\}\), and, for \(n \geq 1\), we define \(\mathcal{A}_{n}\) as the partition generated by \(\mathcal{B}_{n-1}\) and \(\mathcal{C}_{n-1}\), i.e. the partition that consists of the sets \(B \cap C\) for \(B \in \mathcal{B}_{n-1}\) and \(C \in \mathcal{C}_{n-1}\). Thus card \(\mathcal{A}_{n} \leq N_{n-1}^{2} \leq N_{n}\), and the sequence ( \(\mathcal{A}_{n}\) ) is admissible. We then choose for each \(n \geq 0\) a set \(T_{n}\) such that card \(T_{n} \leq N_{n}\) which meets all the sets in \(\mathcal{A}_{n}\). It is convenient to reformulate (4.50) as follows: when \(u \geq 1\) we have
\[
\forall s, t \in T, \mathrm{P}\left(\left|X_{s}-X_{t}\right| \geq u^{2} d_{1}(s, t)+u d_{2}(s, t)\right) \leq 2 \exp \left(-u^{2}\right)
\]

We then copy the proof of (2.34), replacing (2.31) by
\[
\forall t,\left|X_{\pi_{n}(t)}-X_{\pi_{n-1}(t)}\right| \leq u^{2} 2^{n} d_{1}\left(\pi_{n}(t), \pi_{n-1}(t)\right)+u 2^{n / 2} d_{2}\left(\pi_{n}(t), \pi_{n-1}(t)\right) .
\]

Exercise 4.5.14. The purpose of this exercise is to deduce Theorem 4.5.13 from Theorem 2.7.14.
(a) Prove that if for some numbers \(A, B>0\) a r.v. \(Y \geq 0\) satisfies
\[
\mathrm{P}(Y \geq u) \leq 2 \exp \left(-\min \left(\frac{u^{2}}{A^{2}}, \frac{u}{B}\right)\right)
\]
then for \(p \geq 1\) we have \(\|Y\|_{p} \leq L(A \sqrt{p}+B p)\).
(b) We denote by \(D_{n}(A)\) the diameter of a subset \(A\) of \(T\) for the distance \(\delta_{n}(s, t)=\left\|X_{s}-X_{t}\right\|_{2^{n}}\). Prove that under the conditions of Theorem 4.5.13 there exists an admissible sequence of partitions ( \(\mathcal{A}_{n}\) ) such that
\[
\sup _{t \in T} \sum_{n \geq 0} D_{n}\left(A_{n}(t)\right) \leq L\left(\gamma_{1}\left(T, d_{1}\right)+\gamma_{2}\left(T, d_{2}\right)\right)
\]

Exercise 4.5.15. Consider a space \(T\) equipped with two different distances \(d_{1}\) and \(d_{2}\). Prove that
\[
\gamma_{2}\left(T, d_{1}+d_{2}\right) \leq L\left(\gamma_{2}\left(T, d_{1}\right)+\gamma_{2}\left(T, d_{2}\right)\right)
\]

We can now state a general bound, from which we will deduce Theorem 4.5.3.

Theorem 4.5.16. Consider a class \(\mathcal{F}\) of functions on \([0,1]^{2}\) and assume that \(0 \in \mathcal{F}\). Then
\[
\mathrm{E} \sup _{f \in \mathcal{F}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \leq L\left(\sqrt{N} \gamma_{2}\left(\mathcal{F}, d_{2}\right)+\gamma_{1}\left(\mathcal{F}, d_{\infty}\right)\right)
\]
where \(d_{2}\) and \(d_{\infty}\) are the distances induced on \(\mathcal{F}\) by the norms of \(L^{2}\) and \(L^{\infty}\) respectively.

Proof. Combining Corollary 4.5.7 with Theorem 4.5.13 we get, since \(0 \in \mathcal{F}\) and \(Z_{0}=0\),
\[
\mathrm{E} \sup _{f \in \mathcal{F}}\left|Z_{f}\right| \leq \mathrm{E} \sup _{f, f^{\prime} \in \mathcal{F}}\left|Z_{f}-Z_{f^{\prime}}\right| \leq L\left(\gamma_{2}\left(\mathcal{F}, 2 \sqrt{N} d_{2}\right)+\gamma_{1}\left(\mathcal{F}, 4 d_{\infty}\right)\right)
\]

Finally, \(\gamma_{2}\left(\mathcal{F}, 2 \sqrt{N} d_{2}\right)=2 \sqrt{N} \gamma_{2}\left(\mathcal{F}, d_{2}\right)\) and \(\gamma_{1}\left(\mathcal{F}, 4 d_{\infty}\right)=4 \gamma_{1}\left(\mathcal{F}, d_{\infty}\right)\).
Exercise 4.5.17. Try to prove (4.25) now. Hint: Consider \(\mathcal{F}=\left\{1_{[0, k / N]} ; k \leq\right.\) \(N\}\). Use Exercise 2.7.5 and entropy numbers.

In the situation which interests us, there will plenty of room to control the term \(\gamma_{1}\left(\mathcal{F}, d_{\infty}\right)\), and this term is a lower-order term, which can be considered as a simple nuisance. For this term, entropy numbers suffice. To control these, we first state a general principle, which was already known to Kolmogorov.

Lemma 4.5.18. Consider a metric space ( \(U, d\) ) and assume that for certain numbers \(B\) and \(\alpha \geq 1\) and each \(0<\epsilon<B\) we have
\[
N(U, d, \epsilon) \leq\left(\frac{B}{\epsilon}\right)^{\alpha}
\]

Consider the set \(\mathcal{B}\) of 1 -Lipschitz functions \(f\) on \(U\) with \(\|f\|_{\infty} \leq B\). Then for each \(\epsilon>0\) we have
\[
\log N\left(\mathcal{B}, d_{\infty}, \epsilon\right) \leq K(\alpha)\left(\frac{B}{\epsilon}\right)^{\alpha}
\]
where \(K(\alpha)\) depends only on \(\alpha\). In particular,
\[
e_{n}\left(\mathcal{B}, d_{\infty}\right) \leq K(\alpha) B 2^{-n / \alpha}
\]

Proof. By homogeneity we may and do assume that \(B=1\). Using (4.58) for \(\epsilon=2^{-n}\), for each \(n \geq 0\) consider a set \(V_{n} \subset U\) with card \(V_{n} \leq 2^{n \alpha}\) such that any point of \(U\) is within distance \(2^{-n}\) of a point of \(V_{n}\). We define on \(\mathcal{B}\) the distance \(d_{n}\) by \(d_{n}(f, g)=\max _{x \in V_{n}}|f(x)-g(x)|\). We prove first that
\[
d_{\infty}(f, g) \leq 2^{-n+1}+d_{n}(f, g)
\]

Indeed, for any \(x \in U\) we can find \(y \in V_{n}\) with \(d(x, y) \leq 2^{-n}\) and then \(|f(x)-g(x)| \leq 2^{-n+1}+|f(y)-g(y)| \leq 2^{-n+1}+d_{n}(f, g)\).

Denote by \(W_{n}(f, r)\) the ball for \(d_{n}\) of center \(f\) and radius \(r\). We claim that
\[
W_{n-1}\left(f, 2^{-n+1}\right) \subset W_{n}\left(f, 2^{-n+3}\right)
\]

Indeed, using (4.61) for \(n-1\) rather than \(n\) we see that \(d_{n}(f, g) \leq d_{\infty}(f, g) \leq\) \(2^{-n+2}+2^{-n+1} \leq 2^{-n+3}\) for \(g \in W_{n-1}\left(f, 2^{-n+1}\right)\).

Next, we claim that
\[
N\left(W_{n}\left(f, 2^{-n+3}\right), d_{n}, 2^{-n}\right) \leq L^{\operatorname{card} V_{n}}
\]

Since \(d_{n}(f, g)=\left\|\varphi_{n}(f)-\varphi_{n}(g)\right\|_{\infty}\) where \(\varphi_{n}(f)=(f(x))_{x \in V_{n}}\) we are actually working here in \(\mathbb{R}^{\text {card } V_{n}}\), and (4.63) is a consequence of (2.47): in \(\mathbb{R}^{\text {card } V_{n}}\) we are covering a ball of radius \(2^{-n+3}\) by balls of radius \(2^{-n}\).

Covering \(\mathcal{B}\) by \(N\left(\mathcal{B}, d_{n-1}, 2^{-n+1}\right)\) balls \(W_{n-1}\left(f, 2^{-n+1}\right)\) and hence by \(N\left(\mathcal{B}, d_{n-1}, 2^{-n+1}\right)\) balls \(W_{n}\left(f, 2^{-n+3}\right)\) and then covering each of these by \(N\left(W_{n}\left(f, 2^{-n+3}\right), d_{n}, 2^{-n}\right) \leq L^{\text {card } V_{n}}\) balls for \(d_{n}\) of radius \(2^{-n}\) we obtain
\[
N\left(\mathcal{B}, d_{n}, 2^{-n}\right) \leq L^{\operatorname{card} V_{n}} N\left(\mathcal{B}, d_{n-1}, 2^{-n+1}\right)
\]

Since \(\operatorname{card} V_{n}=2^{\alpha n}\), iteration of (4.64) proves that \(\log N\left(\mathcal{B}, d_{n}, 2^{-n}\right) \leq\) \(K 2^{\alpha n}\). Finally (4.61) implies that
\[
\log N\left(\mathcal{B}, d_{\infty}, 2^{-n+2}\right) \leq \log N\left(\mathcal{B}, d_{n-1}, 2^{-n-1}\right) \leq K 2^{\alpha n}
\]
and concludes the proof.
We apply the previous lemma to \(U=[0,1]^{2}\) which obviously satisfies (4.58) for \(\alpha=2\), so that (4.60) implies that for \(n \geq 0\),
\[
e_{n}\left(\hat{\mathcal{C}}, d_{\infty}\right) \leq L 2^{-n / 2}
\]

Proposition 4.5.19. We have
\[
\mathrm{E} \sup _{f \in \widehat{\mathcal{C}}}\left|\sum_{i \leq N} f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right| \leq L \sqrt{N \log N} .
\]

An interesting feature of this proof is that it does not work to try to use (4.56) directly. Rather we will use (4.56) for an appropriate subset \(T\) of \(\widehat{\mathcal{C}}\), which can be thought of as the "main part" of \(\widehat{\mathcal{C}}\), and for the "rest" of \(\widehat{\mathcal{C}}\) we will use other (and much cruder) bounds. This method is not artificial. As we will learn much later, in Theorem 6.8.3, when properly used, it always yields the best possible estimates.

Proof. Consider the largest integer \(m\) with \(2^{-m} \geq 1 / N\). By (4.65) we may find a subset \(T\) of \(\widehat{\mathcal{C}}\) with \(\operatorname{card} T \leq N_{m}\) and
\[
\forall f \in \widehat{\mathcal{C}}, d_{\infty}(f, T) \leq L 2^{-m / 2} \leq L / \sqrt{N}
\]

Thus for each \(f \in \widehat{\mathcal{C}}\) consider \(\tilde{f} \in T\) with \(d_{\infty}(f, \tilde{f}) \leq L / \sqrt{N}\). Then
\[
\left|Z_{f}\right| \leq\left|Z_{\tilde{f}}\right|+\left|Z_{f}-Z_{\tilde{f}}\right|=\left|Z_{\tilde{f}}\right|+\left|Z_{f-\tilde{f}}\right| \leq\left|Z_{\tilde{f}}\right|+L \sqrt{N},
\]
where we have used the obvious inequality \(\left|Z_{f-\tilde{f}}\right| \leq 2 d_{\infty}(f, \tilde{f})\). Since \(\tilde{f} \in T\) we obtain
\[
\mathrm{E} \sup _{f \in \widehat{\mathcal{C}}}\left|Z_{f}\right| \leq \mathrm{E} \sup _{f \in T}\left|Z_{f}\right|+L \sqrt{N}
\]

To prove (4.66) it suffices to show that
\[
\mathrm{E} \sup _{f \in T}\left|Z_{f}\right| \leq L \sqrt{N \log N}
\]

Proposition 4.5.10 and Lemma 4.1.3 imply \(\gamma_{2}\left(T, d_{2}\right) \leq L \sqrt{m} \leq L \sqrt{\log N}\). Now, as in (2.56) we have
\[
\gamma_{1}\left(T, d_{\infty}\right) \leq L \sum_{n \geq 0} 2^{n} e_{n}\left(T, d_{\infty}\right)
\]

Since \(e_{n}\left(T, d_{\infty}\right)=0\) for \(n \geq m\), (4.65) yields \(\gamma_{1}\left(T, d_{\infty}\right) \leq L 2^{m / 2} \leq L \sqrt{N}\). Thus (4.68) follows from Theorem 4.5.16 and this completes the proof.

Exercise 4.5.20. Use Exercise 4.5.8 to prove that Dudley's bound cannot yield better than the estimate \(\gamma_{2}\left(T, d_{2}\right) \leq L \log N\).

Exercise 4.5.21. Assuming \(\gamma_{2}\left(\mathcal{C}^{*}, d_{2}\right)<\infty\), show that the previous arguments prove that
\[
\mathrm{E} \sup _{f \in \mathcal{C}^{*}}\left|\sum_{i \leq N} f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right| \leq L \sqrt{N}\left(1+\gamma_{2}\left(\mathcal{C}^{*}, d_{2}\right)\right)
\]

Comparing with (4.78) conclude that \(\gamma_{2}\left(\mathcal{C}^{*}, d_{2}\right)=\infty\). Convince yourself that the separated trees implicitly constructed in the proof of (4.78) also witness this.

Exercise 4.5.22. Suppose now that you are in dimension \(d=3\). Prove that \(\operatorname{Esup}_{f \in \mathcal{C}}\left|\sum_{i \leq N} f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right| \leq L N^{2 / 3}\). Hint: according to Lemma 4.5.18 we have \(e_{n}\left(\widehat{\mathcal{C}}, d_{\infty}\right) \leq L 2^{-n / 3}\). This is the only estimate you need, using the trivial fact that \(e_{n}\left(\widehat{\mathcal{C}}, d_{2}\right) \leq e_{n}\left(\widehat{\mathcal{C}}, d_{\infty}\right)\).

Exercise 4.5.23. Consider the space \(T=\{0,1\}^{\mathbb{N}}\) provided with the distance \(d\left(t, t^{\prime}\right)=2^{-j / 2}\), where \(j=\min \left\{i \geq 1 ; t_{i} \neq t_{i}^{\prime}\right\}\) for \(t=\left(t_{i}\right)_{i \geq 1}\). This space somewhat resembles the unit square, in the sense that \(N(T, d, \epsilon) \leq L \epsilon^{-2}\) for \(\epsilon \leq 1\). Prove that if \(\left(X_{i}\right)_{i \leq N}\) are i.i.d. uniformly distributed in \(T\) and \(\left(Y_{i}\right)_{i \leq N}\) are uniformly spread (in a manner which is left to the reader to define precisely) then
\[
\mathrm{E} \inf _{\pi} \sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \leq L \sqrt{N} \log N
\]
where the infimum is over all the permutations of \(\{1, \ldots, N\}\). Hint: You can do this from scratch, and for this covering numbers suffice, e.g. in the form of (4.59). The method of Exercise 4.5.2 also works here. In Exercise 4.6.8 below, you will be asked to prove that this bound is of the correct order.

\subsection*{4.5.2 The Short and Magic Way}

We now start studying the Bobkov-Ledoux approach [27] which considerably simplifies previous results such as the following one.

Theorem 4.5.24. [[149]] Consider the class \(\mathcal{C}^{*}\) of 1 -Lipschitz functions on \([0,1]^{2}\) which are zero on the boundary of \([0,1]^{2}\). Consider points \(\left(z_{i}\right)_{i \leq N}\) in \([0,1]^{2}\) and standard independent Gaussian r.v.s \(g_{i}\). Then
\[
\mathrm{E} \sup _{f \in \mathcal{C}^{*}}\left|\sum_{i \leq N} g_{i} f\left(z_{i}\right)\right|^{2} \leq L N \log N
\]

It should be obvious from Lemma 4.5.12 that in the previous result one may replace \(\mathcal{C}^{*}\) by \(\widehat{\mathcal{C}}\) of Theorem 4.5.5. The following improves on Theorem 4.5.3.

Corollary 4.5.25. Consider an independent sequence \(\left(X_{i}\right)_{i \leq N}\) of r.v.s valued in \([0,1]^{2}\). (It is not assumed that these r.v.s have the same distribution.) Then
\[
\mathrm{E} \sup _{f \in \widehat{\mathcal{C}}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\mathrm{E} f\left(X_{i}\right)\right)\right| \leq L \sqrt{N \log N} .
\]

Proof. Consider i.i.d standard Gaussian r.v.s \(g_{i}\). Taking first expectation in the \(g_{i}\) given the \(X_{i}\), it follows from Theorem 4.5.24 (or more accurately from the version of this theorem for the class \(\widehat{\mathcal{C}}\) ) that \(\operatorname{Esup}_{f \in \widehat{\mathcal{C}}}\left|\sum_{i \leq N} g_{i} f\left(X_{i}\right)\right|^{2} \leq\) \(L N \log N\). The Cauchy-Schwarz inequality yields \(\mathrm{Esup}_{f \in \hat{\mathcal{C}}}\left|\sum_{i \leq N} g_{i} f\left(X_{i}\right)\right| \leq\) \(L \sqrt{N \log N}\). We will learn later the simple tools which allow to deduce (4.71) from this inequality, in particular the Giné-Zinn inequalities and specifically (11.35) (which has to be combined with (6.6)).

Let us consider an integer \(n \geq \sqrt{N}\) and the set
\[
G=\{(k / n, \ell / n) ; 0 \leq k, \ell \leq n-1\}
\]

Using the fact that the functions \(f \in \mathcal{C}^{*}\) are 1 -Lipschitz, and replacing each point \(z_{i}\) by the closest point in \(G\) (which is at distance \(\leq \sqrt{2} / n \leq L / \sqrt{N}\) of \(z\) ), the following is obvious.

Lemma 4.5.26. To prove Theorem 4.5.24 we may assume that each \(z_{i} \in G\).
Let us define an ellipsoid \(\mathcal{E}\) in \(\mathbb{R}^{N}\) as a set \(\mathcal{E}=\left\{\sum_{k \geq 1} \alpha_{k} u_{k}\right\}\) where \(\left(u_{k}\right)_{k \geq 1}\) is a given sequence in \(\mathbb{R}^{N}\) and where \(\left(\alpha_{k}\right)_{k \geq 1}\) varies over all the possible sequences with \(\sum_{k \geq 1} \alpha_{k}^{2} \leq 1 .{ }^{12}\) For \(t \in \mathbb{R}^{N}\) we write \(X_{t}=\sum_{i \leq N} t_{i} g_{i}\) as usual.

\footnotetext{
\({ }^{12}\) The name is justified, a bit a of algebra allows one to show that such a set is an ellipsoid in the usual sense, but we do not need that.
}

Lemma 4.5.27. We have
\[
\mathrm{E} \sup _{t \in \mathcal{E}}\left|X_{t}\right|^{2} \leq \sum_{k \geq 1}\left\|u_{k}\right\|^{2}
\]

Proof. This is exactly the same argument as to prove (2.157). For \(t=\) \(\sum_{k \geq 1} \alpha_{k} u_{k} \in \mathcal{E}\) we have \(X_{t}=\sum_{k \geq 1} \alpha_{k} X_{u_{k}}\) so that by the Cauchy-Schwarz inequality we have \(\left|X_{t}\right|^{2} \leq \sum_{k \geq 1}\left|X_{u_{k}}\right|^{2}\) and the result follows from taking the supremum in \(t\) and expectation since \(\mathrm{E}\left|X_{u_{k}}\right|^{2}=\left\|u_{k}\right\|^{2}\).

To prove Theorem 4.5.24 we will show that the set \(\left\{\left(f\left(z_{i}\right)\right)_{i \leq N} ; f \in \mathcal{C}^{*}\right\}\) is a subset of an appropriate ellipsoid.

For this we identify \(G\) with the group \(\mathbb{Z}_{n} \times \mathbb{Z}_{n}\) where \(\mathbb{Z}_{n}=\mathbb{Z} / n \mathbb{Z}\), with the idea to use Fourier analysis in \(G\), keeping in mind that a function on \([0,1]^{2}\) which is zero on the boundary of this set will give rise to a function on \(\mathbb{Z}_{n} \times \mathbb{Z}_{n}\). Consider the elements \(\tau_{1}=(1,0)\) and \(\tau_{2}=(0,1)\) of \(G\). For a function \(f: G \rightarrow \mathbb{R}\) we define the functions \(f_{1}(\tau)=f\left(\tau+\tau_{1}\right)-f(\tau)\) and \(f_{2}(\tau)=f\left(\tau+\tau_{2}\right)-f(\tau)\), and the class \(\tilde{\mathcal{C}}\) of functions \(G \rightarrow \mathbb{R}\) which satisfy
\[
\forall \tau \in G,|f(\tau)| \leq 1 ; \forall \tau \in G ;\left|f_{1}(\tau)\right| \leq 1 / n ;\left|f_{2}(\tau)\right| \leq 1 / n
\]

Thus, seeing the functions on \(\mathcal{C}^{*}\) as functions on \(G\), they belong to \(\tilde{\mathcal{C}}\). Let us denote by \(\widehat{G}\) the set of characters \(\chi\) on \(G .^{13}\) The Fourier transform \(\hat{f}\) of a function \(f\) on \(G\) is the function \(\hat{f}\) on \(\widehat{G}\) given by \(\hat{f}(\chi)=\) \((\operatorname{card} G)^{-1} \sum_{\tau \in G} f(\tau) \bar{\chi}(\tau)\) where we recall that \(|\chi(\tau)|=1\). One then has the Fourier expansion
\[
f=\sum_{\chi \in \widehat{G}} \hat{f}(\chi) \chi
\]
and the Plancherel formula
\[
\frac{1}{\operatorname{card} G} \sum_{\tau \in G}|f(\tau)|^{2}=\sum_{\chi \in \widehat{G}}|\hat{f}(\chi)|^{2}
\]

The key to the argument is the following.
Proposition 4.5.28. There exist positive numbers \((c(\chi))_{\chi \in \widehat{G}}\) such that
\[
\sum_{\chi \in \widehat{G}} \frac{1}{c(\chi)} \leq L \log n
\]
and
\[
\forall f \in \tilde{\mathcal{C}}, \sum_{\chi \in \widehat{G}} c(\chi)|\widehat{f}(\chi)|^{2} \leq 1
\]

\footnotetext{
\({ }^{13}\) A character of a group \(G\) is a group homomorphism from \(G\) to the unit circle.
}

We start the preparations for the proof of Proposition 4.5.28. The following lemma performs integration by parts.

Lemma 4.5.29. For each function \(f\) on \(G\) and every \(\chi \in \widehat{G}\) we have \(\hat{f}_{1}(\chi)=\) \(\left(\chi\left(-\tau_{1}\right)-1\right) \hat{f}(\chi)\).

Proof. Since \(\sum_{\tau \in G} f\left(\tau+\tau_{1}\right) \chi(\tau)=\sum_{\tau \in G} f(\tau) \chi\left(\tau-\tau_{1}\right)\) by change of variable, we have
\[
\begin{aligned}
(\operatorname{card} G) \hat{f}_{1}(\chi) & =\sum_{\tau \in G}\left(f\left(\tau+\tau_{1}\right)-f(\tau)\right) \chi(\tau)=\sum_{\tau \in G} f(\tau)\left(\chi\left(\tau-\tau_{1}\right)-\chi(\tau)\right) \\
& =\left(\chi\left(\tau_{1}\right)-1\right) \sum_{\tau \in G} f(\tau) \chi(\tau)=\left(\chi\left(\tau_{1}\right)-1\right)(\operatorname{card} G) \hat{f}(\chi)
\end{aligned}
\]
where we have used in the third equality that \(\chi\left(\tau-\tau_{1}\right)=\chi(\tau) \chi\left(-\tau_{1}\right)\).
Corollary 4.5.30. For \(f \in \tilde{\mathcal{C}}\) we have
\[
\sum_{\chi \in \widehat{G}}\left(\left|\chi\left(-\tau_{1}\right)-1\right|^{2}+\left|\chi\left(-\tau_{2}\right)-1\right|^{2}\right)|\hat{f}(\chi)|^{2} \leq \frac{2}{n^{2}}
\]

Proof. Using Lemma 4.5.29 and then the Plancherel formula (4.74) and (4.72) we obtain
\[
\sum_{\chi \in \widehat{G}}\left|\chi\left(-\tau_{1}\right)-1\right|^{2}|\hat{f}(\chi)|^{2}=\sum_{\chi \in \widehat{G}}\left|\hat{f}_{1}(\chi)\right|^{2}=\frac{1}{\operatorname{card} G} \sum_{\tau \in G}\left|f_{1}(\tau)\right|^{2} \leq \frac{1}{n^{2}}
\]
and we proceed similarly for \(\tau_{2}\).
Proof of Proposition 4.5.28. For \(\chi \in \widehat{G}\) let us set \(c(\chi)=1 / 2\) if \(\chi\) is constant character \(\chi_{0}\) equal to 1 , and otherwise
\[
c(\chi)=\frac{n^{2}}{4}\left(\left|\chi\left(-\tau_{1}\right)-1\right|^{2}+\left|\chi\left(-\tau_{2}\right)-1\right|^{2}\right)
\]

Then, since \(\left|\hat{f}\left(\chi_{0}\right)\right| \leq 1\) because \(|f(\tau)| \leq 1\) for each \(\tau\), and using (4.77) in the second inequality,
\[
\sum_{\chi \in \hat{G}} c(\chi)|\widehat{f}(\chi)|^{2}=\frac{1}{2}\left|\hat{f}\left(\chi_{0}\right)\right|^{2}+\sum_{\chi \in \hat{G}, \chi \neq \chi_{0}} c(\chi)|\hat{f}(\chi)|^{2} \leq \frac{1}{2}+\frac{n^{2}}{4} \frac{2}{n^{2}} \leq 1,
\]
and this proves (4.76). To prove (4.75) we use that \(\widehat{G}\) is exactly the set of characters of the type \(\chi_{p, q}(a, b)=\exp (2 \mathrm{i} \pi(a p+b q) / n)\) where \(0 \leq p, q \leq n-1\). Thus \(\chi_{p, q}\left(-\tau_{1}\right)=\exp (-2 \mathrm{i} \pi p / n)\) and \(\chi_{p, q}\left(-\tau_{2}\right)=\exp (-2 \mathrm{i} \pi q / n)\). Now, for \(0 \leq x \leq 1\) we have \(|1-\exp (-2 \mathrm{i} \pi x)| \geq \min (x, 1-x)\) so that
\[
\left|\chi_{p, q}\left(-\tau_{1}\right)-1\right| \geq \frac{1}{L n} \min (p, n-p) ;\left|\chi_{p, q}\left(-\tau_{2}\right)-1\right| \geq \frac{1}{L n} \min (q, n-q)
\]

Thus
\[
\sum_{\chi \in \widehat{G}} \frac{1}{c(\chi)} \leq \frac{1}{c\left(\chi_{0}\right)}+L \sum \frac{1}{\min (p, n-p)^{2}+\min (q, n-q)^{2}}
\]
where the sum is over \(0 \leq p, q \leq n-1\) and \((p, q) \neq(0,0)\). Distinguishing whether \(p \leq n / 2\) or \(p \geq n / 2\) (and similarly for \(q\) ) we obtain
\[
\sum \frac{1}{\min (p, n-p)^{2}+\min (q, n-q)^{2}} \leq 4 \sum \frac{1}{p^{2}+q^{2}}
\]
where the sum is over the same set and this sum is \(\leq L \log n\).
Proof of Theorem 4.5.24. We write (4.73) as \(f=\sum_{\chi \in \widehat{G}} \alpha_{\chi} \chi / \sqrt{c(\chi)}\) where \(\alpha_{\chi}=\sqrt{c(\chi)} \hat{f}(\chi)\) so that \(\sum_{\chi \in \widehat{G}}\left|\alpha_{\chi}\right|^{2} \leq 1\) by (4.76). Now we come back to real numbers by taking the real part of the identity \(f=\sum_{\chi \in \widehat{G}} \alpha_{\chi} \chi / \sqrt{c(\chi)}\). This gives an equality of the type \(f=\sum_{\chi \in \widehat{G}}\left(\alpha_{\chi}^{\prime} \chi^{\prime}+\beta_{\chi}^{\prime} \chi^{\prime \prime}\right) / \sqrt{c(\chi)}\) where \(\sum_{\chi \in \widehat{G}}\left(\left(\alpha_{\chi}^{\prime}\right)^{2}+\left(\beta_{\chi}^{\prime}\right)^{2}\right) \leq 1\) and \(\left|\chi^{\prime}\right|,\left|\chi^{\prime \prime}\right| \leq 1\). That is, the set \(\left\{\left(f\left(z_{i}\right)\right)_{i \leq N} ; f \in\right.\) \(\left.\mathcal{C}^{*}\right\}\) is a subset of the ellipsoid \(\mathcal{E}=\left\{\sum_{k \geq 1} \alpha_{k} u_{k} ; \sum_{k} \alpha_{k}^{2} \leq 1\right\}\), where the family \(\left(u_{k}\right)\) of points of \(\mathbb{R}^{N}\) consists of the points \(\left(\chi^{\prime}\left(z_{i}\right) / \sqrt{c(\chi)}\right)_{i \leq N}\) and \(\left(\chi^{\prime \prime}\left(z_{i}\right) / \sqrt{c(\chi)}\right)_{i \leq N}\) where \(\chi\) takes all possible values in \(\widehat{G}\). For such a \(u_{k}\) we have \(\left|u_{k}\left(z_{i}\right)\right| \leq 1 / \sqrt{c(\chi)}\) so that \(\left\|u_{k}\right\|^{2}=\sum_{i \leq N} u_{k}\left(z_{i}\right)^{2} \leq N / c(\chi)\), and then by (4.75) we have \(\sum_{k}\left\|u_{k}\right\|^{2} \leq L N \log n\). Finally we apply Lemma 4.5.27, and we take for \(n\) the smallest integer \(\geq \sqrt{N}\).

Exercise 4.5.31. Let us denote by \(\nu\) the uniform measure on \(G\) and by \(d_{\nu}\) the distance in the space \(L^{2}(\nu)\). Prove that \(\gamma_{2}\left(\mathcal{C}^{*}, d_{\nu}\right) \geq \sqrt{\log n} / L\). Warning: this is not so easy, and the solution is not provided. Hint: make sure you understand the previous chapter, an construct an appropriate tree. The ingredients on how to build that tree are contained in the proof given in the next section, and Section 3.2 should also be useful. You may assume that \(N\) is a power of 2 to save technical work. Furthermore, you may also look at [171] where trees were explicitly used.

\subsection*{4.6 Lower Bound for the Ajtai-Komlós-Tusnády Theorem}

Recall that \(\mathcal{C}^{*}\) denotes the class of 1-Lipschitz functions on the unit square which are zero on the boundary of the square. We shall prove the following, where \(\left(X_{i}\right)_{i \leq N}\) are i.i.d. in \([0,1]^{2}\).

Theorem 4.6.1. We have
\[
\mathrm{E} \sup _{f \in \mathcal{C}^{*}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \geq \frac{1}{L} \sqrt{N \log N}
\]

Since
\[
\begin{aligned}
\sup _{f \in \mathcal{C}^{*}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \leq \sup _{f \in \mathcal{C}^{*}} \mid \sum_{i \leq N}( & \left.f\left(X_{i}\right)-f\left(Y_{i}\right)\right) \mid \\
& +\sup _{f \in \mathcal{C}^{*}}\left|\sum_{i \leq N}\left(f\left(Y_{i}\right)-\int f \mathrm{~d} \lambda\right)\right|
\end{aligned}
\]
taking expectation and using (4.41) it follows from (4.78) that if the points \(Y_{i}\) are evenly spread then (provided \(N \geq L\) ),
\[
\mathrm{E} \sup _{f \in \mathcal{C}^{*}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-f\left(Y_{i}\right)\right)\right| \geq \frac{1}{L} \sqrt{N \log N}
\]
and since \(\mathcal{C}^{*} \subset \mathcal{C}\) the duality formula (4.29) implies that the expected cost of matching the points \(X_{i}\) and the points \(Y_{i}\) is at least \(\sqrt{N \log N} / L\).

The proof of Theorem 4.6.1 occupies this entire section and starts now. The beautiful argument we present goes back to [5]. We can expect that this proof is non-trivial. To explain why, let us recall the set \(T\) used in the proof of Proposition 4.5.19. Analysis of the proof of that proposition leads us to guess that the reason why the bound it provides cannot be improved is that \(\gamma_{2}\left(T, d_{2}\right)\) is actually of order \(\sqrt{\log N}\) (and not of a smaller order). So a proof of (4.78) must contain a proof that this is the case. In the previous chapter, we learned a technique to prove such results, the construction of "trees". Not surprisingly, our proof implicitly uses a tree which witnesses just this, somewhat similar to the tree we constructed in Section 3.2. \({ }^{14}\)

We may assume \(N\) large and we consider a number \(r \in \mathbb{N}\) which is a small proportion of \(\log N\), say \(r \simeq(\log N) / 100 .{ }^{15}\) The structure of the proof is to recursively construct for \(k \leq r\) certain (random) functions \(f_{k}\) such that for any \(q \leq r\)
\[
\sum_{k \leq q} f_{k} \text { is } 1 \text {-Lipschitz }
\]
and for each \(k \leq r\),
\[
\mathrm{E} \sum_{i \leq N}\left(f_{k}\left(X_{i}\right)-\int f_{k} \mathrm{~d} \lambda\right) \geq \frac{\sqrt{N}}{L \sqrt{r}}
\]

\footnotetext{
\({ }^{14}\) It should also be useful to solve Exercise 4.5.31 above.
\({ }^{15}\) We absolutely need for the proof a number \(r\) which is a proportion of \(\log N\), and taking a small proportion gives us some room. More specifically, each square of side \(2^{-r}\) will have an area larger than, say, \(1 / \sqrt{N}\), so that it will typically contain many points \(X_{i}\), as we will use when we perform normal approximation.
}

The function \(f^{*}=\sum_{k \leq r} f_{k}\) is then 1-Lipschitz and satisfies
\[
\mathrm{E} \sum_{i \leq N}\left(f^{*}\left(X_{i}\right)-\int f^{*} \mathrm{~d} \lambda\right) \geq \frac{\sqrt{N r}}{L}
\]

This completes the proof of (4.78). The function \(f_{k}\) looks to what happens at scale \(2^{-k}\), and (4.80) states that each such scale \(2^{-k}\) contributes about equally to the final result.

Following the details of the construction is not that difficult, despite the fact that ensuring (4.79) requires technical work. What is more difficult is to see why one makes such choices as we do. There is no magic there, making the right choices means that we have understood which aspect of the geometric complexity of the class \(\mathcal{C}^{*}\) is relevant here.

The main idea behind the construction of the function \(f_{k}\) is that if we divide \([0,1]^{2}\) into little squares of side \(2^{-k}\), in each of these little squares there is some irregularity of the distribution of the \(X_{i}\). The function \(f_{k}\) is a sum of terms, each corresponding to one of the little squares, see (4.90). It is designed to, in a sense, add the irregularities over these different squares.

The functions \(f_{k}\) will be built out of simple functions which we describe now. For \(1 \leq k \leq r\) and \(1 \leq \ell \leq 2^{k}\) we consider the function \(f_{k, \ell}^{\prime}\) on \([0,1]\) defined as follows:
\[
f_{k, \ell}^{\prime}(x)= \begin{cases}0 & \text { unless } x \in\left[(\ell-1) 2^{-k}, \ell 2^{-k}[ \right. \\ 1 & \text { for } x \in\left[(\ell-1) 2^{-k},(\ell-1 / 2) 2^{-k}[ \right. \\ -1 & \text { for } x \in\left[(\ell-1 / 2) 2^{-k}, \ell 2^{-k}[ \right.\end{cases}
\]

We define
\[
f_{k, \ell}(x)=\int_{0}^{x} f_{k, \ell}^{\prime}(y) \mathrm{d} y
\]
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-144.jpg?height=250&width=800&top_left_y=1672&top_left_x=500)

Fig. 4.1. The graphs of \(f_{k, 1}\) and \(f_{k, 3}\).
We now list a few useful properties of these functions. In these formulas \(\|.\|_{2}\) denotes the norm in \(L^{2}([0,1])\), etc. The proofs of these assertions are completely straightforward and better left to the reader.

Lemma 4.6.2. The following holds true:
The family \(\left(f_{k, \ell}^{\prime}\right)\) is orthogonal in \(L^{2}([0,1])\).
\[
\begin{gathered}
\left\|f_{k, \ell}^{\prime}\right\|_{2}^{2}=2^{-k} \\
\left\|f_{k, \ell}^{\prime}\right\|_{1}=2^{-k} \\
\left.f_{k, \ell} \text { is zero outside }\right](\ell-1) 2^{-k}, \ell 2^{-k}[ \\
\left\|f_{k, \ell}\right\|_{1}=2^{-2 k-2} \\
\left\|f_{k, \ell}^{\prime}\right\|_{\infty}=1 ;\left\|f_{k, \ell}\right\|_{\infty}=2^{-k-1} \\
\left\|f_{k, \ell}\right\|_{2}^{2}=\frac{1}{12} 2^{-3 k}
\end{gathered}
\]

The functions \(f_{k}\) will be of the type
\[
f_{k}=\frac{2^{k-5}}{\sqrt{r}} \sum_{\ell, \ell^{\prime} \leq 2^{k}} z_{k, \ell, \ell^{\prime}} f_{k, \ell} \otimes f_{k, \ell^{\prime}}
\]
where \(f_{k, \ell} \otimes f_{k, \ell^{\prime}}(x, y)=f_{k, \ell}(x) f_{k, \ell^{\prime}}(y)\) and \(z_{k, \ell, \ell^{\prime}} \in\{0,1,-1\}\). Note that \(f_{k, \ell} \otimes f_{k, \ell^{\prime}}\) is zero outside the little square \(\left[(\ell-1) 2^{-k}, \ell 2^{-k}\left[\times\left[\left(\ell^{\prime}-\right.\right.\right.\right.\) 1) \(2^{-k}, \ell^{\prime} 2^{-k}\) [, and that these little squares are disjoint as \(\ell\) and \(\ell^{\prime}\) vary. The term \(z_{k, \ell, \ell^{\prime}} f_{k, \ell} \otimes f_{k, \ell^{\prime}}\) is designed to take advantage of the irregularity of the distribution of the \(X_{i}\) in the corresponding little square. The problem of course is to choose the numbers \(z_{k, \ell, \ell^{\prime}}\). There are two different ideas here. First, as a technical device to ensure (4.79), \(z_{k, \ell, \ell^{\prime}}\) may be set to zero. This will happen on a few little squares, those where are getting dangerously close to violate this condition (4.79). The second idea is that we will adjust the signs of \(z_{k, \ell, \ell^{\prime}}\) in a way that the contributions of the different little squares add properly (rather than canceling each other).

Let us now explain the scaling term \(2^{k-5} / \sqrt{r}\) in (4.90). The coefficient \(2^{-5}\) is just a small numerical constant ensuring that we have enough room. The idea of the term \(2^{k} / \sqrt{r}\) is that the partial derivatives of \(f_{k}\) will be of order \(1 / \sqrt{r}\). Taking a sum of a most \(r\) such terms and taking cancellations in effect will give us partial derivatives which, at most of the points are \(\leq 1\). This is formally expressed in the next lemma. So, such sums are not necessarily 1-Lipschitz, but are pretty close to being so and some minor tweaking will ensure that they are.

Lemma 4.6.3. Consider \(q \leq r\). Consider a function of the type \(f=\) \(\sum_{k \leq q} f_{k}\), where \(f_{k}\) is given by (4.90) and where \(z_{k, \ell, \ell^{\prime}} \in\{0,1,-1\}\). Then
\[
\left\|\frac{\partial f}{\partial x}\right\|_{2} \leq 2^{-6}
\]

Proof. First we note that
\[
\frac{\partial f}{\partial x}(x, y)=\sum_{k \leq q} \frac{2^{k-5}}{\sqrt{r}} \sum_{\ell, \ell^{\prime} \leq 2^{k}} z_{k, \ell, \ell^{\prime}} f_{k, \ell}^{\prime}(x) f_{k, \ell^{\prime}}(y)
\]
which we rewrite as
\[
\frac{\partial f}{\partial x}(x, y)=\sum_{k \leq q} \frac{2^{k-5}}{\sqrt{r}} \sum_{\ell \leq 2^{k}} a_{k, \ell}(y) f_{k, \ell}^{\prime}(x)
\]
where \(a_{k, \ell}(y)=\sum_{\ell^{\prime} \leq 2^{k}} z_{k, \ell, \ell^{\prime}} f_{k, \ell^{\prime}}(y)\). Using (4.83) we obtain
\[
\int\left(\frac{\partial f}{\partial x}\right)^{2} \mathrm{~d} x=\sum_{k \leq q} \frac{2^{2 k-10}}{r} \sum_{\ell \leq 2^{k}} a_{k, \ell}(y)^{2}\left\|f_{k, \ell}^{\prime}\right\|_{2}^{2}
\]

Since \(z_{k, \ell, \ell^{\prime}}^{2} \leq 1\), and since the functions \(\left(f_{k, \ell}\right)_{\ell \leq 2^{k}}\) have disjoint support, we have \(a_{k, \ell}(y)^{2} \leq \sum_{\ell^{\prime} \leq 2^{k}} f_{k, \ell^{\prime}}(y)^{2}\), so that
\[
\int\left(\frac{\partial f}{\partial x}\right)^{2} \mathrm{~d} x \leq \sum_{k \leq q} \frac{2^{2 k-10}}{r} \sum_{\ell, \ell^{\prime} \leq 2^{k}}\left\|f_{k, \ell}^{\prime}\right\|_{2}^{2} f_{k, \ell^{\prime}}(y)^{2}
\]

Integrating in \(y\) and using (4.84) and (4.89) yields
\[
\left\|\frac{\partial f}{\partial x}\right\|_{2}^{2} \leq \sum_{k \leq q} \frac{2^{2 k-10}}{r} \frac{2^{-2 k}}{12}=\frac{q}{r} \frac{2^{-10}}{12} \leq 2^{-12}
\]

Naturally, we have the same bound for \(\|\partial f / \partial y\|_{2}\). These bounds do not imply that \(f\) is 1 -Lipschitz, but they imply that it is 1 -Lipschitz "most of the time".

We construct the functions \(f_{k}\) recursively. Having constructed \(f_{1}, \ldots, f_{q}\), let \(f:=\sum_{k \leq q} f_{k}\), and assume that it is 1 -Lipschitz. We will construct \(f_{q+1}\) of the type ( \(4 . \overline{9} 0\) ) by choosing the coefficients \(z_{q+1, \ell, \ell^{\prime}}\). Let us say that a square of the type
\[
\left[(\ell-1) 2^{-q}, \ell 2^{-q}\left[\times\left[\left(\ell^{\prime}-1\right) 2^{-q}, \ell^{\prime} 2^{-q}[\right.\right.\right.
\]
for \(1 \leq \ell, \ell^{\prime} \leq 2^{q}\) is a \(q\)-square. There are \(2^{2 q}\) such \(q\)-squares.
Definition 4.6.4. We say that a \((q+1)\)-square is dangerous if it contains a point for which either \(|\partial f / \partial x| \geq 1 / 2\) or \(|\partial f / \partial y| \geq 1 / 2\). We say that it is safe if it is not dangerous.

The danger is that on this square (4.93) the function \(f+f_{q+1}\) might not be 1-Lipschitz.

Lemma 4.6.5. At most half of the ( \(q+1\) )-squares are dangerous, so at least half of the ( \(q+1\) )-squares are safe.

This lemma is a consequence of the fact that " \(f\) is 1 -Lipschitz most of the time." The proof is a bit technical, so we delay it to the end of the section.

The following is also a bit technical, but is certainly expected. It will also be proved later.

Lemma 4.6.6. If \(z_{q+1, \ell, \ell^{\prime}}=0\) whenever the corresponding \((q+1)\)-square (4.93) is dangerous, then \(f+f_{q+1}\) is 1 -Lipschitz.

We now complete the construction of the function \(f_{q+1}\). For a dangerous square we set \(z_{q+1, \ell, \ell^{\prime}}=0\). Let us define
\[
h_{\ell, \ell^{\prime}}(x)=f_{q+1, \ell} \otimes f_{q+1, \ell^{\prime}}(x)-\int f_{q+1, \ell} \otimes f_{q+1, \ell^{\prime}} \mathrm{d} \lambda
\]

Using (4.89) and (4.87) we obtain
\[
\left\|h_{\ell, \ell^{\prime}}\right\|_{2}^{2} \geq 2^{-6 q} / L
\]

Let us define then
\[
D_{\ell, \ell^{\prime}}=\sum_{i \leq N} h_{\ell, \ell^{\prime}}\left(X_{i}\right)
\]

For a safe square, we choose \(z_{q+1, \ell, \ell^{\prime}}= \pm 1\) such that
\[
z_{q+1, \ell, \ell^{\prime}} D_{\ell, \ell^{\prime}}=\left|D_{\ell, \ell^{\prime}}\right|
\]

Thus, if
\[
f_{q+1}=\frac{2^{q-4}}{\sqrt{r}} \sum_{\ell, \ell^{\prime} \leq 2^{q+1}} z_{q+1, \ell, \ell^{\prime}} f_{q+1, \ell} \otimes f_{q+1, \ell^{\prime}}
\]
we have
\[
\sum_{i \leq N}\left(f_{q+1}\left(X_{i}\right)-\int f_{q+1} \mathrm{~d} \lambda\right)=\frac{2^{q-4}}{\sqrt{r}} \sum_{\text {safe }}\left|D_{\ell, \ell^{\prime}}\right|
\]
where the sum is over all values of \(\left(\ell, \ell^{\prime}\right)\) such that the corresponding square (4.93) is safe.

We turn to the proof of (4.80) and for this we estimate \(\mathrm{E} \sum_{\text {safe }}\left|D_{\ell, \ell^{\prime}}\right|\). An obvious obstacle to perform this estimate is that the r.v.s \(D_{\ell, \ell^{\prime}}\) are not independent of the set of safe squares. But we know that at least half of the squares are safe, so we can bound below \(\sum_{\text {safe }}\left|D_{\ell, \ell^{\prime}}\right|\) by the sum of the \(2^{2 q+1}\) smallest among the \(2^{2 q+2}\) r.v.s \(\left|D_{\ell, \ell^{\prime}}\right|\).

Let us estimate \(\mathrm{E} D_{\ell, \ell^{\prime}}^{2}\). By definition (4.95) \(D_{\ell, \ell^{\prime}}\) is a sum \(\sum_{i \leq N} h_{\ell, \ell^{\prime}}\left(X_{i}\right)\) of independent centered r.v.s so that \(\mathrm{E} D_{\ell, \ell^{\prime}}^{2}=N\left\|h_{\ell, \ell^{\prime}}\right\|_{2}^{2}\), and using (4.94) we obtain an estimate
\[
\mathrm{E} D_{\ell, \ell^{\prime}}^{2} \geq 2^{-6 q} N / L
\]

Let us then pretend for a moment that the r.v.s \(D_{\ell, \ell^{\prime}}\) are Gaussian and independent as \(\ell, \ell^{\prime}\) vary. For a Gaussian r.v. \(g\) we have \(\mathrm{P}\left(|g| \geq\left(\mathrm{E} g^{2}\right)^{1 / 2} / 100\right) \geq\)
\(7 / 8\). Then for each \(\ell, \ell^{\prime}\) we have \(\left|D_{\ell, \ell^{\prime}}\right| \geq 2^{-3 q} \sqrt{N} / L\) with probability \(\geq 7 / 8\). In other words the r.v. \(Y_{\ell, \ell^{\prime}}=\mathbf{1}_{\left|D_{\ell, \ell^{\prime}}\right| \geq 2^{-3 q} \sqrt{N} / L}\) satisfies \(\mathrm{E} Y_{\ell, \ell^{\prime}} \geq 7 / 8\). Then Bernstein's inequality shows that with overwhelming probability at least \(3 / 4\) of these variables equal 1 . For further use let us state the following more general principle. Considering \(M\) independent r.v.s \(Z_{i} \in\{0,1\}\) with \(\mathrm{P}\left(Z_{i}=1\right)=a_{i}=\mathrm{E} Z_{i}\) then for \(u>0\) we have
\[
\mathrm{P}\left(\left|\sum_{i \leq M}\left(Z_{i}-a_{i}\right)\right| \geq u M\right) \leq 2 \exp \left(-M u^{2} / L\right)
\]
and in particular \(\mathrm{P}\left(\sum_{i \leq M} Z_{i} \leq \sum_{i \leq M} a_{i}-M u\right) \leq 2 \exp \left(-M u^{2} / L\right)\).
Thus \(\left|D_{\ell, \ell^{\prime}}\right| \geq 2^{-3 q} \sqrt{N} / L\) for at least \(3 / 4\) of the squares, so that at least \(1 / 4\) of the squares are both safe and satisfy this inequality. Consequently (4.80) holds (for \(q+1\) rather than \(q\) ) follows from (4.96) as desired.

It is not exactly true that the r.v.s \(D_{\ell, \ell^{\prime}}\) are independent and Gaussian. Standard techniques exist to take care of this, namely Poissonization and normal approximation. There is all the room in the world because \(r \leq(\log N) / 100\). As these considerations are not related to the rest of the material of this work they are better omitted.

We now turn to the proofs of Lemmas 4.6.5 and 4.6.6. The next lemma prepares for these proofs.

Lemma 4.6.7. Consider \(q \leq r\) and a function of the type \(f=\sum_{k \leq q} f_{k}\), where \(f_{k}\) is given by (4.90) and where \(z_{k, \ell, \ell^{\prime}} \in\{0,1,-1\}\). Then
\[
\left|\frac{\partial^{2} f}{\partial x \partial y}\right| \leq \frac{2^{q-4}}{\sqrt{r}}
\]

Proof. We have
\[
\left|\frac{\partial^{2} f}{\partial x \partial y}\right|=\left|\sum_{k \leq q} \frac{2^{k-5}}{\sqrt{r}} \sum_{\ell, \ell^{\prime} \leq 2^{k}} z_{k, \ell, \ell^{\prime}} f_{k, \ell}^{\prime} \otimes f_{k, \ell^{\prime}}^{\prime}\right| \leq \sum_{k \leq q} \frac{2^{k-5}}{\sqrt{r}} \sum_{\ell, \ell^{\prime} \leq 2^{k}}\left|f_{k, \ell}^{\prime} \otimes f_{k, \ell^{\prime}}^{\prime}\right|
\]

The functions \(f_{k, \ell}^{\prime} \otimes f_{k, \ell^{\prime}}^{\prime}\) have disjoint support, and by the first part of (4.88) \(\left|f_{k, \ell}^{\prime} \otimes f_{k, \ell^{\prime}}^{\prime}\right| \leq 1\). Also, \(\sum_{k \leq q} 2^{k} \leq 2^{q+1}\).

Proof of Lemma 4.6.5. We will observe from the definition that all functions \(f_{k, \ell}^{\prime}\) for \(k \leq q\) are constant on the intervals \(\left[\ell 2^{-q-1},(\ell+1) 2^{-q-1}[\right.\). Thus according to (4.92), on a ( \(q+1\) )-square, \(\partial f / \partial x\) does not depend on \(x\). If ( \(x, y\) ) and ( \(x^{\prime}, y^{\prime}\) ) belong to the same ( \(q+1\) )-square then
\[
\frac{\partial f}{\partial x}\left(x^{\prime}, y^{\prime}\right)=\frac{\partial f}{\partial x}\left(x, y^{\prime}\right)
\]

Moreover \(\left|y-y^{\prime}\right| \leq 2^{-q-1}\) so that (4.99) implies
\[
\left|\frac{\partial f}{\partial x}(x, y)-\frac{\partial f}{\partial x}\left(x, y^{\prime}\right)\right| \leq\left|y-y^{\prime}\right| \frac{2^{q-5}}{\sqrt{r}} \leq \frac{2^{-6}}{\sqrt{r}}
\]
and combining with (4.100) we obtain
\[
\left|\frac{\partial f}{\partial x}(x, y)-\frac{\partial f}{\partial x}\left(x^{\prime}, y^{\prime}\right)\right|=\left|\frac{\partial f}{\partial x}(x, y)-\frac{\partial f}{\partial x}\left(x, y^{\prime}\right)\right| \leq \frac{2^{-6}}{\sqrt{r}}
\]

In particular if a ( \(q+1\) )-square contains a point at which \(|\partial f / \partial x| \geq 1 / 2\), then at each point of this square we have \(|\partial f / \partial x| \geq 1 / 2-2^{-6} / \sqrt{r} \geq 1 / 4\). The proportions \(\alpha\) of ( \(q+1\) )-squares with this property satisfies \(\alpha(1 / 4)^{2} \leq\) \(\|\partial f / \partial x\|_{2}^{2} \leq 2^{-12}\), where we have used (4.91) in the last inequality. This implies that at most a proportion \(2^{-8}\) of ( \(q+1\) )-squares can contain a point with \(|\partial f / \partial x| \geq 1 / 2\). Repeating the same argument for \(\partial f / \partial y\) shows that as desired at most half of ( \(q+1\) )-squares are dangerous.

Proof of Lemma 4.6.6. To ensure that \(g:=f+f_{q+1}\) is 1 -Lipschitz, it suffices to ensure that it is 1 -Lipschitz on each ( \(q+1\) )-square. When the square is dangerous, \(f_{q+1}=0\) on this square by construction and \(g\) is 1-Lipschitz on it because there \(g=f\) and \(f\) is 1 -Lipschitz.

When the square is safe, everywhere on the square we have \(|\partial f / \partial x| \leq 1 / 2\) and \(|\partial f / \partial y| \leq 1 / 2\). Now the second part of (4.88) implies
\[
\left\|\frac{\partial g}{\partial x}-\frac{\partial f}{\partial x}\right\|_{\infty}=\left\|\frac{1}{\sqrt{r}} 2^{q-4} \sum_{\ell, \ell^{\prime} \leq 2^{q+1}} z_{q+1, \ell, \ell^{\prime}} f_{q+1, \ell}^{\prime} \otimes f_{q+1, \ell^{\prime}}\right\|_{\infty} \leq \frac{1}{2^{5} \sqrt{r}}
\]
and
\[
\left\|\frac{\partial g}{\partial y}-\frac{\partial f}{\partial y}\right\|_{\infty}=\left\|\frac{1}{\sqrt{r}} 2^{q-4} \sum_{\ell, \ell^{\prime} \leq 2^{q+1}} z_{q+1, \ell, \ell^{\prime}} f_{q+1, \ell} \otimes f_{q+1, \ell^{\prime}}^{\prime}\right\|_{\infty} \leq \frac{1}{2^{5} \sqrt{r}}
\]
where we have used that the elements of the sum have disjoint supports. So we are certain that at each point of a safe square we have \(|\partial g / \partial x| \leq 1 / \sqrt{2}\) and \(|\partial g / \partial y| \leq 1 / \sqrt{2}\), and hence that \(g\) is 1-Lipschitz on a safe square.

Exercise 4.6.8. This is a continuation of Exercise 4.5.23. Adapt the method you learned in this section to prove that the bound (4.69) is of the correct order.

\subsection*{4.7 The Leighton-Shor Grid Matching Theorem}

Theorem 4.7.1 ([77]). If the points \(\left(Y_{i}\right)_{i \leq N}\) are evenly spread and if \(\left(X_{i}\right)_{i \leq N}\) are i.i.d. uniform over \([0,1]^{2}\), then (for \(N \geq 2\) ), with probability at least \(1-L \exp \left(-(\log N)^{3 / 2} / L\right)\) we have
\[
\inf _{\pi} \sup _{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \leq L \frac{(\log N)^{3 / 4}}{\sqrt{N}}
\]

In particular
\[
\mathrm{E} \inf _{\pi} \sup _{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \leq L \frac{(\log N)^{3 / 4}}{\sqrt{N}}
\]

To deduce (4.102) from (4.101) one simply uses any matching in the (rare) event that (4.101) fails. We shall prove in Section 4.8 that the inequality (4.102) can be reversed. A close cousin of this theorem can be found in Appendix A.

A first simple idea is that to prove Theorem 4.7.1 we do not care about what happens at a scale smaller than \((\log N)^{3 / 4} / \sqrt{N}\). Therefore consider the largest integer \(\ell_{1}\) with \(2^{-\ell_{1}} \geq(\log N)^{3 / 4} / \sqrt{N}\) (so that in particular \(2^{\ell_{1}} \leq\) \(\sqrt{N}\) ). We divide \([0,1]^{2}\) into little squares of side \(2^{-\ell_{1}}\). For each such square, we are interested in how many points ( \(X_{i}\) ) it contains, but we do not care where these points are located in the square. We shall deduce Theorem 4.7.1 from a discrepancy theorem for a certain class of functions. \({ }^{16}\) What we really have in mind is the class of functions which are indicators of a union \(A\) of little squares with sides of length \(2^{-\ell_{1}}\), and such that the boundary of \(A\) has a given length. It turns out that we shall have to parametrize the boundaries of these sets by curves, so it is convenient to turn things around and to consider the class of sets \(A\) that are the interiors of curves of given length.

To make things precise, let us define the grid \(G\) of \([0,1]^{2}\) of mesh width \(2^{-\ell_{1}}\) by
\[
G=\left\{\left(x_{1}, x_{2}\right) \in[0,1]^{2} ; 2^{\ell_{1}} x_{1} \in \mathbb{N} \text { or } 2^{\ell_{1}} x_{2} \in \mathbb{N}\right\}
\]

A vertex of the grid is a point \(\left(x_{1}, x_{2}\right) \in[0,1]^{2}\) with \(2^{\ell_{1}} x_{1} \in \mathbb{N}\) and \(2^{\ell_{1}} x_{2} \in \mathbb{N}\). There are \(\left(2^{\ell_{1}}+1\right)^{2}\) vertices. An edge of the grid is the segment between two vertices that are at distance \(2^{-\ell_{1}}\) of each other. A square of the grid is a square of side \(2^{-\ell_{1}}\) whose edges are edges of the grid. Thus, an edge of the grid is a subset of the grid, but a square of the grid is not a subset of the grid, see Figure 4.2.

A curve is the image of a continuous map \(\varphi:[0,1] \rightarrow \mathbb{R}^{2}\). We say that the curve is a simple curve if it is one-to-one on \([0,1[\). We say that the curve is traced on \(G\) if \(\varphi([0,1]) \subset G\), and that it is closed if \(\varphi(0)=\varphi(1)\). If \(C\) is a closed simple curve in \(\mathbb{R}^{2}\), the set \(\mathbb{R}^{2} \backslash C\) has two connected components. One of these is bounded. It is called the interior of \(C\) and is denoted by \(\stackrel{o}{C}\).

The proof of Theorem 4.7.1 has a probabilistic part (the hard one) and a deterministic part. The probabilistic part states that with high probability the number of points inside a closed curve differs from its expected value by at most the length of the curve times \(L \sqrt{N}(\log N)^{3 / 4}\). The deterministic part will be given at the end of the section and will show how to deduce Theorem 4.7.1 from Theorem 4.7.2.

\footnotetext{
\({ }^{16}\) This is the case for every matching theorem we prove.
}
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-151.jpg?height=584&width=592&top_left_y=418&top_left_x=566)

Fig. 4.2. A square \(A\), and edge \(e\), a vertex \(V\) and a simple curve \(C\) traced on \(G\).

Theorem 4.7.2. With probability at least \(1-L \exp \left(-(\log N)^{3 / 2} / L\right)\), the following occurs. Given any closed simple curve \(C\) traced on \(G\), we have
\[
\left|\sum_{i \leq N}\left(1_{{ }_{\circ}}\left(X_{i}\right)-\lambda(\stackrel{o}{C})\right)\right| \leq L \ell(C) \sqrt{N}(\log N)^{3 / 4},
\]
where \(\lambda(\stackrel{o}{C})\) is the area of \(\stackrel{o}{C}\) and \(\ell(C)\) is the length of \(C\).
We will reduce the proof of this theorem to the following result, which concerns curves of a given length going through a given vertex.
Proposition 4.7.3. Consider a vertex \(\tau\) of \(G\) and \(k \in \mathbb{Z}\). Define \(\mathcal{C}(\tau, k)\) as the set of closed simple curves traced on \(G\) that pass through \(\tau^{17}\) and have length \(\leq 2^{k}\). Then, if \(-\ell_{1} \leq k \leq \ell_{1}+2\), with probability at least \(1-\) \(L \exp \left(-(\log N)^{3 / 2} / L\right)\), for each \(C \in \mathcal{C}(\tau, k)\) we have
\[
\left|\sum_{i \leq N}\left(1_{{ }_{\circ}^{\circ}}\left(X_{i}\right)-\lambda(\stackrel{o}{C})\right)\right| \leq L 2^{k} \sqrt{N}(\log N)^{3 / 4} .
\]

It would be easy to control the left-hand side if one considered only curves with a simple pattern, such as boundaries of rectangles. The point however is that the curves we consider can be very complicated, and the longer we allow them to be, the more so. Before we discuss Proposition 4.7.3 further, we show that it implies Theorem 4.7.2.

\footnotetext{
17 that is, \(\tau\) is an end vertex of an edge which belongs to the curve.
}

Proof of Theorem 4.7.2. Since there are at most \(\left(2^{\ell_{1}}+1\right)^{2} \leq L N\) choices for the vertex \(\tau\), we can assume with probability at least
\[
1-L\left(2^{\ell_{1}}+1\right)^{2}\left(2 \ell_{1}+4\right) \exp \left(-(\log N)^{3 / 2} / L\right) \geq 1-L^{\prime} \exp \left(-(\log N)^{3 / 2} / L^{\prime}\right)
\]
that (4.104) occurs for all choices of \(C \in \mathcal{C}(\tau, k)\), for any \(\tau\) and any \(k\) with \(-\ell_{1} \leq k \leq \ell_{1}+2\).

Consider a simple curve \(C\) traced on \(G\). Bounding the length of \(C\) by the total length of the edges of \(G\), we have \(2^{-\ell_{1}} \leq \ell(C) \leq 2\left(2^{\ell_{1}}+1\right) \leq 2^{\ell_{1}+2}\). Then the smallest integer \(k\) for which \(\ell(C) \leq 2^{k}\) satisfies \(-\ell_{1} \leq k \leq \ell_{1}+2\). Since \(2^{k} \leq 2 \ell(C)\) the proof is finished by (4.104).

Exercise 4.7.4. Prove the second inequality in (4.105) in complete detail.
The main step to prove Proposition 4.7.3 is the following.
Proposition 4.7.5. Consider a vertex \(\tau\) of \(G\) and \(k \in \mathbb{Z}\). Define \(\mathcal{C}(\tau, k)\) as in Proposition 4.7.3. Then, if \(-\ell_{1} \leq k \leq \ell_{1}+2\) we have
\[
\mathrm{E} \sup _{C \in C(\tau, k)}\left|\sum_{i \leq N}\left(1_{{ }_{\circ}}\left(X_{i}\right)-\lambda(\stackrel{o}{C})\right)\right| \leq L 2^{k} \sqrt{N}(\log N)^{3 / 4}
\]

Proof of Proposition 4.7.3. To prove Proposition 4.7.3 we have to go from the control in expectation provided by (4.106) to the control in probability of (4.104). There is powerful tool to do this: concentration of measure. The function
\[
f\left(x_{1}, \ldots, x_{N}\right)=\sup _{C \in \mathcal{C}(\tau, k)}\left|\sum_{i \leq N}\left(1_{{ }_{\circ}}\left(x_{i}\right)-\lambda(\stackrel{o}{C})\right)\right|
\]
of points \(x_{1}, \ldots, x_{N} \in[0,1]^{2}\) has the property that changing the value of a given variable \(x_{i}\) can change the value of \(f\) by at most one. One of the earliest "concentration of measure" results (for which we refer to [74]) asserts that for such a function the r.v. \(W=f\left(X_{1}, \ldots, X_{N}\right)\) satisfies a deviation inequality of the form
\[
\mathrm{P}(|W-\mathrm{E} W| \geq u) \leq 2 \exp \left(-\frac{u^{2}}{2 N}\right)
\]

Using (4.106) to control EW and taking \(u=L 2^{k} \sqrt{N}(\log N)^{3 / 4}\) proves Proposition 4.7.3 in the case \(k \geq 0\). A little bit more work is needed when \(k<0\). In that case a curve of length \(2^{k}\) is entirely contained in the square \(V\) of center \(\tau\) and side \(2^{k+1}\) and \(1_{C}\left(X_{i}\right)=0\) unless \(X_{i} \in V\). To take advantage of this, we work conditionally on \(I=\left\{i \leq N ; X_{i} \in V\right\}\) and we can then use (4.107) with card \(I\) instead of \(N\). This provides the desired inequality when card \(I \leq L 2^{2 k} N\). On the other hand, by (4.98) and since \(\lambda(V)=2^{2 k+2}\) we have \(\mathrm{P}\left(\operatorname{card} I \geq L 2^{2 k} N\right) \leq \exp \left(-N 2^{2 k}\right) \leq L \exp \left(-(\log N)^{3 / 2} / L\right)\) because \(k \geq-\ell_{1}\) and the choice of \(\ell_{1}\).

We start the proof of Proposition 4.7.5. We denote by \(\mathcal{F}_{k}\) the class of functions of the type \(\mathbf{1}_{C}\), where \(C \in \mathcal{C}(\tau, k)\) so we can rewrite (4.106) as
\[
\mathrm{E} \sup _{f \in \mathcal{F}_{k}}\left|\sum_{i \leq N}\left(f\left(X_{i}\right)-\int f \mathrm{~d} \lambda\right)\right| \leq L 2^{k} \sqrt{N}(\log N)^{3 / 4}
\]

The key point again is the control on the size of \(\mathcal{F}_{k}\) with respect to the distance of \(L^{2}(\lambda)\). The difficult part of this control is the following.

Proposition 4.7.6. We have
\[
\gamma_{2}\left(\mathcal{F}_{k}, d_{2}\right) \leq L 2^{k}(\log N)^{3 / 4}
\]

Another much easier fact is the following.
Proposition 4.7.7. We have
\[
\gamma_{1}\left(\mathcal{F}_{k}, d_{\infty}\right) \leq L 2^{k} \sqrt{N}
\]

Proof of (4.108) and of Proposition 4.7.5. Combine Proposition 4.7.6, Proposition 4.7.7 and Theorem 4.5.16.

Let us first prove the easy Proposition 4.7.7.
Lemma 4.7.8. We have \(\operatorname{card} \mathcal{C}(\tau, k) \leq 2^{2^{k+\ell_{1}+1}}=N_{k+\ell_{1}+1}\).
Proof. A curve \(C \in \mathcal{C}(\tau, k)\) consists of at most \(2^{k+\ell_{1}}\) edges of \(G\). If we move through \(C\), at each vertex of \(G\) we have at most 4 choices for the next edge, so \(\operatorname{card} \mathcal{C}(\tau, k) \leq 4^{2^{k+\ell_{1}}}=N_{k+\ell_{1}+1}\).

Proof of Proposition 4.7.7. Generally speaking, a set \(T\) of cardinality \(\leq N_{m}\) and diameter \(\Delta\) satisfies \(\gamma_{1}(T, d) \leq L \Delta 2^{m}\), as is shown by taking \(\mathcal{A}_{n}=\{T\}\) for \(n<m\) and \(A_{m}(t)=\{t\}\). We use this for \(T=\mathcal{F}_{k}\), so that \(\operatorname{card} T=\) \(\operatorname{card} \mathcal{C}(\tau, k) \leq N_{k+\ell_{1}+1}\) by Lemma 4.7.8, and \(2^{k+\ell_{1}+1} \leq L 2^{k} \sqrt{N}\).

We now attack the difficult part, the proof of Proposition 4.7.6. The exponent \(3 / 4\) occurs through the following general principle, where we recall that if \(d\) is a distance, so is \(\sqrt{d}\).

Lemma 4.7.9. Consider a finite metric space ( \(T, d\) ) with card \(T \leq N_{m}\). Then
\[
\gamma_{2}(T, \sqrt{d}) \leq m^{3 / 4} \gamma_{1,2}(T, d)^{1 / 2}
\]

Proof. Since \(T\) is finite there exists an admissible sequence ( \(\mathcal{A}_{n}\) ) of \(T\) such that
\[
\forall t \in T, \sum_{n \geq 0}\left(2^{n} \Delta\left(A_{n}(t), d\right)\right)^{2} \leq \gamma_{1,2}(T, d)^{2}
\]

Without loss of generality we can assume that \(A_{m}(t)=\{t\}\) for each \(t\), so that in (4.112) the sum is over \(n \leq m-1\). Now
\[
\Delta(A, \sqrt{d})=\Delta(A, d)^{1 / 2}
\]
so that, using Hölder's inequality,
\[
\begin{aligned}
\sum_{0 \leq n \leq m-1} 2^{n / 2} \Delta\left(A_{n}(t), \sqrt{d}\right) & =\sum_{0 \leq n \leq m-1}\left(2^{n} \Delta\left(A_{n}(t), d\right)\right)^{1 / 2} \\
& \leq m^{3 / 4}\left(\sum_{n \geq 0}\left(2^{n} \Delta\left(A_{n}(t), d\right)\right)^{2}\right)^{1 / 4} \\
& \leq m^{3 / 4} \gamma_{1,2}(T, d)^{1 / 2}
\end{aligned}
\]
which concludes the proof.
Let us denote by \(A \Delta B\) the symmetric difference \((A \backslash B) \cup(B \backslash A)\) between two sets \(A\) and \(B\). On the set of closed simple curves traced on \(G\), we define the distance \(d_{1}\) by \(d_{1}\left(C, C^{\prime}\right)=\lambda\left(\stackrel{o}{C} \triangle \stackrel{o}{C}^{\prime}\right)\) and the distance
\[
\delta\left(C_{1}, C_{2}\right):=\left\|\mathbf{1}_{\stackrel{o}{C}_{1}}-\mathbf{1}_{\stackrel{o}{C}_{2}}\right\|_{2}=\left(\lambda\left(\stackrel{o}{C}_{1} \Delta \stackrel{o}{C}_{2}\right)\right)^{1 / 2}=\left(d_{1}\left(C_{1}, C_{2}\right)\right)^{1 / 2}
\]
so that
\[
\gamma_{2}\left(\mathcal{F}_{k}, d_{2}\right)=\gamma_{2}(\mathcal{C}(\tau, k), \delta)=\gamma_{2}\left(\mathcal{C}(\tau, k), \sqrt{d_{1}}\right)
\]
and using Lemma 4.7.8 and (4.111) for \(m:=k+\ell_{1}+1\) we obtain
\[
\gamma_{2}\left(\mathcal{F}_{k}, d_{2}\right) \leq L(\log N)^{3 / 4} \gamma_{1,2}\left(\mathcal{C}(\tau, k), d_{1}\right)^{1 / 2}
\]
because \(m \leq L \log N\) for \(k \leq \ell_{1}+2\).
Therefore it remains only to prove the following.
Proposition 4.7.10. We have
\[
\gamma_{1,2}\left(\mathcal{C}(\tau, k), d_{1}\right) \leq L 2^{2 k}
\]

The reason why this is true is that the metric space ( \(\mathcal{L}, d_{2}\) ) of Proposition 4.1.8 satisfies \(\gamma_{1,2}\left(\mathcal{L}, d_{2}\right)<\infty\), while \(\left(\mathcal{C}(\tau, k), d_{1}\right)\) is a Lipschitz image of a subset of this metric space ( \(\mathcal{L}, d_{2}\) ). The elementary proof of the following may be found in Section B.2.

Lemma 4.7.11. There exists a map \(W\) from a subset \(T\) of \(\mathcal{L}\) onto \(\mathcal{C}(\tau, k)\) which for any \(f_{0}, f_{1} \in T\) satisfies
\[
d_{1}\left(W\left(f_{0}\right), W\left(f_{1}\right)\right) \leq L 2^{2 k}\left\|f_{0}-f_{1}\right\|_{2}
\]

To conclude the proof of Proposition 4.7.10 we check that the functionals \(\gamma_{\alpha, \beta}\) behave as expected under Lipschitz maps.
Lemma 4.7.12. Consider two metric spaces \((T, d)\) and \(\left(U, d^{\prime}\right)\) and a map \(f:(T, d) \rightarrow\left(U, d^{\prime}\right)\) which is onto and satisfies
\[
\forall x, y \in T, d^{\prime}(f(x), f(y)) \leq A d(x, y)
\]
for a certain constant \(A\). Then
\[
\gamma_{\alpha, \beta}\left(U, d^{\prime}\right) \leq K(\alpha, \beta) A \gamma_{\alpha, \beta}(T, d)
\]

Proof. This is really obvious when \(f\) is one-to-one. We reduce to that case by considering a map \(\varphi: U \rightarrow T\) with \(f(\varphi(x))=x\) and replacing \(T\) by \(\varphi(U)\).
![](https://cdn.mathpix.com/cropped/2025_07_05_f1f18b655d29f2613d56g-155.jpg?height=483&width=451&top_left_y=596&top_left_x=653)

Fig. 4.3. A union \(A\) of little squares, and the boundary of \(A^{\prime}\).

It remains to deduce Theorem 4.7.1 from Theorem 4.7.2. The argument is purely deterministic and unrelated to any other material in the present book. The basic idea is very simple, and to keep it simple we describe it in slightly imprecise terms. Consider a union \(A\) of little squares of side length \(2^{-\ell_{1}}\) and the union \(A^{\prime}\) of all the little squares that touch \(A\), see Figure 4.3.

We want to prove that \(A^{\prime}\) contains as many points \(Y_{i}\) as \(A\) contains points \(X_{i}\), so that by Hall's Marriage Lemma each point \(X_{i}\) can be matched to a point \(Y_{i}\) in the same little square, or in a neighbor of it. Since the points \(Y_{i}\) are evenly spread the number of such points in \(A^{\prime}\) is very nearly \(N \lambda\left(A^{\prime}\right)\). There may be more than \(N \lambda(A)\) points \(X_{i}\) in \(A\), but (4.103) tells us that the excess number of points cannot be more than a proportion of the length \(\ell\) of the boundary of \(A\). The marvelous fact is that we may also expect that \(\lambda\left(A^{\prime}\right)-\lambda(A)\) is also proportional to \(\ell\), so that we may hope that the excess number of points \(X_{i}\) in \(A\) should not exceed \(N\left(\lambda\left(A^{\prime}\right)-\lambda(A)\right)\), proving the result. The proportionality constant is not quite right to make the argument work, but this difficulty is bypassed simply by applying the same argument to a slightly coarser grid.

When one tries to describe precisely what is meant by the previous argument, one has to check a number of details. This elementary task which requires patience is performed in Appendix B.3.

\subsection*{4.8 Lower Bound for the Leighton-Shor Theorem}

Theorem 4.8.1. If the points \(\left(X_{i}\right)_{i \leq N}\) are i.i.d. uniform over \([0,1]^{2}\) and the points \(\left(Y_{i}\right)_{i \leq N}\) are evenly spread, then
\[
\mathrm{E} \inf _{\pi} \max _{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \geq \frac{(\log N)^{3 / 4}}{L \sqrt{N}}
\]

We consider the class of functions
\[
\mathcal{C}=\left\{f:[0,1] \rightarrow[0,1] ; f(0)=f(1)=0 ; \int_{0}^{1} f^{\prime 2}(x) \mathrm{d} x \leq 1\right\}
\]

For \(f \in \mathcal{C}\) we consider its subgraph
\[
S(f):=\left\{(x, y) \in[0,1]^{2} ; y \leq f(x)\right\}
\]

To prove (4.116) the key step will be to show that with high probability we may find \(f \in \mathcal{C}\) with
\[
\operatorname{card}\left\{i \leq N ; X_{i} \in S(f)\right\} \geq N \lambda(S(f))+\frac{1}{L} \sqrt{N}(\log N)^{3 / 4}
\]

With a little more work, we could actually prove that we can find such a function \(f\) which moreover satisfies \(\left|f^{\prime}\right| \leq 1\). This extra work is not needed. The key property of \(f\) here is that its graph has a bounded length and this is already implied by the condition \(\left\|f^{\prime}\right\|_{2} \leq 1\), since the length of this graph is \(\int_{0}^{1} \sqrt{1+f^{\prime 2}(x)} \mathrm{d} x \leq 2\).

Lemma 4.8.2. The set of points within distance \(\epsilon>0\) of the graph of \(f\) has an area \(\leq L \epsilon\). The set of points within distance \(\epsilon>0\) of \(S(f)\) has an area \(\leq \lambda(S(f))+L \epsilon\).

Proof. The graph of \(f \in \mathcal{C}\) has length \(\leq 2\). One can find a subset of the graph of \(f\) of cardinality \(\leq L / \epsilon\) such that each point of the graph is within distance \(\epsilon\) of this set \({ }^{18}\) A point within distance \(\epsilon\) of the graph then belongs to one of \(L / \epsilon\) balls of radius \(2 \epsilon\). This proves the first assertion. The second assertion follows from the fact that a point which is within distance \(\epsilon\) of \(S(f)\) either belongs to \(S(f)\) or is within distance \(\epsilon\) of the graph of \(f\).

Proof of Theorem 4.8.1. We prove that when there exists a function \(f\) satisfying (4.119) then \(\inf _{\pi} \max _{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \geq(\log N)^{3 / 4} / L \sqrt{N}\). Let us denote by \(S(f)_{\epsilon}\) the \(\epsilon\)-neighborhood \({ }^{19}\) of \(S(f)\) in \([0,1]^{2}\). We first observe that for any \(f \in \mathcal{C}\) we have

\footnotetext{
18 This is true for any curve of length 2. If one consider a parameterization \(\varphi(t)\) \(0 \leq t \leq 2\) of the curve by arc length, the points \(\varphi(k \epsilon)\) for \(k \leq 2 / \epsilon\) have this property.
\({ }^{19}\) That is, the set of points within distance \(\leq \epsilon\) of a point of \(S(f)\).
}
\[
\operatorname{card}\left\{i \leq N ; Y_{i} \in S(f)_{\epsilon}\right\} \leq N \lambda(S(f))+L \epsilon N+L \sqrt{N} .
\]

This is because, by definition of an evenly spread family, each point \(Y_{i}\) belongs to a small rectangle \(R_{i}\) of area \(1 / N\) and of diameter \(\leq 10 / \sqrt{N}\), and a pessimistic upper bound for the left-hand side of (4.120) is the number of such rectangles that intersect \(S(f)_{\epsilon}\). These rectangles are entirely contained in the set of points within distance \(L / \sqrt{N}\) of \(S(f)_{\epsilon}\), i.e. in the set of points within distance \(\leq \epsilon+L / \sqrt{N}\) of \(S(f)\) and by Lemma 4.8.2 this set has area \(\leq \lambda(S(f))+L \epsilon+L / \sqrt{N}\), hence the bound (4.120).

Consequently (and since we may assume that \(N\) is large enough) (4.119) implies that for \(\epsilon=(\log N)^{3 / 4} /(L \sqrt{N})\) it holds that
\[
\operatorname{card}\left\{i \leq N ; Y_{i} \in S(f)_{\epsilon}\right\}<\operatorname{card}\left\{i \leq N ; X_{i} \in S(f)\right\},
\]
and therefore any matching must pair at least one point \(X_{i} \in S(f)\) with a point \(Y_{j} \notin S(f)_{\epsilon}\), so that \(\max _{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \geq \epsilon\).

Recalling the functions \(f_{k, \ell}\) of (4.82), we consider now an integer \(c \geq 2\) which will be determined later. The purpose of \(c\) is to give us room. Thus, by (4.87),
\[
\int_{0}^{1} f_{c k, \ell}(x) \mathrm{d} x=2^{-2 c k-2}
\]

Let us set
\[
\tilde{f}_{k, \ell}=\frac{1}{\sqrt{r}} f_{c k, \ell}
\]

Consider the functions of the type
\[
f=\sum_{k \leq r} f_{k} \text { with } f_{k}=\sum_{1 \leq \ell \leq 2^{c k}} x_{k, \ell} \tilde{f}_{k, \ell},
\]
where \(x_{k, \ell} \in\{0,1\}\). Then \(f(0)=f(1)=0\).
Lemma 4.8.3. A function \(f\) of the type (4.122) satisfies
\[
\int_{0}^{1} f^{\prime}(x)^{2} \mathrm{~d} x \leq 1
\]

Proof. Using (4.83) and (4.84) we obtain
\[
\int_{0}^{1} f^{\prime}(x)^{2} \mathrm{~d} x=\sum_{k \leq r} \sum_{\ell \leq 2^{c k}} \frac{x_{k, \ell}^{2}}{r}\left\|f_{c k, \ell}\right\|_{2}^{2}=\sum_{k \leq r} \sum_{\ell \leq 2^{c k}} \frac{x_{k, \ell}^{2}}{r} 2^{-c k} \leq 1 .
\]

Consequently each function of the type (4.122) belongs the class \(\mathcal{C}\) of (4.117).
Proof of (4.119). Given \(N\) large we choose \(r\) as the largest integer for which \(2^{c r} \leq N^{1 / 100}\), so that \(r \leq \log N / L c\). The construction of the functions \(f_{k}\)
is inductive. Assume that we have already constructed \(f_{1}, \ldots, f_{q}\), and let \(g=\sum_{k \leq q} f_{k}\). For \(\ell \leq 2^{c(q+1)}\) let us consider the region
\[
R_{\ell}:=S\left(g+\tilde{f}_{q+1, \ell}\right) \backslash S(g)
\]
so that by (4.121)
\[
\lambda\left(R_{\ell}\right)=\frac{2^{-2 c(q+1)}}{4 \sqrt{r}}
\]

These regions are disjoint because the functions \(\tilde{f}_{q+1, \ell}\) have disjoint support. Furthermore if we choose \(f_{q+1}=\sum_{\ell \leq 2^{c(q+1)}} x_{q+1, \ell} \tilde{f}_{q+1, \ell}\) where \(x_{q+1, \ell} \in\) \(\{0,1\}\) then we have
\[
S\left(g+f_{q+1}\right) \backslash S(g)=\bigcup_{\ell \in J} R_{\ell}
\]
where
\[
J=\left\{\ell \leq 2^{c(q+1)} ; x_{q+1, \ell}=1\right\}
\]
and thus
\[
\lambda\left(S\left(g+f_{q+1}\right) \backslash S(g)\right)=\sum_{J} \lambda\left(R_{\ell}\right)
\]

Since our goal is to construct functions such that there is an excess of points \(X_{i}\) in their subgraph, we do the obvious thing, we take \(x_{q+1, \ell}=1\) if there is an excess of points \(X_{i}\) in \(R_{\ell}\), that if
\[
\delta_{\ell}:=\operatorname{card}\left\{i \leq N ; X_{i} \in R_{\ell}\right\}-N \lambda\left(R_{\ell}\right) \geq 0
\]
and otherwise we set \(x_{k+1, \ell}=0\). We have, recalling (4.125),
\[
\begin{array}{r}
\operatorname{card}\left\{i \leq N ; X_{i} \in S\left(g+f_{q+1}\right) \backslash S(g)\right\}=\sum_{J} \operatorname{card}\left\{i \leq N ; X_{i} \in R_{\ell}\right\} \\
=\sum_{J} \delta_{\ell}+N \lambda\left(S\left(g+f_{q+1}\right) \backslash S(g)\right)
\end{array}
\]

We will show that with high probability we have \(\sum_{J} \delta_{\ell} \geq \sqrt{N} /\left(L r^{1 / 4}\right)\). Recalling that \(g=\sum_{k \leq q} f_{k}\) and \(g+f_{q+1}=\sum_{k \leq q+1} f_{k}\), summation of the inequalities (4.127) over \(q<r\) then proves (4.119), where \(f\) is the function \(\sum_{k<r} f_{k}\).

Let us say that the region \(R_{\ell}\) is favorable if
\[
\delta_{\ell} \geq \sqrt{N \lambda\left(R_{\ell}\right)} / L^{*}=2^{-c(q+1)} \sqrt{N} /\left(L r^{1 / 4}\right)
\]
where the universal constant \(L^{*}\) will be determined later. The idea underlying this definition is that given a subset \(A\) of the square, with \(1 / N \ll \lambda(A) \leq 1 / 2\), the number of points \(X_{i}\) which belong to \(A\) has typical fluctuations of order
\(\sqrt{N \lambda(A)}\). Since \(\delta_{\ell} \geq 0\) for \(\ell \in J\), and since by construction \(\ell \in J\) when \(R_{\ell}\) is favorable, we have
\[
\sum_{J} \delta_{\ell} \geq \operatorname{card}\left\{\ell ; R_{\ell} \text { favorable }\right\} \times 2^{-c(q+1)} \sqrt{N} /\left(L r^{1 / 4}\right)
\]

To conclude the proof it then suffices to show that with overwhelming probability at least a fixed proportion of the regions \(R_{\ell}\) for \(\ell \leq 2^{c(q+1)}\) are favorable. One has to be cautious that the r.v.s \(X_{i}\) are not independent of the function \(g\) and of the regions \(R_{\ell}\) because in particular the construction of \(g\) uses the values of the \(X_{i}\).

One simple way around that difficulty is to proceed as follows. There are at most \(\prod_{k \leq q} 2^{2^{c k}} \leq 2^{2^{c q+1}}\) possibilities for \(g\). To each of these possibilities corresponds a family of \(2^{2^{c(q+1)}}\) regions \(R_{\ell}\). If we can ensure that with overwhelming probability for each of these families a least a fixed proportion of the \(R_{\ell}\) are favorable we are done. Since there are at most \(2^{2^{c q+1}}\) families, it suffices to prove that for a given family this fails with probability \(\leq 2^{-2^{c q+2}}\). To achieve this we proceed as follows. By normal approximation of the tails of the binomial law, there exists a constant \(L^{*}\) and a number \(N_{0}>0\) such that given any set \(A \subset[0,1]^{2}\) with \(1 / 2 \geq \lambda(A)\) and \(N \lambda(A) \geq N_{0}\) we have
\[
\mathrm{P}\left(\operatorname{card}\left\{i \leq N ; X_{i} \in A\right\}-N \lambda(A) \geq \sqrt{N \lambda(A)} / L^{*}\right) \geq 1 / 4 .
\]

Since \(c\) is a universal constant and \(2^{r c} \leq N^{1 / 100}\), (4.124) shows that \(N \lambda\left(R_{\ell}\right)\) becomes large with \(N\). In particular (4.128) shows that the probability that a given region \(R_{\ell}\) is favorable is \(\geq 1 / 4\). Now, using Poissonization, we can pretend that these probabilities are independent as \(\ell\) varies. As noted in (4.98), given \(M\) independent r.v.s \(Z_{i} \in\{0,1\}\) with \(\mathrm{P}\left(Z_{i}=1\right) \geq 1 / 4\), then \(\mathrm{P}\left(\sum_{i \leq M} Z_{i} \leq M / 8\right) \leq \exp (-\beta M)\) for some universal constant \(\beta\). Since here we have \(M=2^{c(q+1)}\) then \(\exp (-\beta M)=\exp \left(-\beta 2^{c-2} 2^{c q+2}\right)\). This is \(\leq 2^{-2^{c q+2}}\) as required provided we have chosen \(c\) large enough that \(\beta 2^{c-2} \geq 1\).

\subsection*{4.9 For the Expert Only}

Having proved both the Ajtai-Komlós-Tusnády and the Leighton-Shor matching theorems, we should not fall under the illusion that we understand everything about matchings. The most important problem left is arguably the ultimate matching conjecture, stated later as Problem 17.1.2. A first step in that direction would be to answer the following question. \({ }^{20}\)

Question 4.9.1 Can we find a matching which achieves simultaneously both (4.36) and (4.101)?

\footnotetext{
\({ }^{20}\) The difference between a Problem and a Question is that a Question is permitted to sound less central.
}

The existence of such a matching does not seem to be of any particular importance, but the challenge is that the Ajtai-Komlós-Tusnády (AKT) theorem and the Leighton-Shor matching theorems are proved by rather different routes and it is far from obvious to find a common proof.

In the rest of the section, we discuss a special matching result. Consider the space \(T=\{0,1\}^{\mathbb{N}}\) provided with the distance \(d\left(t, t^{\prime}\right)=2^{-j}\), where \(j=\) \(\min \left\{i \geq 1 ; t_{i} \neq t_{i}^{\prime}\right\}\) for \(t=\left(t_{i}\right)_{i \geq 1}\). This space somewhat resembles the unit interval, in the sense that \(N(T, d, \epsilon) \leq L \epsilon^{-1}\) for \(\epsilon \leq 1\). The space of Exercise 4.5.23 is essentially the space \(T \times T\). The AKT theorem tells us what happens for matchings in \([0,1]^{2}\) and Exercise 4.5.23 tells us what happens for matchings in \(T^{2}\). But what happens in the space \(U:=[0,1] \times T\) ? It does not really matter which specific sensible distance we use on \(U\), let us say that we define \(d\left((x, t),\left(x^{\prime}, t^{\prime}\right)\right)=\left|x-x^{\prime}\right|+d\left(t, t^{\prime}\right)\).

Theorem 4.9.2. The expected cost of the optimal matching of \(N\) random i.i.d uniformly distributed \({ }^{21}\) points in \(U\) with \(N\) evenly spread points is exactly of order \(\sqrt{N}(\log N)^{3 / 4}\).

The appealing part of this special result is of course the fractional power of log. This result is as pretty as almost anything found in this book, but its special nature makes it appropriate to guide the (expert) reader to the proof through exercises.

Let us start with a finite approximation of \(T\). We consider the space \(T_{m}=\{0,1\}^{m}\) provided with the distance defined for \(t \neq t^{\prime}\) by \(d\left(t, t^{\prime}\right)=2^{-j}\), where \(j=\min \left\{i \geq 1 ; t_{i} \neq t_{i}^{\prime}\right\}\) for \(t=\left(t_{i}\right)_{i \leq m}\). We set \(U_{m}=[0,1] \times T_{m}\) and we denote by \(\theta_{m}\) the uniform measure on \(U_{m}\). Surely the reader who has reached this stage knows how to deduce \({ }^{22}\) the upper bound of Theorem 4.9.2 from the following.

Theorem 4.9.3. The set \(\mathcal{L}\) of 1 -Lipschitz functions \(f\) on \(U_{m}\) which satisfies \(|f| \leq 1\) satisfy \(\gamma_{2}\left(\mathcal{L}, d_{2}\right) \leq L m^{3 / 4}\).

Here of course \(\mathcal{L}\) is seen as a subset of \(L^{2}\left(U_{m}, \theta_{m}\right)\). The proof of Theorem 4.9.3 will use expansion of the elements of \(\mathcal{L}\) on a suitable basis. Using the same method as in Lemma 4.5.12 one can assume furthermore that the functions of \(\mathcal{L}\) are zero on \(\{0\} \times T_{m}\) and \(\{1\} \times T_{m}\). For \(0 \leq n \leq m\) we consider the natural partition \(\mathcal{C}_{n}\) of \(T_{m}\) into \(2^{n}\) sets obtained by fixing the first \(n\) coordinates of \(t \in T_{m}\). Denoting by \(\mu_{m}\) the uniform measure on \(T_{m}\), for \(C \in \mathcal{C}_{n}\) we have \(\mu_{m}(C)=2^{-n}\). A set \(C \in \mathcal{C}_{n}\) with \(n<m\) is the union of two sets \(C_{1}\) and \(C_{2}\) in \(\mathcal{C}_{n+1}\). We denote by \(h_{C}\) a function on \(T_{n}\) which equals \(2^{n / 2}\) on one of these sets and \(-2^{n / 2}\) on the other. Consider also the function \(h_{\emptyset}\) on \(T_{m}\), constant equal to 1 . In this manner we obtain an orthogonal basis ( \(h_{C}\) ) of \(L^{2}\left(T_{m}, \mu_{m}\right)\). For \(f \in \mathcal{L}\) we consider the coefficients of \(f\) on this basis.

\footnotetext{
\({ }^{21}\) It should be obvious what is meant by "uniform probability on \(U\) ".
\({ }^{22}\) By following the scheme of proof of (4.43).
}
\[
a_{p, C}(f):=\int_{U_{m}} \exp (2 i p \pi x) h_{C}(t) f(x, t) \mathrm{d} x \mathrm{~d} \mu_{m}(t)
\]

There and always, \(p \in \mathbb{Z}, n \geq 0, C \in \mathcal{C}_{n}\) or \(p \in \mathbb{Z}\) and \(C=\emptyset\). We will lighten notation by writing simply \(\sum_{p, C}\) sums over all possible values of ( \(p, C\) ) as above.

Exercise 4.9.4. (a) Prove that
\[
\sum_{p, C} p^{2}\left|a_{p, C}\right|^{2} \leq L
\]

Hint: just use that \(|\partial f / \partial x| \leq 1\).
(b) Prove that for each \(n\) and each \(C \in \mathcal{C}_{n}\) we have
\[
\sum_{p \in \mathbb{Z}}\left|a_{p, C}\right|^{2} \leq L 2^{-3 n}
\]

Hint: prove that \(\left|\int h_{C}(t) f(x, t) \mathrm{d} \mu_{m}(t)\right| \leq L 2^{-3 n / 2}\).
We have just shown that \(\mathcal{L}\) is isometric to a subset of the set \(\mathcal{A}\) of sequences ( \(a_{p, C}\) ) which satisfy (4.129) and (4.130).
Exercise 4.9.5. We will now show that \(\gamma_{2}(\mathcal{A}) \leq L m^{3 / 4}\).
(a) Prove that \(\mathcal{A}\) is contained in an ellipsoid of the type
\[
\mathcal{E}=\left\{\left(a_{c, C}\right) ; \sum_{p, C} \alpha_{p, C}^{2}\left|a_{p, C}\right|^{2} \leq 1\right\}
\]
where \(\alpha_{p, C}^{2}=\left(p^{2}+2^{2 n} / m\right) / L\) if \(C \in \mathcal{C}_{n}, n \geq 0\) and \(\alpha_{p, \emptyset}^{2}=p^{2} / L\).
(b) Conclude using (2.155). (The reader must be careful for the unfortunate clash of notation.)

The goal of the next exercise is to prove the lower bound in Theorem 4.9.2. This lower bound is obtained by a non-trivial twist on the proof of the lower bound for the AKT theorem, so you must fully master that argument to have a chance.

Exercise 4.9.6. Let us recall the functions \(f_{q, \ell}\) of (4.82) where we take \(r \simeq\) \((\log N) / 100\). For \(n \geq 0\) we still consider the natural partition \(\mathcal{C}_{n}\) of \(T\) into \(2^{n}\) sets obtained by fixing the first \(n\) coordinates of \(t \in T\). We consider an integer \(p\) with \(2^{-p} \simeq 1 / \sqrt{r}\). For each \(q\), each \(\ell \leq 2^{q}\) and \(C \in \mathcal{C}_{q+p}\) we consider the function \(f_{q, \ell, C}\) on \(U\) given by \(f_{q, \ell, C}(x, t)=2^{-p-20} f_{q, \ell}(x) 1_{C}(t)\). We consider functions of the type \(f_{q}=\sum_{\ell \leq 2^{q}, C \in \mathcal{C}_{q+p}} z_{q, \ell, C} f_{q, \ell, C}\) where \(z_{q, \ell, C} \in\{0,1,-1\}\). Copy the proof of the lower bound of the AKT theorem to prove that with high probability one can construct these functions such that \(\sum_{k \leq q} f_{k}\) is 1 Lipschitz and for each \(q, \sum_{i \leq N}\left(f_{q}\left(X_{i}\right)-\int f_{q} \mathrm{~d} \theta\right) \geq \sqrt{N} /\left(L r^{1 / 4}\right)\), where \(X_{i}\) are i.i.d uniform on \(U\) and \(\theta\) is the uniform probability measure on \(U\). Summation over \(q \leq r\) yields the desired result.

While making futile attempts in the direction of Theorem 4.9.2, arose further questions which we cannot answer. We describe one of these now. We recall the functionals \(\gamma_{\alpha, \beta}\) of (4.5) and the uniform measure \(\mu_{m}\) on \(T_{m}\).

Question 4.9.7 Is it true that for any metric space ( \(T, d\) ) the space \(U\) of 1-Lipschitz maps \(f\) from \(T_{m}\) to \(T\), provided with the distance \(D\) given by \(D\left(f, f^{\prime}\right)^{2}=\int_{T_{m}} d\left(f(s), f^{\prime}(s)\right)^{2} \mathrm{~d} \mu_{m}(s)\) satisfies \(\gamma_{2}(U, D) \leq L m^{3 / 4} \gamma_{1,2}(T)\) ?

The motivation for this result is that if \(T\) is the set of Lipschitz functions on \([0,1]\) then \(\gamma_{1,2}(T) \leq L\) (using Fourier transform to compare with an ellipsoids) and with minimal effort this would provide an alternate and more conceptual proof for the upper bound of Theorem 4.9.2.

Exercise 4.9.8. In the setting of Question 4.9.7 assume that \(e_{n}(T, d) \leq\) \(2^{-n}\). Prove that \(e_{2 n}(U, D) \leq L 2^{-n}\) (and better if \(n>m\) ). Conclude that \(\sum_{n \geq 0} 2^{n / 2} e_{n}(U, D) \leq L m\). Prove that \(\gamma_{2}(U, D) \leq L m \gamma_{1,2}(T)\).

\section*{Key ideas to remember}
- Ellipsoids in a Hilbert space are in a sense smaller than their entropy numbers indicate. This is true more generally for sufficiently convex sets in a Banach space. This phenomenon explains the fractional powers of logarithms occurring in the most famous matching theorems.
- The size of ellipsoids in sometimes accurately described by using proper generalizations \(\gamma_{\alpha, \beta}(T, d)\) of the basic functional \(\gamma_{2}(T, d)\).
- Matching theorems are typically proved through a discrepancy bound, which evaluates the supremum of the empirical process over a class \(\mathcal{F}\) of functions.
- Bernstein's inequality is a convenient tool to prove discrepancy bounds. It involves the control of \(\mathcal{F}\) both for the \(L^{2}\) and the supremum distance.
- Using two different distances reveals the power of approaching chaining through sequences of partitions.

\subsection*{4.10 Notes and Comments}

The original proof of the Leighton-Shor theorem amounts basically to perform by hand a kind of generic chaining in this highly non-trivial case, an incredible tour de force. \({ }^{23}\) A first attempt was made in [124] to relate (an important consequence of) the Leighton-Shor theorem to general methods for bounding stochastic processes, but runs into technical complications. Coffman and Shor [36] then introduced the use of Fourier transforms and brought to light the role of ellipsoids, after which it became clear that the structure

\footnotetext{
\({ }^{23}\) There is a simple explanation as to why this was possible: as you can check through Wikipedia, both authors are geniuses.
}
of these ellipsoids plays a central part in these matching results, a point of view systematically expounded in [153].

Chapter 17 is a continuation of the present chapter. The more difficult material it contains is presented later for fear of scaring readers at this early stage. A notable feature of the result presented there is that ellipsoids do not suffice, a considerable source of complication. The material of Appendix A is closely related to the Leighton-Shor theorem.

The original results of [5] are proved using an interesting technique called the transportation method. A version of this method, which avoids many of the technical difficulties of the original approach. is presented in [173]. With the notation of Theorem 4.5.1, it is proved in [173] (a stronger version of the fact) that with probability \(\geq 9 / 10\) one has
\[
\inf _{\pi} \frac{1}{N} \sum_{i \leq N} \exp \left(\frac{N d\left(X_{i}, Y_{\pi(i)}\right)^{2}}{L \log N}\right) \leq 2
\]

Since \(\exp x \geq x\), (4.131) implies that \(\sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right)^{2} \leq L \log N\) and hence using the Cauchy-Schwarz inequality \(\sum_{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \leq L \sqrt{N \log N}\). Moreover (4.131) also implies \(\max _{i \leq N} d\left(X_{i}, Y_{\pi(i)}\right) \leq L \log N / \sqrt{N}\). This unfortunately fails to bring a positive answer to Question 4.9.1.

For results about matching for unbounded distributions, see the work of J. Yukich [186] as well as the non-standard results of [172].