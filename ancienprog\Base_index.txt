INDEX1 : 0, 1 
INDEX2 : A, B, C
INDEX3 : <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TIE
INDEX5 : 

0_A_BANKER	
0_B_BANKER	
0_C_BANKER	
1_A_BANKER
1_B_BANKER
1_C_BANKER

0_A_PLAYER	
0_B_PLAYER	
0_C_PLAYER	
1_A_PLAYER
1_B_PLAYER
1_C_PLAYER

0_A_TIE		
0_B_TIE		
0_C_TIE		
1_A_TIE
1_B_TIE
1_C_TIE

INDEX6 :

0_A = M
0_B = N
0_C = O
1_A = S
1_B = T
1_C = U

INDEX7 = INDEX6_INDEX3 soit :

M_BANKER
N_BANKER
O_BANKER
S_BANKER
T_BANKER
U_BANKER

M_PLAYER
N_PLAYER
O_PLAYER
S_PLAYER
T_PLAYER
U_PLAYER

M_TIE
N_TIE
O_TIE
S_TIE
T_TIE
U_TIE

- Nous savons que : 

Si INDEX1 = 0 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 1
Si INDEX1 = 1 à la main n avec INDEX2 = C à la main n, alors à la main n+1 : INDEX1 sera égal à 0

Si INDEX1 = 0 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 0
Si INDEX1 = 1 à la main n avec INDEX2 = A à la main n, alors à la main n+1 : INDEX1 sera égal à 1

Si INDEX1 = 0 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 0
Si INDEX1 = 1 à la main n avec INDEX2 = B à la main n, alors à la main n+1 : INDEX1 sera égal à 1
