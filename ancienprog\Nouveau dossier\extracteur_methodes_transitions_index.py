#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXTRACTEUR DE MÉTHODES - RAPPORT TRANSITIONS INDEX
==================================================

Extracteur qui copie toutes les méthodes impliquées dans la création du rapport
rapport_statistique_avance_temp_transitions_index.txt dans un fichier texte.

Auteur: Système d'analyse baccarat INDEX
Date: 2025-07-05
"""

import os
import re
from datetime import datetime
from typing import List, Dict, Tuple

class ExtracteurMethodesTransitionsIndex:
    """Extracteur de toutes les méthodes impliquées dans le rapport transitions INDEX"""
    
    def __init__(self):
        """Initialisation de l'extracteur"""
        self.methodes_a_extraire = [
            # Méthodes dans analyseur_transitions_index.py
            ("analyseur_transitions_index.py", "__init__"),
            ("analyseur_transitions_index.py", "charger_donnees"),
            ("analyseur_transitions_index.py", "construire_index5"),
            ("analyseur_transitions_index.py", "construire_index6"),
            ("analyseur_transitions_index.py", "construire_index7"),
            ("analyseur_transitions_index.py", "construire_index1_index2"),
            ("analyseur_transitions_index.py", "analyser_transitions_par_position"),
            ("analyseur_transitions_index.py", "generer_rapport_transitions"),
            ("analyseur_transitions_index.py", "integrer_analyse_transitions"),
            
            # Méthodes dans analyseur_statistique_avance.py
            ("analyseur_statistique_avance.py", "main"),
            
            # Méthodes dans analyseur_biais_systematique.py
            ("analyseur_biais_systematique.py", "safe_print"),
            ("analyseur_biais_systematique.py", "creer_dict_imbrique"),
            ("analyseur_biais_systematique.py", "__init__"),
            ("analyseur_biais_systematique.py", "charger_donnees"),
            ("analyseur_biais_systematique.py", "_charger_donnees_cache_10gb"),
            ("analyseur_biais_systematique.py", "filtrer_mains_valides"),
            ("analyseur_biais_systematique.py", "analyser_transitions_index1"),
            ("analyseur_biais_systematique.py", "analyser_correlations_index2_index3"),
            ("analyseur_biais_systematique.py", "analyser_sequences_index1"),
            ("analyseur_biais_systematique.py", "analyser_biais_par_sous_categories"),
            ("analyseur_biais_systematique.py", "analyser_effet_etat_initial"),
            ("analyseur_biais_systematique.py", "calculer_probabilites_theoriques"),
            ("analyseur_biais_systematique.py", "analyser_transitions_sequentielles"),
            ("analyseur_biais_systematique.py", "analyser_toutes_combinaisons_sequentielles"),
            ("analyseur_biais_systematique.py", "analyser_patterns_significatifs"),
            ("analyseur_biais_systematique.py", "generer_rapport_complet"),
            ("analyseur_biais_systematique.py", "calculer_statistiques_avancees"),
            ("analyseur_biais_systematique.py", "analyser_statistiques_avancees_patterns"),
            ("analyseur_biais_systematique.py", "test_significativite_statistique_patterns"),
            ("analyseur_biais_systematique.py", "filtrer_mains_valides_statique"),
            ("analyseur_biais_systematique.py", "extraire_donnees_par_positions"),
            ("analyseur_biais_systematique.py", "analyser_position_transitions"),
            ("analyseur_biais_systematique.py", "analyser_position_correlations"),
            ("analyseur_biais_systematique.py", "analyser_position_sequences"),
            ("analyseur_biais_systematique.py", "analyser_position_combinaisons_sequentielles"),
            ("analyseur_biais_systematique.py", "analyser_position_sous_categories"),
            ("analyseur_biais_systematique.py", "analyser_position_etat_initial"),
            ("analyseur_biais_systematique.py", "analyser_position_probabilites"),
            ("analyseur_biais_systematique.py", "analyser_position_transitions_sequentielles"),
            ("analyseur_biais_systematique.py", "analyser_position_pattern_specifique"),
            ("analyseur_biais_systematique.py", "analyser_position_statistiques_avancees")
        ]
        
        self.fichiers_source = [
            "analyseur_transitions_index.py",
            "analyseur_statistique_avance.py", 
            "analyseur_biais_systematique.py"
        ]
    
    def lire_fichier(self, nom_fichier: str) -> str:
        """Lit le contenu d'un fichier source"""
        try:
            with open(nom_fichier, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ Erreur lecture {nom_fichier}: {e}")
            return ""
    
    def extraire_methode(self, contenu_fichier: str, nom_methode: str) -> str:
        """Extrait une méthode spécifique du contenu d'un fichier"""
        lignes = contenu_fichier.split('\n')
        methode_lignes = []
        dans_methode = False
        indentation_methode = 0
        
        for i, ligne in enumerate(lignes):
            # Chercher le début de la méthode
            if not dans_methode:
                if (f"def {nom_methode}(" in ligne or 
                    f"class {nom_methode}" in ligne or
                    (nom_methode == "__init__" and "def __init__(" in ligne)):
                    dans_methode = True
                    indentation_methode = len(ligne) - len(ligne.lstrip())
                    methode_lignes.append(ligne)
            else:
                # Dans la méthode
                if ligne.strip() == "":
                    # Ligne vide - toujours inclure
                    methode_lignes.append(ligne)
                else:
                    indentation_courante = len(ligne) - len(ligne.lstrip())
                    
                    # Si l'indentation est <= à celle de la méthode et ce n'est pas un commentaire/docstring
                    if (indentation_courante <= indentation_methode and 
                        not ligne.strip().startswith('#') and 
                        not ligne.strip().startswith('"""') and
                        not ligne.strip().startswith("'''") and
                        ligne.strip() != '"""' and
                        ligne.strip() != "'''"):
                        # Fin de la méthode
                        break
                    else:
                        methode_lignes.append(ligne)
        
        return '\n'.join(methode_lignes) if methode_lignes else f"# MÉTHODE {nom_methode} NON TROUVÉE"
    
    def extraire_toutes_methodes(self) -> str:
        """Extrait toutes les méthodes et les compile dans un texte"""
        contenu_final = []
        contenu_final.append("=" * 120)
        contenu_final.append("🔍 EXTRACTION COMPLÈTE DES MÉTHODES - RAPPORT TRANSITIONS INDEX")
        contenu_final.append("=" * 120)
        contenu_final.append(f"📅 Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        contenu_final.append(f"📊 Total méthodes extraites : {len(self.methodes_a_extraire)}")
        contenu_final.append("=" * 120)
        contenu_final.append("")
        
        # Charger le contenu de tous les fichiers
        contenus_fichiers = {}
        for fichier in self.fichiers_source:
            if os.path.exists(fichier):
                contenus_fichiers[fichier] = self.lire_fichier(fichier)
                print(f"✅ Fichier chargé : {fichier}")
            else:
                print(f"❌ Fichier non trouvé : {fichier}")
        
        # Extraire chaque méthode
        for i, (nom_fichier, nom_methode) in enumerate(self.methodes_a_extraire, 1):
            contenu_final.append(f"📍 MÉTHODE {i:2d}/{len(self.methodes_a_extraire)} : {nom_methode}")
            contenu_final.append(f"📄 Fichier source : {nom_fichier}")
            contenu_final.append("-" * 80)
            
            if nom_fichier in contenus_fichiers:
                methode_code = self.extraire_methode(contenus_fichiers[nom_fichier], nom_methode)
                contenu_final.append(methode_code)
            else:
                contenu_final.append(f"# ERREUR : Fichier {nom_fichier} non disponible")
            
            contenu_final.append("")
            contenu_final.append("=" * 80)
            contenu_final.append("")
        
        contenu_final.append("🎯 FIN DE L'EXTRACTION - TOUTES LES MÉTHODES COMPILÉES")
        contenu_final.append("=" * 120)
        
        return '\n'.join(contenu_final)
    
    def generer_fichier_extraction(self, nom_fichier_sortie: str = "methodes_transitions_index_completes.txt"):
        """Génère le fichier d'extraction complet"""
        print(f"🚀 DÉMARRAGE DE L'EXTRACTION DES MÉTHODES")
        print("=" * 80)
        
        contenu_complet = self.extraire_toutes_methodes()
        
        try:
            with open(nom_fichier_sortie, 'w', encoding='utf-8') as f:
                f.write(contenu_complet)
            print(f"✅ Extraction terminée : {nom_fichier_sortie}")
            print(f"📊 Taille du fichier : {len(contenu_complet):,} caractères")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde : {e}")

def main():
    """Fonction principale d'extraction"""
    extracteur = ExtracteurMethodesTransitionsIndex()
    extracteur.generer_fichier_extraction()

if __name__ == "__main__":
    main()
