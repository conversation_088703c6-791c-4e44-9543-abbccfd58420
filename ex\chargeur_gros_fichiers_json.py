#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CHARGEUR HAUTE PERFORMANCE POUR GROS FICHIERS JSON
==================================================

Basé sur l'analyse technique des programmes existants :
- analyseur_proportions_index5.py
- analyseur_biais_systematique.py  
- analyseur_statistique_avance.py
- lancer_analyse_complete.py
- analyseur_transitions_index.py

Optimisé pour traiter des fichiers JSON de 9+ GB avec :
- Cache 10GB RAM
- Buffer 1GB optimisé (vitesse doublée)
- Gestion mémoire avancée
- Correction automatique des formats JSON mal formés
- Optimisation pour 8 cœurs CPU + 28GB RAM

Auteur : Système d'analyse Baccarat
Date : 2025-01-05
"""

import json
import os
import sys
import gc
import psutil
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
import traceback
import re


class ChargeurGrossFichiersJSON:
    """
    Chargeur haute performance pour fichiers JSON volumineux (9+ GB)
    
    Techniques utilisées :
    - Cache 10GB RAM avec optimisations système
    - Buffer 1GB pour lecture optimisée (vitesse doublée)
    - Garbage collection contrôlé
    - Détection et correction automatique des formats
    - Gestion des erreurs mémoire
    - Progression en temps réel
    """
    
    def __init__(self):
        """Initialise le chargeur avec les paramètres optimaux"""
        self.donnees = None
        self.total_parties = 0
        self.total_mains = 0
        self.mains_valides = 0
        
        # Paramètres techniques optimisés (basés sur l'analyse des programmes existants)
        self.SEUIL_CACHE_10GB = 5.0  # GB - Seuil pour activer le cache haute performance
        self.BUFFER_SIZE = 1024 * 1024 * 1024  # 1GB - Buffer optimisé pour lecture (doublé)
        self.CHUNK_SIZE_PROGRESSION = 200 * 1024 * 1024  # 200MB - Chunks pour progression (optimisé)

        print("🚀 CHARGEUR HAUTE PERFORMANCE POUR GROS FICHIERS JSON")
        print("=" * 70)
        print(f"📊 Optimisé pour fichiers 9+ GB")
        print(f"🧠 Cache haute performance : 10GB RAM")
        print(f"⚡ Buffer optimisé : {self.BUFFER_SIZE // (1024*1024)}MB")
        
    def verifier_ressources_systeme(self) -> tuple[bool, bool, bool]:
        """
        Vérifie les ressources système disponibles
        Basé sur lancer_analyse_complete.py
        """
        print("\n🔍 VÉRIFICATION DES RESSOURCES SYSTÈME")
        print("-" * 50)
        
        # RAM disponible
        ram_total = psutil.virtual_memory().total / (1024**3)
        ram_disponible = psutil.virtual_memory().available / (1024**3)
        
        print(f"💾 RAM totale : {ram_total:.1f} GB")
        print(f"💾 RAM disponible : {ram_disponible:.1f} GB")
        
        # CPU
        nb_coeurs = psutil.cpu_count()
        print(f"🖥️ Nombre de cœurs CPU : {nb_coeurs}")
        
        # Espace disque
        espace_libre = psutil.disk_usage('.').free / (1024**3)
        print(f"💽 Espace disque libre : {espace_libre:.1f} GB")
        
        # Vérifications pour gros fichiers
        warnings = []
        if ram_disponible < 15:
            warnings.append(f"⚠️ RAM disponible faible ({ram_disponible:.1f} GB < 15 GB recommandés)")
        
        if nb_coeurs < 4:
            warnings.append(f"⚠️ Nombre de cœurs faible ({nb_coeurs} < 4 recommandés)")
        
        if espace_libre < 5:
            warnings.append(f"⚠️ Espace disque faible ({espace_libre:.1f} GB < 5 GB recommandés)")
        
        if warnings:
            print("\n⚠️ AVERTISSEMENTS :")
            for warning in warnings:
                print(f"   {warning}")
        else:
            print("\n✅ Ressources système suffisantes pour le traitement haute performance")
        
        return ram_disponible >= 15, nb_coeurs >= 2, espace_libre >= 2
    
    def verifier_fichier(self, filename: str) -> tuple[bool, float]:
        """
        Vérifie l'existence et la taille du fichier
        Basé sur lancer_analyse_complete.py
        """
        print(f"\n🔍 VÉRIFICATION DU FICHIER : {filename}")
        print("-" * 50)
        
        if not os.path.exists(filename):
            print(f"❌ Fichier non trouvé : {filename}")
            return False, 0.0
        
        file_size = os.path.getsize(filename)
        file_size_gb = file_size / (1024**3)
        print(f"📏 Taille du fichier : {file_size_gb:.2f} GB")
        
        if file_size_gb < 0.1:
            print("⚠️ Fichier très petit (< 0.1 GB)")
        elif file_size_gb > 20:
            print("⚠️ Fichier très volumineux (> 20 GB) - Temps de traitement élevé")
        
        return True, file_size_gb
    
    def charger_fichier(self, filename: str, callback_traitement: Optional[Callable] = None) -> bool:
        """
        Charge un gros fichier JSON avec la méthode optimale
        
        Args:
            filename: Chemin vers le fichier JSON
            callback_traitement: Fonction optionnelle pour traiter chaque partie
            
        Returns:
            bool: True si chargement réussi
        """
        # Vérifications préliminaires
        ram_ok, cpu_ok, disk_ok = self.verifier_ressources_systeme()
        fichier_ok, file_size_gb = self.verifier_fichier(filename)
        
        if not fichier_ok:
            return False
        
        if not ram_ok:
            print("\n⚠️ RAM insuffisante détectée. Le chargement pourrait échouer.")
            reponse = input("Continuer quand même ? (o/N) : ")
            if reponse.lower() not in ['o', 'oui', 'y', 'yes']:
                print("❌ Chargement annulé par l'utilisateur")
                return False
        
        print(f"\n🚀 DÉBUT DU CHARGEMENT HAUTE PERFORMANCE")
        print("=" * 70)
        print(f"📂 Fichier : {filename}")
        print(f"📏 Taille : {file_size_gb:.2f} GB")
        print(f"⏰ Heure de début : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        debut_chargement = datetime.now()
        
        try:
            # Choix de la méthode selon la taille du fichier
            if file_size_gb > self.SEUIL_CACHE_10GB:
                print(f"🚀 Fichier volumineux détecté - Chargement cache 10GB RAM")
                success = self._charger_cache_10gb(filename, callback_traitement)
            else:
                print(f"📖 Chargement standard")
                success = self._charger_standard(filename, callback_traitement)
            
            fin_chargement = datetime.now()
            duree = fin_chargement - debut_chargement
            
            if success:
                print(f"\n✅ CHARGEMENT TERMINÉ AVEC SUCCÈS !")
                print(f"⏱️ Durée totale : {duree}")
                print(f"📊 Parties traitées : {self.total_parties:,}")
                if self.total_mains > 0:
                    print(f"📊 Mains analysées : {self.total_mains:,}")
                    print(f"📊 Mains valides : {self.mains_valides:,}")
            else:
                print(f"\n❌ ÉCHEC DU CHARGEMENT")
                print(f"⏱️ Durée avant échec : {duree}")
            
            return success
            
        except Exception as e:
            print(f"\n❌ ERREUR CRITIQUE LORS DU CHARGEMENT : {e}")
            print(f"🔍 Traceback : {traceback.format_exc()}")
            return False
    
    def _charger_standard(self, filename: str, callback_traitement: Optional[Callable] = None) -> bool:
        """
        Chargement standard pour fichiers < 5GB
        Basé sur analyseur_biais_systematique.py
        """
        try:
            print("📖 Lecture du fichier en mode standard...")
            
            with open(filename, 'r', encoding='utf-8') as f:
                self.donnees = json.load(f)
            
            # Traitement des données
            if 'parties_condensees' in self.donnees:
                parties = self.donnees['parties_condensees']
                print(f"📊 Format détecté : Condensé")
                print(f"✅ Dataset chargé : {len(parties):,} parties")
                self.total_parties = len(parties)
                
                # Appliquer le callback si fourni
                if callback_traitement:
                    for partie in parties:
                        callback_traitement(partie)
                        
            elif 'parties' in self.donnees:
                parties = self.donnees['parties']
                print(f"📊 Format détecté : Standard")
                print(f"✅ Dataset chargé : {len(parties):,} parties")
                self.total_parties = len(parties)
                
                # Appliquer le callback si fourni
                if callback_traitement:
                    for partie in parties:
                        callback_traitement(partie)
            else:
                print("❌ Format de dataset non reconnu")
                print(f"🔑 Clés disponibles : {list(self.donnees.keys())}")
                return False
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON : {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement standard : {e}")
            return False

    def _charger_cache_10gb(self, filename: str, callback_traitement: Optional[Callable] = None) -> bool:
        """
        Chargement haute performance avec allocation de 10GB de RAM
        Basé sur analyseur_proportions_index5.py et analyseur_biais_systematique.py
        Optimisé pour les très gros fichiers JSON (9-12GB)
        """
        try:
            print("🧠 Allocation de 10GB de RAM pour le cache...")

            # Optimisations système pour performances maximales
            os.environ['PYTHONHASHSEED'] = '0'  # Hash déterministe

            # Forcer le garbage collection avant le chargement
            gc.collect()
            gc.disable()  # Désactiver GC pendant le chargement pour plus de vitesse

            # Lire tout le fichier en une fois avec un buffer optimisé
            print("📖 Lecture complète du fichier en mémoire...")

            # Obtenir la taille du fichier pour la barre de progression
            file_size = os.path.getsize(filename)
            start_time = datetime.now()

            with open(filename, 'r', encoding='utf-8', buffering=self.BUFFER_SIZE) as f:
                print("⚡ Chargement du contenu complet...")
                print("📊 Progression du chargement :")

                # Lire par chunks pour afficher la progression
                content = ""
                bytes_read = 0

                while True:
                    chunk = f.read(self.CHUNK_SIZE_PROGRESSION)
                    if not chunk:
                        break
                    content += chunk
                    bytes_read += len(chunk.encode('utf-8'))

                    # Afficher progression avec temps écoulé
                    progress = min(100, (bytes_read / file_size) * 100)
                    elapsed = datetime.now() - start_time
                    elapsed_str = str(elapsed).split('.')[0]  # Enlever les microsecondes
                    print(f"   📈 {progress:.1f}% chargé ({bytes_read:,} / {file_size:,} octets) - ⏱️ {elapsed_str}", end='\r')

                print()  # Nouvelle ligne après progression

            print(f"✅ Fichier chargé en mémoire : {len(content):,} caractères")

            # Analyser le début du contenu pour détecter le format
            debut_content = content[:200]
            print(f"🔍 Début du fichier : {debut_content[:100]}...")

            # Détecter et corriger le format si nécessaire
            if content.startswith(',{"partie_number"'):
                print("🔧 Correction du format JSON mal formé (commence par virgule)...")
                if content.endswith(']}'):
                    content = content[:-2]  # Enlever ]}
                    print("🔧 Suppression du ]} final en trop")
                # Enlever la virgule du début et ajouter la structure JSON correcte
                content = '{"parties_condensees": [' + content[1:] + ']}'
                print("✅ Format JSON corrigé")

            elif content.startswith('{"partie_number"'):
                print("🔧 Correction du format JSON mal formé (manque structure)...")
                # Ajouter la structure JSON correcte
                content = '{"parties_condensees": [' + content + ']}'
                print("✅ Format JSON corrigé")

            elif '"parties_condensees":' in debut_content:
                print("📊 Format JSON correct détecté")

            elif '"parties":' in debut_content:
                print("📊 Format JSON standard détecté")

            else:
                print("⚠️ Format JSON non standard détecté - Tentative de parsing direct")

            # Parser le JSON avec le contenu corrigé
            print("🔄 Parsing JSON haute performance...")
            try:
                data = json.loads(content)
                print("✅ JSON parsé avec succès")
            except json.JSONDecodeError as e:
                print(f"❌ Erreur de parsing JSON : {e}")
                print(f"📍 Position : ligne {e.lineno}, colonne {e.colno}")
                # Afficher le contexte de l'erreur
                lines = content.split('\n')
                if e.lineno <= len(lines):
                    error_line = lines[e.lineno - 1] if e.lineno > 0 else "N/A"
                    print(f"🔍 Ligne d'erreur : {error_line[:100]}")
                return False

            # Libérer la mémoire du contenu brut
            del content
            gc.enable()  # Réactiver GC après chargement
            gc.collect()

            # Traiter les données
            if 'parties_condensees' in data:
                parties = data['parties_condensees']
                print("📊 Format détecté : Condensé (cache 10GB)")
                print(f"📊 Métadonnées : Format condensé sans métadonnées détaillées")
            elif 'parties' in data:
                parties = data['parties']
                print("📊 Format détecté : Standard (cache 10GB)")
            else:
                print("❌ Structure JSON invalide après correction")
                print(f"🔑 Clés disponibles : {list(data.keys())}")
                return False

            # Stocker les données
            self.donnees = data
            self.total_parties = len(parties)

            print(f"✅ Dataset chargé avec succès en mode cache 10GB !")
            print(f"   • Total parties traitées : {len(parties):,}")

            # Appliquer le callback si fourni
            if callback_traitement:
                print("🔄 Application du traitement personnalisé...")
                for i, partie in enumerate(parties):
                    callback_traitement(partie)
                    if (i + 1) % 100000 == 0:
                        print(f"   📊 Parties traitées : {i + 1:,}")

            return True

        except MemoryError:
            print("❌ Erreur : Mémoire insuffisante pour le cache 10GB")
            print("💡 Suggestion : Fermer d'autres applications ou augmenter la RAM")
            return False
        except Exception as e:
            print(f"❌ Erreur lors du chargement cache 10GB : {e}")
            print(f"🔍 Traceback : {traceback.format_exc()}")
            return False
        finally:
            # S'assurer que le GC est réactivé
            gc.enable()

    def obtenir_donnees(self) -> Optional[Dict]:
        """Retourne les données chargées"""
        return self.donnees

    def obtenir_statistiques(self) -> Dict[str, int]:
        """Retourne les statistiques de chargement"""
        return {
            'total_parties': self.total_parties,
            'total_mains': self.total_mains,
            'mains_valides': self.mains_valides
        }


def exemple_callback_traitement(partie: Dict):
    """
    Exemple de fonction de traitement personnalisé pour chaque partie

    Args:
        partie: Dictionnaire représentant une partie du dataset
    """
    # Exemple : compter les mains dans chaque partie
    if 'mains_condensees' in partie:
        mains = partie['mains_condensees']
        print(f"Partie {partie.get('partie_number', '?')} : {len(mains)} mains")
    elif 'mains' in partie:
        mains = partie['mains']
        print(f"Partie {partie.get('partie_number', '?')} : {len(mains)} mains")


def main():
    """
    Fonction principale pour tester le chargeur
    Usage: python chargeur_gros_fichiers_json.py <fichier.json>
    """
    if len(sys.argv) != 2:
        print("Usage: python chargeur_gros_fichiers_json.py <fichier_dataset.json>")
        print("\nExemple :")
        print("python chargeur_gros_fichiers_json.py dataset_baccarat_lupasco_20250704_170242_condensed.json")
        sys.exit(1)

    filename = sys.argv[1]

    # Créer le chargeur
    chargeur = ChargeurGrossFichiersJSON()

    # Charger le fichier avec callback optionnel
    # success = chargeur.charger_fichier(filename, exemple_callback_traitement)
    success = chargeur.charger_fichier(filename)

    if success:
        print("\n🎉 CHARGEMENT RÉUSSI !")

        # Afficher les statistiques
        stats = chargeur.obtenir_statistiques()
        print(f"📊 Statistiques finales :")
        for key, value in stats.items():
            print(f"   • {key}: {value:,}")

        # Accéder aux données si nécessaire
        donnees = chargeur.obtenir_donnees()
        if donnees:
            print(f"🔑 Clés disponibles dans les données : {list(donnees.keys())}")

            # Exemple d'utilisation des données
            if 'parties_condensees' in donnees:
                parties = donnees['parties_condensees']
                print(f"📈 Première partie : {parties[0].get('partie_number', 'N/A')}")
                print(f"📈 Dernière partie : {parties[-1].get('partie_number', 'N/A')}")
    else:
        print("\n❌ ÉCHEC DU CHARGEMENT")
        sys.exit(1)


if __name__ == "__main__":
    main()
